# Question Service - API Implementation Specification

## Overview

This document specifies the exact API endpoints that need to be implemented in the **question-section-service** to replace frontend mocks and add missing functionality.

**Service Location**: `/Users/<USER>/Developer/kla/question/question-section-service`
**Base Package**: `in.enpk.question`
**Current Port**: 8081 (based on existing configuration)
**Base URL**: `/api`

## Implementation Priority

### High Priority (Week 1-2)
1. Starred Questions Balloting API
2. Unstarred Questions Balloting API
3. Notice Detail API with Answers/Attachments

### Medium Priority (Week 3-4)
4. Section Staff Notice APIs
5. Document Management APIs
6. Other Notices APIs

### Lower Priority (Week 5+)
7. Report Generation APIs
8. Explanatory Note APIs

---

## High Priority APIs

### 1. Starred Questions Balloting API

**Current Mock**: `GET /api/documents-mockStarred?date={yyyy-MM-dd}`
**Current Response**:
```json
{
  "id": "1234",
  "type": "SETTING_OF_STARRED_QUESTIONS", 
  "status": "pending",
  "currentNumber": "123",
  "assembly": 14,
  "session": 20,
  "ministerDesignationGroupId": "abcd-1234-efgh-5678",
  "groupName": "Group 1",
  "groupNameInLocal": "ഗ്രൂപ്പ് 1",
  "order": 1,
  "groupEntries": [
    {
      "id": "entry-001",
      "designationGroupId": "group-xyz",
      "designationId": 5,
      "ministerDesignation": "Minister for Education",
      "designationInLocal": "വിദ്യാഭ്യാസ മന്ത്രി",
      "ministerId": 101,
      "order": 1,
      "politicalPartyId": 101,
      "politicalPartyName": "Democratic Progressive Party",
      "politicalPartyNameInLocal": "ഡെമോക്രാറ്റിക് പ്രോഗ്രസീവ് പാർട്ടി"
    }
  ],
  "pendingNoticesData": [
    {
      "id": 1,
      "noticeNumber": "1009",
      "clubbed": false,
      "noticeHeading": "കെടിട സമുച്ചയങ്ങളിലെ പാർക്കിംഗ് ഇല്ലായ്മക്ക്",
      "designation": "Chief Minister",
      "portfolio": "GAD",
      "currentlyWith": {
        "name": "Sreejith Sivaraman",
        "designation": "Assistant"
      }
    }
  ],
  "createdAt": "2025-04-07T10:00:00Z",
  "lastModifiedAt": "2025-04-07T12:30:00Z",
  "createdBy": "admin_user_01",
  "lastModifiedBy": "editor_user_03"
}
```

**Used By**:
- `src/pages/listing/components/starred/index.jsx`
- `src/services/starred-questions.js`
- Balloting system for starred questions by date

**NEW Implementation**:

**Controller**: `StarredQuestionsBallotingController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/starred-questions-balloting")
@RequiredArgsConstructor
@Slf4j
public class StarredQuestionsBallotingController {
    
    private final StarredQuestionsBallotingService starredQuestionsBallotingService;
    
    @GetMapping("/by-date")
    public ResponseEntity<StarredQuestionsBallotingResponse> getStarredQuestionsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session) {
        
        StarredQuestionsBallotingResponse response = 
            starredQuestionsBallotingService.getStarredQuestionsByDate(date, assembly, session);
        return ResponseEntity.ok(response);
    }
}
```

**Contract**: `StarredQuestionsBallotingResponse.java` (NEW FILE)
```java
@Data
@Builder
public class StarredQuestionsBallotingResponse {
    private String id;
    private String type;
    private String status;
    private String currentNumber;
    private Integer assembly;
    private Integer session;
    private UUID ministerDesignationGroupId;
    private String groupName;
    private String groupNameInLocal;
    private Integer order;
    private List<GroupEntryResponse> groupEntries;
    private List<PendingNoticeResponse> pendingNoticesData;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
}

@Data
@Builder
public class GroupEntryResponse {
    private String id;
    private String designationGroupId;
    private Long designationId;
    private String ministerDesignation;
    private String designationInLocal;
    private Long ministerId;
    private Integer order;
    private Long politicalPartyId;
    private String politicalPartyName;
    private String politicalPartyNameInLocal;
}

@Data
@Builder  
public class PendingNoticeResponse {
    private Long id;
    private String noticeNumber;
    private Boolean clubbed;
    private String noticeHeading;
    private String designation;
    private String portfolio;
    private CurrentlyWithResponse currentlyWith;
}

@Data
@Builder
public class CurrentlyWithResponse {
    private String name;
    private String designation;
}
```

**Service Method**:
```java
public StarredQuestionsBallotingResponse getStarredQuestionsByDate(
        LocalDate questionDate, Integer assembly, Integer session) {
    
    // Get or create starred questions setting document for the date
    StarredQuestionsSetting setting = starredQuestionsSettingRepository
        .findByQuestionDateAndAssemblyAndSession(questionDate, assembly, session)
        .orElseGet(() -> createDefaultStarredQuestionsSetting(questionDate, assembly, session));
    
    // Get pending notices for the date
    List<NoticeForQuestion> pendingNotices = noticeForQuestionRepository
        .findStarredNoticesByDateAndStatus(questionDate, Arrays.asList(
            DocumentStatus.APPROVED, DocumentStatus.PENDING_BALLOT));
    
    // Build response
    return StarredQuestionsBallotingResponse.builder()
        .id(setting.getId().toString())
        .type("SETTING_OF_STARRED_QUESTIONS")
        .status(setting.getStatus().name().toLowerCase())
        .currentNumber(setting.getCurrentNumber())
        .assembly(setting.getAssembly())
        .session(setting.getSession())
        .ministerDesignationGroupId(setting.getMinisterDesignationGroupId())
        .groupName(setting.getGroupName())
        .groupNameInLocal(setting.getGroupNameInLocal())
        .order(setting.getDisplayOrder())
        .groupEntries(mapToGroupEntries(setting.getGroupEntries()))
        .pendingNoticesData(mapToPendingNotices(pendingNotices))
        .createdAt(setting.getCreatedAt())
        .lastModifiedAt(setting.getLastModifiedAt())
        .createdBy(setting.getCreatedBy())
        .lastModifiedBy(setting.getLastModifiedBy())
        .build();
}
```

**Database Tables Required**:
```sql
-- Starred Questions Setting table
CREATE TABLE starred_questions_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_date DATE NOT NULL,
    assembly INTEGER NOT NULL,
    session INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    current_number VARCHAR(100),
    minister_designation_group_id UUID,
    group_name VARCHAR(255),
    group_name_in_local VARCHAR(255),
    display_order INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    UNIQUE(question_date, assembly, session)
);

-- Group entries for starred questions
CREATE TABLE starred_questions_group_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    starred_questions_setting_id UUID NOT NULL REFERENCES starred_questions_settings(id),
    designation_group_id VARCHAR(255),
    designation_id BIGINT,
    minister_designation VARCHAR(255),
    designation_in_local VARCHAR(255),
    minister_id BIGINT,
    display_order INTEGER,
    political_party_id BIGINT,
    political_party_name VARCHAR(255),
    political_party_name_in_local VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 2. Unstarred Questions Balloting API

**Current Mock**: `GET /api/documents-mockUnstarred?date={yyyy-MM-dd}`
**Current Response**: (Similar structure to starred but with `unstarredQuestions` array)

**NEW Implementation**:

**Controller**: `UnstarredQuestionsBallotingController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/unstarred-questions-balloting")
@RequiredArgsConstructor
@Slf4j
public class UnstarredQuestionsBallotingController {
    
    private final UnstarredQuestionsBallotingService unstarredQuestionsBallotingService;
    
    @GetMapping("/by-date")
    public ResponseEntity<UnstarredQuestionsBallotingResponse> getUnstarredQuestionsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session) {
        
        UnstarredQuestionsBallotingResponse response = 
            unstarredQuestionsBallotingService.getUnstarredQuestionsByDate(date, assembly, session);
        return ResponseEntity.ok(response);
    }
}
```

**Contract**: `UnstarredQuestionsBallotingResponse.java` (NEW FILE)
```java
@Data
@Builder
public class UnstarredQuestionsBallotingResponse {
    private String id;
    private String type;
    private String status;
    private String currentNumber;
    private Integer assembly;
    private Integer session;
    private UUID ministerDesignationGroupId;
    private String groupName;
    private String groupNameInLocal;
    private Integer order;
    private List<GroupEntryResponse> groupEntries;
    private List<UnstarredQuestionResponse> unstarredQuestions;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
}

@Data
@Builder
public class UnstarredQuestionResponse {
    private String id;
    private String noticeNumber;
    private Boolean clubbed;
    private String noticeHeading;
    private Long ministerId;
    private String ministerDesignationId;
    private String ministerDesignation;
    private String ministerDesignationInLocal;
    private Long portfolioId;
    private String portfolio;
    private String portfolioInLocal;
    private String questionNumber;
    private List<ClauseResponse> clauses;
    private CurrentlyWithResponse currentlyWith;
    private List<MemberResponse> members;
}

@Data
@Builder
public class ClauseResponse {
    private UUID id;
    private String content;
    private Integer order;
}

@Data
@Builder
public class MemberResponse {
    private Long memberId;
    private String memberDisplayName;
    private String memberDisplayNameInLocal;
    private String constituency;
    private String politicalParty;
}
```

**Database Tables Required**:
```sql
-- Unstarred Questions Setting table
CREATE TABLE unstarred_questions_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_date DATE NOT NULL,
    assembly INTEGER NOT NULL,
    session INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    current_number VARCHAR(100),
    minister_designation_group_id UUID,
    group_name VARCHAR(255),
    group_name_in_local VARCHAR(255),
    display_order INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255),
    UNIQUE(question_date, assembly, session)
);

-- Group entries for unstarred questions (similar to starred)
CREATE TABLE unstarred_questions_group_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unstarred_questions_setting_id UUID NOT NULL REFERENCES unstarred_questions_settings(id),
    designation_group_id VARCHAR(255),
    designation_id BIGINT,
    minister_designation VARCHAR(255),
    designation_in_local VARCHAR(255),
    minister_id BIGINT,
    display_order INTEGER,
    political_party_id BIGINT,
    political_party_name VARCHAR(255),
    political_party_name_in_local VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 3. Notice Detail API with Answers/Attachments

**Current Mock**: `GET /api/mock/notice-for-questions/{id}`
**Current Response**:
```json
{
  "id": "ab6a20ab-368d-47ee-a3cd-3d1849cf6348",
  "type": "NOTICE_FOR_QUESTION",
  "name": "NFQ-2024-014",
  "status": "DRAFT",
  "answerStatus": "INTERIM",
  "noticeNumber": "NFQ-2024-014",
  "assembly": 15,
  "session": 4,
  "questionDate": "2024-07-15",
  "ministerDesignationId": 3,
  "ministerDesignation": "Education Minister",
  "ministerDesignationInLocal": "വിദ്യാഭ്യാസ മന്ത്രി",
  "ministerId": 105,
  "portfolioId": 8,
  "portfolio": "General Education",
  "portfolioInLocal": "പൊതു വിദ്യാഭ്യാസ വകുപ്പ്",
  "source": "MLA",
  "subSubjectId": 22,
  "subSubject": "Higher Secondary Education",
  "subSubjectInLocal": "ഉയർന്നതര വിദ്യാഭ്യാസം",
  "noticePriority": "P1",
  "starred": true,
  "noticeHeading": "Status of school infrastructure development projects",
  "primaryMember": {
    "memberId": 201,
    "memberDisplayName": "Shri. Roji M John",
    "memberDisplayNameInLocal": "ശ്രീ. റോജി എം ജോൺ",
    "constituencyId": 301,
    "constituencyName": "Angamaly",
    "constituencyNameInLocal": "അങ്കമാലി",
    "constituencyNumber": "083",
    "politicalPartyId": 401,
    "politicalPartyName": "United Democratic Front",
    "politicalPartyNameInLocal": "യുണൈറ്റഡ് ഡെമോക്രാറ്റിക് ഫ്രണ്ട്"
  },
  "secondaryMembers": [
    {
      "memberId": 202,
      "memberDisplayName": "Shri. V. D. Satheesan",
      "memberDisplayNameInLocal": "ശ്രീ. വി. ഡി. സതീശൻ",
      "constituencyId": 302,
      "constituencyName": "Paravur",
      "constituencyNameInLocal": "പാറവൂർ",
      "constituencyNumber": "084",
      "politicalPartyId": 402,
      "politicalPartyName": "Indian National Congress",
      "politicalPartyNameInLocal": "ഇന്ത്യൻ നാഷണൽ കോൺഗ്രസ്സ്"
    }
  ],
  "clauses": [
    {
      "id": "clause-uuid-001",
      "content": "കേരളത്തിലെ പൊതു വിദ്യാലയങ്ങളിലെ അടിസ്ഥാന സൗകര്യ വികസന പദ്ധതി നിലവിൽ എന്ത് നിലയിലാണ്?",
      "order": 1,
      "answers": [
        {
          "id": "answer-uuid-001",
          "clauseId": "clause-uuid-001",
          "answerType": "INTERIM",
          "content": "അടിസ്ഥാന സൗകര്യ വികസന പദ്ധതികൾ പല സ്കൂളുകളിലും പുരോഗമിക്കുകയാണ്.",
          "attachments": [
            {
              "id": "attachment-uuid-001",
              "name": "infrastructure-progress.pdf",
              "contentType": "application/pdf",
              "fileUrl": "https://example.com/files/infrastructure-progress.pdf"
            }
          ]
        }
      ]
    }
  ],
  "questionDiscussedInAssembly": false,
  "delayStatement": {
    "id": "delay-uuid-001",
    "name": "delay-statement.pdf",
    "contentType": "application/pdf",
    "fileUrl": "https://example.com/files/delay-statement.pdf"
  },
  "ministerLetter": {
    "id": "letter-uuid-001",
    "name": "minister-letter.pdf",
    "contentType": "application/pdf",
    "fileUrl": "https://example.com/files/minister-letter.pdf"
  },
  "createdAt": "2024-05-15T10:30:00Z",
  "lastModifiedAt": "2024-05-20T14:45:00Z",
  "createdBy": "system",
  "lastModifiedBy": "admin"
}
```

**Used By**:
- Notice detail pages
- Question viewing components
- Answer management

**NEW Implementation**:

**Add to existing NoticeForQuestionController**:
```java
@GetMapping("/{id}/detailed-view")
public ResponseEntity<NoticeForQuestionDetailedResponse> getNoticeDetailedView(
        @PathVariable UUID id) {
    NoticeForQuestionDetailedResponse response = 
        noticeForQuestionService.getNoticeDetailedView(id);
    return ResponseEntity.ok(response);
}
```

**Contract**: `NoticeForQuestionDetailedResponse.java` (NEW FILE)
```java
@Data
@Builder
public class NoticeForQuestionDetailedResponse {
    private UUID id;
    private String type;
    private String name;
    private DocumentStatus status;
    private AnswerStatus answerStatus;
    private String noticeNumber;
    private Integer assembly;
    private Integer session;
    private LocalDate questionDate;
    private Long ministerDesignationId;
    private String ministerDesignation;
    private String ministerDesignationInLocal;
    private Long ministerId;
    private Long portfolioId;
    private String portfolio;
    private String portfolioInLocal;
    private NoticeSource source;
    private Long subSubjectId;
    private String subSubject;
    private String subSubjectInLocal;
    private NoticePriority noticePriority;
    private Boolean starred;
    private String noticeHeading;
    private PrimaryMemberResponse primaryMember;
    private List<SecondaryMemberResponse> secondaryMembers;
    private List<ClauseWithAnswersResponse> clauses;
    private Boolean questionDiscussedInAssembly;
    private FileAttachmentResponse delayStatement;
    private FileAttachmentResponse ministerLetter;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
}

@Data
@Builder
public class PrimaryMemberResponse {
    private Long memberId;
    private String memberDisplayName;
    private String memberDisplayNameInLocal;
    private Long constituencyId;
    private String constituencyName;
    private String constituencyNameInLocal;
    private String constituencyNumber;
    private Long politicalPartyId;
    private String politicalPartyName;
    private String politicalPartyNameInLocal;
}

@Data
@Builder
public class SecondaryMemberResponse {
    private Long memberId;
    private String memberDisplayName;
    private String memberDisplayNameInLocal;
    private Long constituencyId;
    private String constituencyName;
    private String constituencyNameInLocal;
    private String constituencyNumber;
    private Long politicalPartyId;
    private String politicalPartyName;
    private String politicalPartyNameInLocal;
}

@Data
@Builder
public class ClauseWithAnswersResponse {
    private UUID id;
    private String content;
    private Integer order;
    private List<AnswerWithAttachmentsResponse> answers;
}

@Data
@Builder
public class AnswerWithAttachmentsResponse {
    private UUID id;
    private UUID clauseId;
    private AnswerType answerType;
    private String content;
    private List<FileAttachmentResponse> attachments;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
}

@Data
@Builder
public class FileAttachmentResponse {
    private UUID id;
    private String name;
    private String contentType;
    private String fileUrl;
    private Long fileSize;
    private LocalDateTime uploadedAt;
}
```

---

## Medium Priority APIs

### 4. Section Staff Notice APIs

**Current Mocks**:
- `GET /api/section-notice-for-questions/all`
- `GET /api/section-notice-for-questions`

**NEW Implementation**:

**Controller**: `SectionStaffNoticeController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/section-notice-for-questions")
@RequiredArgsConstructor
@Slf4j
public class SectionStaffNoticeController {
    
    private final SectionStaffNoticeService sectionStaffNoticeService;
    
    @GetMapping("/all")
    public ResponseEntity<PageResponseContract<SectionNoticeListResponse>> getAllNotices(
            @RequestParam(required = false) DocumentStatus status,
            @RequestParam(required = false) Boolean starred,
            @RequestParam(required = false) NoticePriority priority,
            @RequestParam(required = false) LocalDate questionDate,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session,
            @RequestParam(required = false) String searchText,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        SectionNoticeSearchRequest searchRequest = SectionNoticeSearchRequest.builder()
            .status(status)
            .starred(starred)
            .priority(priority)
            .questionDate(questionDate)
            .assembly(assembly)
            .session(session)
            .searchText(searchText)
            .page(page)
            .size(size)
            .sortBy(sortBy)
            .sortDirection(sortDirection)
            .build();
        
        PageResponseContract<SectionNoticeListResponse> response = 
            sectionStaffNoticeService.getAllNotices(searchRequest);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    public ResponseEntity<PageResponseContract<SectionNoticeListResponse>> getActionRequiredNotices(
            @RequestParam(required = false) String actionType,
            @RequestParam(required = false) LocalDate questionDate,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "5") Integer size) {
        
        PageResponseContract<SectionNoticeListResponse> response = 
            sectionStaffNoticeService.getActionRequiredNotices(
                actionType, questionDate, assembly, session, page, size);
        return ResponseEntity.ok(response);
    }
}
```

**Contracts**:
```java
@Data
@Builder
public class SectionNoticeListResponse {
    private UUID id;
    private String noticeNumber;
    private List<SecondaryMemberResponse> secondaryMembers;
    private Boolean starred;
    private Boolean clubbed;
    private String noticeHeading;
    private String ministerDesignation;
    private String portfolio;
    private LocalDate questionDate;
    private String noticePriority;
    private DocumentStatus status;
    private Integer assembly;
    private Integer session;
}

@Data
@Builder
public class SectionNoticeSearchRequest {
    private DocumentStatus status;
    private Boolean starred;
    private NoticePriority priority;
    private LocalDate questionDate;
    private Integer assembly;
    private Integer session;
    private String searchText;
    private Integer page;
    private Integer size;
    private String sortBy;
    private String sortDirection;
}
```

---

### 5. Document Management APIs

**Current Mocks**:
- `POST /api/documents/draft`
- `GET /api/documents/{documentId}`
- `POST /api/documents/{documentId}/submit`

**NEW Implementation**:

**Controller**: `DocumentManagementController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/documents")
@RequiredArgsConstructor
@Slf4j
public class DocumentManagementController {
    
    private final DocumentManagementService documentManagementService;
    
    @PostMapping("/draft")
    public ResponseEntity<DocumentResponse> createDocumentDraft(
            @RequestBody CreateDocumentRequest request) {
        DocumentResponse response = documentManagementService.createDocumentDraft(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @GetMapping("/{documentId}")
    public ResponseEntity<DocumentResponse> getDocumentById(
            @PathVariable UUID documentId) {
        DocumentResponse response = documentManagementService.getDocumentById(documentId);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/{documentId}/submit")
    public ResponseEntity<DocumentResponse> submitDocument(
            @PathVariable UUID documentId,
            @RequestBody SubmitDocumentRequest request) {
        DocumentResponse response = documentManagementService.submitDocument(documentId, request);
        return ResponseEntity.ok(response);
    }
}
```

**Contracts**:
```java
@Data
@Builder
public class CreateDocumentRequest {
    private DocumentType type;
    private String name;
    private Integer assembly;
    private Integer session;
    private Object documentData; // Generic field for different document types
}

@Data
@Builder
public class SubmitDocumentRequest {
    private Integer assembly;
    private Integer session;
    private Object submissionData;
}

@Data
@Builder
public class DocumentResponse {
    private UUID id;
    private DocumentType type;
    private String name;
    private DocumentStatus status;
    private String currentNumber;
    private Integer assembly;
    private Integer session;
    private Object documentData;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
}
```

---

### 6. Other Notices APIs

**Current Mocks**:
- `GET /api/other-notices/notice-bank`
- `GET /api/other-notices/my-notices`
- `GET /api/notice-bank-for-questions/my-notices`

**NEW Implementation**:

**Controller**: `OtherNoticesController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/other-notices")
@RequiredArgsConstructor
@Slf4j
public class OtherNoticesController {
    
    private final OtherNoticesService otherNoticesService;
    
    @GetMapping("/notice-bank")
    public ResponseEntity<PageResponseContract<OtherNoticeResponse>> getNoticeBank(
            @RequestParam(required = false) String noticeType,
            @RequestParam(required = false) String ministerDesignation,
            @RequestParam(required = false) String portfolio,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        PageResponseContract<OtherNoticeResponse> response = 
            otherNoticesService.getNoticeBank(noticeType, ministerDesignation, 
                portfolio, assembly, session, page, size);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/my-notices")
    public ResponseEntity<PageResponseContract<OtherNoticeResponse>> getMyOtherNotices(
            @RequestParam(required = false) String noticeType,
            @RequestParam(required = false) DocumentStatus status,
            @RequestParam(required = false) LocalDate questionDate,
            @RequestParam(required = false) LocalDate createdDate,
            @RequestParam(required = false) Integer assembly,
            @RequestParam(required = false) Integer session,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        PageResponseContract<OtherNoticeResponse> response = 
            otherNoticesService.getMyOtherNotices(noticeType, status, questionDate, 
                createdDate, assembly, session, page, size);
        return ResponseEntity.ok(response);
    }
}
```

**Contracts**:
```java
@Data
@Builder
public class OtherNoticeResponse {
    private UUID id;
    private String currentNumber;
    private String noticeType;
    private String ministerDesignation;
    private String noticeHeading;
    private LocalDate questionDate;
    private LocalDate createdDate;
    private String ministerPortfolio;
    private DocumentStatus status;
    private Integer assembly;
    private Integer session;
}
```

---

## Lower Priority APIs

### 7. Report Generation APIs

**Current Mocks**: Various report endpoints with different mock paths
**Purpose**: Answer Status Reports, Late Answer Bulletins, Delay Statement Lists

**NEW Implementation**:

**Controller**: `ReportGenerationController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/reports")
@RequiredArgsConstructor
@Slf4j
public class ReportGenerationController {
    
    private final ReportGenerationService reportGenerationService;
    
    @PostMapping("/answer-status-report/draft")
    public ResponseEntity<ReportResponse> createAnswerStatusReportDraft(
            @RequestBody CreateAnswerStatusReportRequest request) {
        ReportResponse response = reportGenerationService.createAnswerStatusReportDraft(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/late-answer-bulletin/draft")  
    public ResponseEntity<ReportResponse> createLateAnswerBulletinDraft(
            @RequestBody CreateLateAnswerBulletinRequest request) {
        ReportResponse response = reportGenerationService.createLateAnswerBulletinDraft(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/delay-statement-list/draft")
    public ResponseEntity<ReportResponse> createDelayStatementListDraft(
            @RequestBody CreateDelayStatementListRequest request) {
        ReportResponse response = reportGenerationService.createDelayStatementListDraft(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
}
```

### 8. Explanatory Note API

**Current Mock**: `GET /api/explanatory-note-details`
**Purpose**: Template data for explanatory notes

**NEW Implementation**:

**Controller**: `ExplanatoryNoteController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/api/explanatory-note")
@RequiredArgsConstructor
@Slf4j
public class ExplanatoryNoteController {
    
    private final ExplanatoryNoteService explanatoryNoteService;
    
    @GetMapping("/details")
    public ResponseEntity<ExplanatoryNoteDetailsResponse> getExplanatoryNoteDetails(
            @RequestParam(required = false) String noticeType,
            @RequestParam(required = false) UUID noticeId) {
        
        ExplanatoryNoteDetailsResponse response = 
            explanatoryNoteService.getExplanatoryNoteDetails(noticeType, noticeId);
        return ResponseEntity.ok(response);
    }
}
```

**Contract**:
```java
@Data
@Builder
public class ExplanatoryNoteDetailsResponse {
    private ExplanatoryNoteData data;
}

@Data
@Builder
public class ExplanatoryNoteData {
    private String title;
    private String subTitle;
    private String noticeDate;
    private String subject;
    private String place;
    private String date;
    private String memberDisplayName;
    private String memberDisplayNameInLocal;
    private String constituencyName;
    private String constituencyNameInLocal;
    private String noticeHeading;
    private String clause;
}
```

---

## Database Schema Updates Required

### 1. Enhanced Notice Tables
```sql
-- Add answer status tracking to notices
ALTER TABLE notice_for_question ADD COLUMN answer_status VARCHAR(50) DEFAULT 'NOT_ANSWERED';
ALTER TABLE notice_for_question ADD COLUMN question_discussed_in_assembly BOOLEAN DEFAULT FALSE;

-- Add file attachments for notices
ALTER TABLE notice_for_question ADD COLUMN delay_statement_id UUID REFERENCES file_attachments(id);
ALTER TABLE notice_for_question ADD COLUMN minister_letter_id UUID REFERENCES file_attachments(id);
```

### 2. Answers and Attachments Tables
```sql
-- Answers table
CREATE TABLE answers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clause_id UUID NOT NULL REFERENCES notice_for_question_clauses(id),
    answer_type VARCHAR(50) NOT NULL,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255)
);

-- Answer attachments
CREATE TABLE answer_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    answer_id UUID NOT NULL REFERENCES answers(id),
    file_attachment_id UUID NOT NULL REFERENCES file_attachments(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Section Staff Tracking
```sql
-- Add section staff tracking fields
ALTER TABLE notice_for_question ADD COLUMN currently_with_user_id VARCHAR(255);
ALTER TABLE notice_for_question ADD COLUMN currently_with_designation VARCHAR(255);
ALTER TABLE notice_for_question ADD COLUMN section_status VARCHAR(50) DEFAULT 'NOT_ASSIGNED';
```

### 4. Other Notice Types Tables
```sql
-- Generic other notices table
CREATE TABLE other_notices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notice_type VARCHAR(100) NOT NULL,
    current_number VARCHAR(100),
    minister_designation VARCHAR(255),
    notice_heading TEXT,
    question_date DATE,
    minister_portfolio VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    assembly INTEGER,
    session INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255)
);
```

### 5. Reports Tables
```sql
-- Generic reports table
CREATE TABLE generated_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    current_number VARCHAR(100),
    assembly INTEGER,
    session INTEGER,
    report_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    last_modified_by VARCHAR(255)
);
```

---

## Standard Enums to Add

```java
public enum AnswerStatus {
    NOT_ANSWERED,
    INTERIM,
    FINAL,
    DELAYED
}

public enum AnswerType {
    INTERIM,
    FINAL
}

public enum SectionStatus {
    NOT_ASSIGNED,
    ASSIGNED,
    IN_PROGRESS,
    COMPLETED,
    RETURNED
}

public enum ReportType {
    ANSWER_STATUS_REPORT,
    LATE_ANSWER_BULLETIN,
    DELAY_STATEMENT_LIST,
    DELAYED_ANSWER_BULLETIN
}
```

---

## Implementation Notes

1. **Follow existing patterns** from current Question service controllers
2. **Use existing base classes** (`DocumentContract`, `PageResponseContract`)
3. **Maintain audit trail** using existing audit fields pattern
4. **Add proper validation** using Spring Validation annotations
5. **Use existing exception handling** (`GlobalExceptionHandler`, `EntityNotFoundException`)
6. **Follow existing naming conventions** for endpoints and fields
7. **Add comprehensive unit tests** following existing test patterns

---

## API Testing Strategy

1. **Update Bruno collection** in `api-tests/` folder
2. **Create integration tests** with @SpringBootTest
3. **Mock external dependencies** (MDM service calls)
4. **Test error scenarios** and edge cases
5. **Validate response contracts** match specifications

---

## Migration Strategy

1. **Phase 1**: Implement high priority APIs (balloting, notice details)
2. **Phase 2**: Implement medium priority APIs (section staff, documents)
3. **Phase 3**: Implement lower priority APIs (reports, explanatory notes)
4. **Phase 4**: Frontend integration and mock removal

Each phase should include:
- Backend implementation and testing
- Database schema updates
- Bruno API test creation
- Frontend service integration
- Mock handler removal

---

## Performance Considerations

1. **Add database indexes** for frequently queried fields (question_date, assembly, session, status)
2. **Implement caching** for frequently accessed data (active assembly/session)
3. **Use pagination** for all list endpoints
4. **Optimize N+1 queries** using JPA fetch joins
5. **Add connection pooling** configuration for database connections

---

## Security Considerations

1. **Add role-based access control** for section staff endpoints
2. **Validate user permissions** for document access
3. **Audit log all changes** to important documents
4. **Sanitize user inputs** to prevent XSS attacks
5. **Rate limiting** for expensive operations like report generation