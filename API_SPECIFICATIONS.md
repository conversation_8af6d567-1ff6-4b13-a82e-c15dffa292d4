# KLA Question Section - API Specifications and Improvements

## Executive Summary

This document outlines suggested API specifications and improvements for the KLA Question Section system based on analysis of existing backend services, frontend mocks, and current API patterns.

## Current Architecture Analysis

### Question Section Service (Main Service)
- **Base URL**: `/api/`
- **Pattern**: RESTful with entity-based routes
- **Authentication**: Spring Security integration
- **Response Format**: Standard ResponseEntity with JSON
- **Error Handling**: GlobalExceptionHandler with custom problem details
- **Pagination**: PageResponseContract with standard parameters

### Core MDM Service (Master Data Management)
- **Base URL**: `/` (root level)
- **Pattern**: Simple RESTful endpoints
- **Caching**: Redis-based caching with manual cache clearing
- **Response Format**: Direct entity responses or lists
- **Error Handling**: Basic exception handling

## API Standards and Conventions

### Established Patterns from Question Service

```json
{
  "url_structure": "/api/{resource-name}/{id?}/{action?}",
  "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"],
  "pagination": {
    "page": 0,
    "size": 10,
    "sortBy": "createdAt",
    "sortDirection": "desc"
  },
  "response_wrapper": {
    "data": "entity or array",
    "totalElements": "number",
    "totalPages": "number",
    "page": "number",
    "size": "number"
  }
}
```

## Suggested API Specifications

## 1. Master Data Management Service (MDM) Improvements

### Current Issues Identified:
- Inconsistent endpoint patterns
- Missing standardized pagination
- Lack of search/filter capabilities
- Inconsistent response formats
- Lacks proper pagination support

### Recommended Improvements:

#### 1.1 Assembly Management
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/assemblies",
      "description": "Get all assemblies with filtering",
      "parameters": {
        "status": "ACTIVE|INACTIVE",
        "page": 0,
        "size": 10,
        "sortBy": "number",
        "sortDirection": "asc"
      },
      "response": {
        "data": [
          {
            "id": "uuid",
            "number": 15,
            "name": "15th Kerala Legislative Assembly",
            "nameInLocal": "15-ാം കേരള നിയമസഭ",
            "startDate": "2021-05-20",
            "endDate": "2026-05-19",
            "status": "ACTIVE",
            "totalSessions": 13,
            "currentSession": 13
          }
        ],
        "pagination": "StandardPaginationResponse"
      }
    },
    {
      "method": "GET", 
      "path": "/assemblies/{number}",
      "description": "Get assembly by number"
    },
    {
      "method": "GET",
      "path": "/assemblies/active",
      "description": "Get current active assembly",
      "response": {
        "assembly": 15,
        "session": 13,
        "assemblyDetails": "AssemblyResponse"
      }
    },
    {
      "method": "GET",
      "path": "/assemblies-sessions",
      "description": "Get all assemblies with nested sessions (default 5 assemblies, auto-paginated after 30)",
      "parameters": {
        "limit": "5 (default, max 50)",
        "offset": "0 (for pagination beyond 30 assemblies)",
        "includeActiveOnly": "false (default: false - if true, returns only active assembly and session)"
      },
      "response": {
        "assemblies": [
          {
            "id": "uuid",
            "number": 15,
            "name": "15th Kerala Legislative Assembly",
            "nameInLocal": "15-ാം കേരള നിയമസഭ",
            "startDate": "2021-05-20",
            "endDate": null,
            "status": "ACTIVE",
            "totalSessions": 13,
            "sessions": [
              {
                "id": "uuid",
                "number": 13,
                "name": "Budget Session 2025",
                "nameInLocal": "ബജറ്റ് സെഷൻ 2025",
                "startDate": "2025-02-03",
                "endDate": "2025-03-15",
                "status": "ACTIVE"
              },
              {
                "id": "uuid", 
                "number": 12,
                "name": "Winter Session 2024",
                "nameInLocal": "ശൈത്യകാല സെഷൻ 2024",
                "startDate": "2024-11-10",
                "endDate": "2024-12-20",
                "status": "INACTIVE"
              }
            ]
          }
        ],
        "activeAssemblyId": "uuid",
        "activeSessionId": "uuid",
        "pagination": {
          "hasMore": false,
          "totalAssemblies": 15,
          "limit": 5,
          "offset": 0
        }
      }
    }
  ]
}
```

#### Implementation Notes for Assemblies-Sessions
```
Business Rules:
- Only ONE assembly can have status="ACTIVE" at any time (currently 15th Assembly)
- Only ONE session can have status="ACTIVE" at any time within the active assembly
- Session status: ACTIVE (current), INACTIVE (completed), UPCOMING (scheduled but not started)

Performance Considerations:
- Default response: latest 5 assemblies (with all their sessions)
- Use includeActiveOnly=true to get just the active assembly and session (fast lookup)
- Auto-sort by assembly number DESC (latest first)
- Sessions within each assembly sorted by number DESC (latest first)
- Pagination kicks in only when there are 30+ assemblies (future-proofing)

Default Behavior:
- Show latest 5 assemblies with all their sessions
- Total response size: ~90 session objects (manageable)
- Frontend can cache this for dropdowns/selectors
- nameInLocal fields provide Malayalam translations for UI
```

#### 1.2 Designations Management
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/designations",
      "description": "Get all designations with search",
      "parameters": {
        "search": "string",
        "ministerial": "boolean",
        "active": "boolean",
        "page": 0,
        "size": 20
      },
      "response": {
        "data": [
          {
            "id": "uuid",
            "name": "Chief Minister",
            "nameInLocal": "മുഖ്യമന്ത്രി",
            "isMinisterial": true,
            "priority": 1,
            "isActive": true,
            "createdAt": "2023-01-01T00:00:00Z"
          }
        ]
      }
    },
    {
      "method": "GET",
      "path": "/designations/{id}",
      "description": "Get designation by ID"
    },
    {
      "method": "GET",
      "path": "/designations/ministerial",
      "description": "Get only ministerial designations"
    }
  ]
}
```

#### 1.3 Portfolio/Subject Management
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/portfolios",
      "description": "Get portfolios with hierarchy",
      "parameters": {
        "search": "string",
        "ministerId": "uuid",
        "active": "boolean"
      },
      "response": {
        "data": [
          {
            "id": "uuid", 
            "name": "Home Affairs",
            "nameInLocal": "ആഭ്യന്തര വകുപ്പ്",
            "currentMinister": "MinisterResponse",
            "subSubjects": ["SubSubjectResponse"],
            "isActive": true
          }
        ]
      }
    },
    {
      "method": "GET",
      "path": "/sub-subjects",
      "description": "Get sub-subjects",
      "parameters": {
        "portfolioId": "uuid",
        "search": "string",
        "page": 0,
        "size": 20
      }
    }
  ]
}
```

#### 1.4 Members and Ministers Management
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/members",
      "description": "Get all members with filters",
      "parameters": {
        "assemblyNumber": 15,
        "constituency": "string",
        "party": "string",
        "status": "ACTIVE|INACTIVE",
        "isMinister": "boolean",
        "search": "string",
        "page": 0,
        "size": 20
      },
      "response": {
        "data": [
          {
            "id": "uuid",
            "name": "Member Name", 
            "nameInLocal": "മലയാളം പേര്",
            "constituency": "ConstituencyResponse",
            "party": "PoliticalPartyResponse",
            "isMinister": false,
            "currentDesignations": ["DesignationResponse"],
            "profilePhoto": "url",
            "status": "ACTIVE",
            "assemblyNumber": 15
          }
        ]
      }
    },
    {
      "method": "GET",
      "path": "/ministers",
      "description": "Get current ministers",
      "parameters": {
        "assemblyNumber": 15,
        "portfolioId": "uuid"
      }
    },
    {
      "method": "GET",
      "path": "/members/{id}/photo",
      "description": "Get member photo",
      "response": "Binary image or URL"
    }
  ]
}
```

#### 1.5 Calendar of Sittings
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/calendar-of-sittings",
      "description": "Get sitting calendar",
      "parameters": {
        "assemblyNumber": 15,
        "sessionNumber": 13,
        "startDate": "2024-01-01",
        "endDate": "2024-12-31",
        "businessType": "QUESTION_HOUR|ZERO_HOUR|GENERAL_DISCUSSION"
      },
      "response": {
        "data": [
          {
            "id": "uuid",
            "date": "2024-01-15",
            "dayOfWeek": "MONDAY",
            "sittingNumber": 1,
            "businessTypes": ["QUESTION_HOUR", "ZERO_HOUR"],
            "startTime": "09:00",
            "endTime": "17:00",
            "isHoliday": false,
            "assemblyNumber": 15,
            "sessionNumber": 13
          }
        ]
      }
    }
  ]
}
```

## 2. Question Service - Current Mocks vs Real APIs Needed

### What's Currently MOCKED in Question Service

#### 2.1 Document Management APIs (Currently Mocked)
```json
{
  "mocked_endpoints": [
    {
      "path": "api/documents-questions/",
      "purpose": "My Questions list page",
      "used_by": "src/pages/my-question/index.jsx",
      "service": "src/services/my-question.js",
      "current_mock": "Simple array return with limit parameter",
      "needs_implementation": "Real question document listing with full pagination & search"
    },
    {
      "path": "api/documents-questions/{id}",
      "purpose": "Question details page",
      "used_by": "src/pages/my-question/index.jsx",
      "service": "src/services/my-question.js", 
      "current_mock": "Single question object from array",
      "needs_implementation": "Real question document detail endpoint"
    }
  ]
}
```

#### 2.2 Starred/Unstarred Question Management (Currently Mocked)
```json
{
  "mocked_endpoints": [
    {
      "path": "api/documents-mockStarred",
      "purpose": "Starred questions for balloting by date",
      "used_by": "src/pages/listing/components/starred/index.jsx", 
      "service": "src/services/starred-questions.js",
      "current_mock": "Date-based mock data (today=pending, yesterday=approved)",
      "needs_implementation": "Real starred questions endpoint by date"
    },
    {
      "path": "api/documents-mockUnstarred", 
      "purpose": "Unstarred questions for balloting by date",
      "used_by": "Balloting system components",
      "service": "src/services/unstarred-questions.js",
      "current_mock": "Date-based mock data (today=pending, yesterday=approved)",
      "needs_implementation": "Real unstarred questions endpoint by date"
    }
  ]
}
```

#### 2.3 Document/Report Generation (Currently Mocked)
```json
{
  "mocked_endpoints": [
    {
      "path": "api/documents-mockas/draft",
      "purpose": "Answer Status Report creation",
      "used_by": "src/pages/answer-status-report/",
      "service": "src/services/answer-status-report.js",
      "current_mock": "localStorage-based mock with UUID generation",
      "needs_implementation": "Real answer status report management"
    },
    {
      "path": "api/documents-mock/draft",
      "purpose": "Late Answer Bulletin creation", 
      "used_by": "src/pages/late-answer-bulletin/",
      "service": "src/services/late-answer-bulletin.js",
      "current_mock": "Static mock response with sample LAB data",
      "needs_implementation": "Real late answer bulletin management"
    },
    {
      "path": "api/documents-mocks/draft",
      "purpose": "Delay Statement List creation",
      "used_by": "src/pages/listing/delay-statement-list/",
      "service": "src/services/delay-statement.js", 
      "current_mock": "Complex mock with drag-drop reordering",
      "needs_implementation": "Real delay statement list management"
    }
  ]
}
```

#### 2.4 Other Mocked Features
```json
{
  "other_mocks": [
    {
      "path": "api/consents/{id}/withdraw",
      "purpose": "Consent withdrawal",
      "current_mock": "Simple status update mock",
      "needs_implementation": "Real consent management"
    },
    {
      "path": "question-service/api/allotment-of-days/{id}",
      "purpose": "Allotment updates",
      "current_mock": "Basic PATCH operation mock",
      "needs_implementation": "Enhanced allotment management"
    }
  ]
}
```

### Required NEW Question Service APIs

#### 2.1 Document Management APIs (Replace Mocks)
```json
{
  "new_endpoints": [
    {
      "method": "GET",
      "path": "/api/documents/questions",
      "description": "Replace api/documents-questions/ mock",
      "purpose": "My Questions page with proper pagination & search",
      "parameters": {
        "page": 0,
        "size": 10,
        "searchText": "string",
        "status": "DRAFT|SUBMITTED|APPROVED|REJECTED",
        "questionDate": "date",
        "assembly": "number",
        "session": "number",
        "sortBy": "questionDate|createdAt|status",
        "sortDirection": "asc|desc"
      },
      "response": {
        "content": ["QuestionDocumentResponse"],
        "pagination": "StandardPaginationResponse"
      }
    },
    {
      "method": "GET",
      "path": "/api/documents/questions/{id}",
      "description": "Replace api/documents-questions/{id} mock",
      "purpose": "Question document details with full metadata"
    }
  ]
}
```

#### 2.2 Question Balloting APIs (Replace Mocks)
```json
{
  "new_endpoints": [
    {
      "method": "GET", 
      "path": "/api/questions/starred/by-date",
      "description": "Replace api/documents-mockStarred mock",
      "purpose": "Starred questions for balloting system",
      "parameters": {
        "questionDate": "2024-01-01 (required)",
        "status": "PENDING|APPROVED|BALLOTED",
        "assembly": 15,
        "session": 13
      },
      "response": {
        "questionDate": "2024-01-01",
        "status": "PENDING|APPROVED", 
        "totalQuestions": 50,
        "questions": [
          {
            "id": "uuid",
            "questionNumber": "Q001",
            "title": "Question about...",
            "memberName": "Member Name",
            "priority": "HIGH|MEDIUM|LOW",
            "status": "PENDING|APPROVED",
            "ballotOrder": "number|null"
          }
        ]
      }
    },
    {
      "method": "GET",
      "path": "/api/questions/unstarred/by-date", 
      "description": "Replace api/documents-mockUnstarred mock",
      "purpose": "Unstarred questions for balloting system",
      "parameters": "Same as starred endpoint"
    }
  ]
}
```

#### 2.3 Report Generation APIs (Replace Mocks)
```json
{
  "new_endpoints": [
    {
      "method": "POST",
      "path": "/api/reports/answer-status-report/draft",
      "description": "Replace api/documents-mockas/draft mock",
      "purpose": "Answer Status Report generation",
      "body": {
        "name": "ASR-2024-001",
        "assembly": 15,
        "session": 13,
        "reportData": "AnswerStatusReportData"
      }
    },
    {
      "method": "POST", 
      "path": "/api/reports/late-answer-bulletin/draft",
      "description": "Replace api/documents-mock/draft mock", 
      "purpose": "Late Answer Bulletin generation",
      "body": {
        "name": "LAB-2024-001",
        "assembly": 15,
        "session": 13,
        "dateRange": {
          "startDate": "2024-01-01",
          "endDate": "2024-01-31"
        }
      }
    },
    {
      "method": "POST",
      "path": "/api/delay-statements/list/draft",
      "description": "Replace api/documents-mocks/draft mock",
      "purpose": "Delay Statement List management",
      "body": {
        "name": "DSL-2024-001", 
        "assembly": 15,
        "session": 13,
        "dateToBeLaidInAssembly": "2024-02-01"
      }
    }
  ]
}
```

### Implementation Priority for Question Service

#### High Priority (Week 1-2)
1. **Document Management APIs**: Replace `api/documents-questions/*` mocks
   - Used by My Questions page (core functionality)
   - Need proper pagination, search, filtering

#### Medium Priority (Week 3-4)  
2. **Question Balloting APIs**: Replace starred/unstarred mocks
   - Used by balloting system
   - Date-based question retrieval for balloting

#### Lower Priority (Week 5-6)
3. **Report Generation APIs**: Replace document creation mocks
   - Answer Status Reports, Late Answer Bulletins, Delay Statement Lists
   - These are specialized report generation features

### Where "Bulk Operations" Come From
The bulk operations I mentioned earlier were **NOT** from existing mocks - they were **suggested enhancements** based on common administrative needs:
- Bulk approve/reject questions
- Bulk export for reports  
- Bulk status updates

These are **optional future enhancements**, not replacing any current mocks.

## 3. Missing APIs That Need Implementation

### 3.1 Document Management
```json
{
  "endpoints": [
    {
      "method": "POST",
      "path": "/api/documents/upload",
      "description": "Upload document attachments",
      "contentType": "multipart/form-data",
      "body": {
        "file": "binary",
        "documentType": "NOTICE|ANSWER|ATTACHMENT",
        "entityId": "uuid",
        "metadata": "object"
      }
    },
    {
      "method": "GET",
      "path": "/api/documents/{id}/preview",
      "description": "Get document preview URL"
    }
  ]
}
```

### 3.2 Notification System
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/api/notifications",
      "description": "Get user notifications",
      "parameters": {
        "type": "NOTICE_SUBMITTED|CONSENT_REQUESTED|DEADLINE_REMINDER",
        "read": "boolean",
        "page": 0,
        "size": 20
      }
    },
    {
      "method": "POST",
      "path": "/api/notifications/{id}/mark-read",
      "description": "Mark notification as read"
    }
  ]
}
```

### 3.3 Audit and History
```json
{
  "endpoints": [
    {
      "method": "GET",
      "path": "/api/{entity}/{id}/history",
      "description": "Get entity change history",
      "response": {
        "data": [
          {
            "id": "uuid",
            "action": "CREATED|UPDATED|DELETED",
            "field": "status",
            "oldValue": "DRAFT",
            "newValue": "SUBMITTED", 
            "changedBy": "UserResponse",
            "changedAt": "2024-01-01T10:00:00Z",
            "reason": "string"
          }
        ]
      }
    }
  ]
}
```

## 4. API Standards and Best Practices

### 4.1 Standardized Response Format
```json
{
  "success_response": {
    "data": "entity or array",
    "metadata": {
      "pagination": {
        "page": 0,
        "size": 10,
        "totalElements": 100,
        "totalPages": 10,
        "hasNext": true,
        "hasPrevious": false
      },
      "sort": [
        {"field": "createdAt", "direction": "desc"}
      ]
    },
    "timestamp": "2024-01-01T10:00:00Z"
  },
  "error_response": {
    "error": {
      "code": "VALIDATION_ERROR",
      "message": "Invalid request parameters",
      "details": [
        {
          "field": "questionDate",
          "message": "Question date cannot be in the past"
        }
      ]
    },
    "timestamp": "2024-01-01T10:00:00Z",
    "path": "/api/notice-for-questions"
  }
}
```

### 4.2 Development Guidelines
```
- Keep API contracts stable during development
- Document breaking changes clearly
- Use feature flags for experimental endpoints
- Coordinate API changes with frontend team
```

### 4.3 Security Standards
```json
{
  "authentication": "JWT Bearer Token",
  "authorization": "Role-based access control",
  "rate_limiting": "100 requests per minute per user",
  "input_validation": "Spring Validation with custom validators",
  "output_sanitization": "XSS protection and data masking"
}
```

## 5. Implementation Priority

### High Priority (Immediate)
1. Standardize MDM service response formats
2. Add pagination to all MDM list endpoints
3. Implement proper error handling in MDM service
4. Add search/filter capabilities to MDM endpoints

### Medium Priority (Next Sprint)
1. Enhanced search capabilities for Question service
2. Bulk operations for question management
3. Document management APIs
4. Member photo management endpoints

### Low Priority (Future)
1. Analytics and reporting APIs
2. Advanced audit capabilities
3. Notification system
4. Real-time updates via WebSocket

## 6. Development Phase Considerations

### MDM Service Evolution
```json
{
  "current_endpoints": "Keep existing paths for stability",
  "enhancement_strategy": "Add new parameters to existing endpoints",
  "changes": [
    "Add optional pagination parameters",
    "Add optional search parameters", 
    "Enhance response format gradually",
    "Improve error handling"
  ]
}
```

### Question Service Enhancements
```json
{
  "approach": "Backward compatible additions",
  "new_features": [
    "Enhanced search endpoint as new route",
    "Bulk operations as separate endpoints",
    "Document management as dedicated service"
  ],
  "stability": [
    "Maintain existing endpoint contracts",
    "Add new endpoints for new functionality"
  ]
}
```

## 7. Cross-Reference with Frontend Dev's Mock API List

Based on the mock API documentation from `/Users/<USER>/Downloads/mock APIs remaining.md`, here's the current status:

### **Still MOCKED (Need Real Implementation):**

#### Question Service Mocks Still Active:
```json
{
  "still_mocked": [
    {
      "api": "GET /api/documents-mockUnstarred?date={date}",
      "purpose": "Unstarred questions for balloting by date",
      "status": "STILL MOCKED - Complex response with minister groups & questions",
      "priority": "HIGH - Used by balloting system"
    },
    {
      "api": "GET /api/documents-mockStarred?date={date}", 
      "purpose": "Starred questions for balloting by date",
      "status": "STILL MOCKED - Complex response with pending notices",
      "priority": "HIGH - Used by balloting system"
    },
    {
      "api": "GET /api/section-notice-for-questions/all",
      "purpose": "Section staff view - all notices",
      "status": "STILL MOCKED - Paginated response",
      "priority": "MEDIUM - Section staff functionality"
    },
    {
      "api": "GET /api/section-notice-for-questions",
      "purpose": "Section staff view - action required notices", 
      "status": "STILL MOCKED - Paginated response",
      "priority": "MEDIUM - Section staff functionality"
    },
    {
      "api": "GET /api/mock/notice-for-questions/{id}",
      "purpose": "Detailed notice view with answers/attachments",
      "status": "STILL MOCKED - Complex response with clauses/answers",
      "priority": "HIGH - Notice detail pages"
    },
    {
      "api": "GET /api/moc/notice-for-questions",
      "purpose": "Notice listing (different from my-notices)",
      "status": "STILL MOCKED - Paginated response",
      "priority": "MEDIUM"
    },
    {
      "api": "GET /api/other-notices/notice-bank",
      "purpose": "Other notices (half-hour, short-notice) bank", 
      "status": "STILL MOCKED - Paginated response",
      "priority": "MEDIUM - Other notice types"
    },
    {
      "api": "GET /api/other-notices/my-notices",
      "purpose": "My other notices",
      "status": "STILL MOCKED - Paginated response", 
      "priority": "MEDIUM - Other notice types"
    },
    {
      "api": "GET /api/notice-bank-for-questions/my-notices",
      "purpose": "Notice bank for questions - my notices",
      "status": "STILL MOCKED - Simple list response",
      "priority": "LOW - Bank functionality"
    },
    {
      "api": "GET /api/explanatory-note-details",
      "purpose": "Explanatory note template data",
      "status": "STILL MOCKED - Template response",
      "priority": "LOW - Template data"
    }
  ]
}
```

#### Document/Report Generation Still Mocked:
```json
{
  "report_apis_mocked": [
    {
      "api": "POST /api/documents/draft",
      "purpose": "Answer Status Report creation",
      "status": "STILL MOCKED - Multiple document types",
      "priority": "MEDIUM - Report generation"
    },
    {
      "api": "GET /api/documents/{documentId}",
      "purpose": "Document retrieval by ID",
      "status": "STILL MOCKED - Generic document response",
      "priority": "MEDIUM - Document management"
    },
    {
      "api": "POST /api/documents/{documentId}/submit",
      "purpose": "Document submission",
      "status": "STILL MOCKED - Status change operation",
      "priority": "MEDIUM - Document workflow"
    }
  ]
}
```

### **Already Replaced with Real APIs:**

#### Question Service - Real APIs Working:
```json
{
  "real_apis_working": [
    {
      "api": "GET /api/notice-for-questions/my-notices",
      "status": "✅ REAL API - Fully implemented with pagination & search",
      "confirmed": "Seen in backend controller NoticeForQuestionController.java"
    },
    {
      "api": "GET /api/notice-for-questions/notice-bank", 
      "status": "✅ REAL API - Fully implemented",
      "confirmed": "Seen in backend controller"
    },
    {
      "api": "POST /api/notice-for-questions/{id}/basic-details",
      "status": "✅ REAL API - Fully implemented",
      "confirmed": "Seen in backend controller"
    },
    {
      "api": "POST /api/notice-for-questions/{id}/notice-details",
      "status": "✅ REAL API - Fully implemented", 
      "confirmed": "Seen in backend controller"
    },
    {
      "api": "All Consent APIs",
      "status": "✅ REAL API - Consent management working",
      "confirmed": "Seen in backend ConsentController.java"
    },
    {
      "api": "All Ballot APIs",
      "status": "✅ REAL API - Balloting system working",
      "confirmed": "Seen in backend BallotController.java"
    },
    {
      "api": "All Allotment APIs",
      "status": "✅ REAL API - Allotment management working", 
      "confirmed": "Seen in backend AllotmentOfDaysController.java"
    }
  ]
}
```

### **MDM Service - Mix of Real and Mocked:**

#### MDM - Real APIs Working:
```json
{
  "mdm_real_apis": [
    {
      "api": "GET /assemblies",
      "status": "✅ REAL API - Basic list (needs enhancement)",
      "confirmed": "Seen in core-mdm-service AssemblyController.java"
    },
    {
      "api": "GET /designations",
      "status": "✅ REAL API - Basic list (needs enhancement)",
      "confirmed": "Seen in core-mdm-service DesignationController.java"
    }
  ]
}
```

#### MDM - Still Mocked:
```json
{
  "mdm_still_mocked": [
    {
      "api": "GET /api/active-assembly",
      "purpose": "Current active assembly and session",
      "status": "STILL MOCKED - Simple response {assembly: '15', session: '1'}",
      "priority": "HIGH - Used throughout app"
    },
    {
      "api": "GET /api/get/assembly",
      "purpose": "Assembly list (different format than /assemblies)",
      "status": "STILL MOCKED - Simple array response",
      "priority": "MEDIUM - May be duplicate of real API"
    },
    {
      "api": "GET /api/get/assembly/{klaId}/sessions",
      "purpose": "Sessions for specific assembly",
      "status": "STILL MOCKED - Simple array response", 
      "priority": "HIGH - Session management"
    },
    {
      "api": "GET /api/calendar-of-sittings",
      "purpose": "Calendar of sitting dates and entries",
      "status": "STILL MOCKED - Complex calendar response",
      "priority": "MEDIUM - Calendar functionality"
    },
    {
      "api": "GET /api/private-member-resolutions",
      "purpose": "Private member resolutions list",
      "status": "STILL MOCKED - Simple list response",
      "priority": "LOW - Private member functionality"
    },
    {
      "api": "GET /api/private-member-bills", 
      "purpose": "Private member bills and member list",
      "status": "STILL MOCKED - Multiple endpoints with same path",
      "priority": "LOW - Private member functionality"
    },
    {
      "api": "GET /api/user/current-profile",
      "purpose": "Current user profile with minister/member details",
      "status": "STILL MOCKED - Complex user profile response",
      "priority": "HIGH - User authentication/profile"
    },
    {
      "api": "GET /api/places-list",
      "purpose": "Places list for form dropdowns",
      "status": "STILL MOCKED - Simple value/label response",
      "priority": "LOW - Form data"
    }
  ]
}
```

## Updated Implementation Priority

### **Critical Priority (Week 1):**
1. **Active Assembly API** - `GET /api/active-assembly` - Used throughout app
2. **User Profile API** - `GET /api/user/current-profile` - Authentication/authorization 
3. **Sessions API** - `GET /api/get/assembly/{klaId}/sessions` - Core functionality

### **High Priority (Week 2-3):**
4. **Starred/Unstarred Balloting** - `GET /api/documents-mock*` - Balloting system
5. **Notice Detail API** - `GET /api/mock/notice-for-questions/{id}` - Notice details
6. **Section Staff APIs** - `GET /api/section-notice-for-questions/*` - Staff workflow

### **Medium Priority (Week 4-5):**
7. **Document Management** - `POST/GET /api/documents/*` - Report generation  
8. **Other Notices** - `GET /api/other-notices/*` - Non-question notices
9. **Calendar of Sittings** - `GET /api/calendar-of-sittings` - Calendar functionality

### **Low Priority (Week 6+):**
10. **Private Member APIs** - `GET /api/private-member-*` - Private member features
11. **Places/Templates** - `GET /api/places-list`, `/api/explanatory-note-details` - Form data

## 7. Current State vs Proposed Changes - Detailed Analysis

### What Currently EXISTS in Backend Services

#### MDM Service (core-mdm-service) - EXISTING Endpoints:
```json
{
  "assemblies": {
    "GET /assemblies": "BASIC - Returns simple list, no pagination",
    "GET /assemblies/{number}": "BASIC - Returns single assembly",
    "DELETE /assemblies/cache": "Cache clearing"
  },
  "designations": {
    "GET /designations": "BASIC - Returns simple list, no pagination", 
    "GET /designations/{id}": "BASIC - Returns single designation",
    "DELETE /designations/cache": "Cache clearing"
  },
  "existing_entities": [
    "constituencies", "departments", "districts", "institutions", 
    "political-parties", "political-fronts", "portfolio-subjects",
    "sub-subjects", "member-users", "minister-users", "users"
  ]
}
```

#### Question Service (question-section-service) - EXISTING Endpoints:
```json
{
  "notice_management": {
    "GET /api/notice-for-questions/my-notices": "FULLY IMPLEMENTED with pagination & search",
    "GET /api/notice-for-questions/notice-bank": "FULLY IMPLEMENTED",
    "POST /api/notice-for-questions/{id}/basic-details": "FULLY IMPLEMENTED",
    "POST /api/notice-for-questions/{id}/notice-details": "FULLY IMPLEMENTED"
  },
  "other_features": [
    "ballots", "consent", "allotment-of-days", "answers", 
    "delay-statements", "minister-designation-groups"
  ]
}
```

### What's Currently MOCKED in Frontend

#### Frontend Mocks (question-section-app/mock/):
```json
{
  "mdm_mocks": {
    "VITE_MDMS_API_BASE_URL/api/active-assembly": "Simple object {assembly: '15', session: '13'}",
    "VITE_MDM_SERVICE_BASE_URL/designations": "Array of designation objects"
  },
  "question_service_mocks": [
    "allotment-handlers.js", "consent-handlers.js", "my-question-handler.js",
    "unstarred-questions-handlers.js", "starred-questions-handlers.js"
  ],
  "note": "Most notice APIs removed from mocks and replaced with real API calls"
}
```

### What's COMPLETELY NEW (Needs Implementation)

#### 1. **NEW: /assemblies-sessions endpoint**
- **Purpose**: Replace multiple API calls for assembly/session dropdowns
- **Current Workaround**: Frontend likely makes separate calls or uses mocked data
- **Why Needed**: Efficient nested data loading for forms

#### 2. **ENHANCED: All MDM endpoints need pagination & search**
- **Current Issue**: Basic list endpoints return all data
- **What's Missing**: Pagination, search, filtering parameters
- **Impact**: Performance issues as data grows

#### 3. **NEW: nameInLocal fields everywhere**
- **Current Issue**: No Malayalam language support in API responses
- **What's Missing**: All entities need nameInLocal field
- **Frontend Impact**: UI currently shows only English names

### Specific Implementation Requirements

#### Immediate Changes Needed:

1. **MDM Service Enhancements** (Modify existing endpoints):
```json
{
  "GET /assemblies": {
    "add_parameters": ["status", "page", "size", "sortBy", "sortDirection"],
    "modify_response": "Wrap in pagination object, add nameInLocal field"
  },
  "GET /designations": {
    "add_parameters": ["search", "ministerial", "active", "page", "size"], 
    "modify_response": "Add pagination, nameInLocal field"
  }
}
```

2. **New Endpoint Implementation**:
```json
{
  "GET /assemblies-sessions": {
    "type": "COMPLETELY NEW",
    "purpose": "Replace frontend assembly/session dropdown logic",
    "replaces": "Multiple separate API calls + frontend data joining"
  }
}
```

3. **Database Schema Impact**:
```sql
-- Need to add nameInLocal columns to existing tables
ALTER TABLE assemblies ADD COLUMN name_in_local VARCHAR(255);
ALTER TABLE sessions ADD COLUMN name_in_local VARCHAR(255);  
ALTER TABLE designations ADD COLUMN name_in_local VARCHAR(255);
ALTER TABLE portfolios ADD COLUMN name_in_local VARCHAR(255);
```

### Frontend Mock Updates Required

#### Remove These Mocks (Replace with Real APIs):
- `active-assembly.js` → Use real `/assemblies/active` 
- `designations.js` → Use enhanced `/designations`

#### Update These Mocks (Match New API Format):
- Add pagination wrappers to all MDM mocks
- Add nameInLocal fields to all entity objects
- Update response formats to match backend changes

### Priority Implementation Order:

1. **Week 1**: Add nameInLocal to database + basic endpoints
2. **Week 2**: Add pagination to MDM service endpoints  
3. **Week 3**: Implement /assemblies-sessions endpoint
4. **Week 4**: Update frontend mocks + remove old ones

---

This specification provides a comprehensive roadmap for improving both the Question Service and MDM Service APIs while maintaining backward compatibility and following established Spring Boot best practices.