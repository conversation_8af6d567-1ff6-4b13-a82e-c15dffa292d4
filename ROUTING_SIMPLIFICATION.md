# Routing System Simplification - Clean Factory Pattern Implementation

## Overview

The routing system has been completely refactored using factory patterns with **all legacy code removed**. The new system generates all configurations from base constants and provides a unified, generic approach to route building with consistent patterns. No backward compatibility - everything uses the new clean system.

## Key Improvements

### 1. **Factory-Based Configuration**
- Base constants drive all type definitions using factory functions
- Automatic generation of key, slug, and label from SNAKE_CASE constants
- Single source of truth eliminates manual duplication

### 2. **Consistent Route Patterns**
All routes follow the same structure: `/{context}/{resourceType}/:id/{action}`

```javascript
// Document routes: /section/documents/{documentType}/:id/{action}
buildRoute.document(documentType, documentId, action?)

// Notice routes: /{context}/my-notices/{noticeType}/:id/{action}
buildRoute.notice(noticeType, documentId, action?, context?)

// Question notice routes: /{context}/my-question-notices/notice-for-question/:id/{action}
buildRoute.questionNotice(documentId, action?, context?)

// Question routes: /{context}/my-question/:id/{action}
buildRoute.question(documentId, action?, context?)
```

### 3. **Standardized Actions**
- `view` - View existing resource
- `edit` - Edit existing resource
- `create` - Create new resource (replaces inconsistent "add")

### 4. **Factory-Generated Types**
Types are automatically generated from base constants:

```javascript
// Base constants
const DOCUMENT_TYPE_KEYS = ['MINISTER_DESIGNATION_GROUP', 'ALLOTMENT_OF_DAYS', ...]

// Auto-generated with factory functions
DOCUMENT_TYPES.MINISTER_DESIGNATION_GROUP = {
  key: 'MINISTER_DESIGNATION_GROUP',
  slug: 'minister-designation-group',  // auto-converted from SNAKE_CASE
  label: 'Minister Designation Group'  // auto-converted to Title Case
}
```

### 5. **Automatic Context Switching**
Routes automatically handle member/ppo context:
```javascript
// Automatically generates correct path based on context
buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', '123', 'create', 'ppo')
// → "/ppo/my-notices/notice-for-short-notice/123/create"

buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', '123', 'create', 'member')
// → "/member/my-notices/notice-for-short-notice/123/create"
```

## Before vs After Examples

### Document Route Building

**Before:**
```javascript
// Hardcoded route construction with manual slug extraction
const slug = matchedDocument.route.split("/").filter(Boolean).pop();
navigate(`/section/documents/${slug}/${id}/view`);

// Or using specific builder functions
navigate(buildRoute.ministerDesignationGroupView(documentId));
```

**After:**
```javascript
// Generic factory-based approach works for any document type
navigate(buildRoute.document('MINISTER_DESIGNATION_GROUP', documentId, 'view'));

// Consistent pattern for all document types
navigate(buildRoute.document('ALLOTMENT_OF_DAYS', documentId, 'edit'));
navigate(buildRoute.document('SCHEDULE_OF_ACTIVITY', documentId, 'create'));
```

### Notice Route Building

**Before:**
```javascript
// Manual context switching and hardcoded paths
const basePath = `/member/my-notices`;
const contextualBasePath = isPPO ? basePath.replace("/member/", "/ppo/") : basePath;
return action === "add"
  ? `${contextualBasePath}/short-notice/add/`
  : `${contextualBasePath}/short-notice/view/`;
```

**After:**
```javascript
// Automatic context handling with consistent actions
buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', documentId, 'create', context);
buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', documentId, 'view', context);
```

## Factory-Based Configuration Structure

### Base Constants (Single Source of Truth)
```javascript
// Document types generated from base constants
const DOCUMENT_TYPE_KEYS = [
  'MINISTER_DESIGNATION_GROUP',
  'ALLOTMENT_OF_DAYS',
  'SCHEDULE_OF_ACTIVITY',
  // ... more types
];

// Notice types generated from base constants
const NOTICE_TYPE_KEYS = [
  'NOTICE_FOR_SHORT_NOTICE',
  'NOTICE_FOR_HALF_AN_HOUR_DISCUSSION',
  // ... more types
];
```

### Auto-Generated Types
```javascript
// Factory functions automatically create these from base constants
export const DOCUMENT_TYPES = {
  MINISTER_DESIGNATION_GROUP: {
    key: 'MINISTER_DESIGNATION_GROUP',
    slug: 'minister-designation-group',  // auto-converted
    label: 'Minister Designation Group'  // auto-converted
  },
  // ... all other types auto-generated
};

export const NOTICE_TYPES = {
  NOTICE_FOR_SHORT_NOTICE: {
    key: 'NOTICE_FOR_SHORT_NOTICE',
    slug: 'notice-for-short-notice',     // auto-converted
    label: 'Notice For Short Notice'     // auto-converted
  },
  // ... all other types auto-generated
};
```

### Standardized Actions
```javascript
export const ACTIONS = {
  VIEW: 'view',
  EDIT: 'edit',
  CREATE: 'create',  // Consistent across all resource types
};
```

## Clean Implementation - No Legacy Code

All legacy route functions have been **completely removed**. The codebase now uses only the new factory-based system:

```javascript
// ✅ Clean new approach - only way to build routes
buildRoute.document('MINISTER_DESIGNATION_GROUP', id, 'view')
buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', id, 'create', context)
buildRoute.questionNotice(id, 'edit', context)
buildRoute.question(id, 'view', context)
```

## Helper Functions

Clean helper functions for route building:
```javascript
// Get document/notice types by key or slug
routeHelpers.getDocumentType('MINISTER_DESIGNATION_GROUP')
routeHelpers.getNoticeType('NOTICE_FOR_SHORT_NOTICE')

// Get current context automatically
routeHelpers.getCurrentContext() // Returns 'member', 'ppo', or 'section'
```

## Benefits Achieved

1. **🏭 Factory Pattern**: All configurations generated from base constants
2. **📐 Consistency**: All routes follow the same `/{context}/{resourceType}/:id/{action}` pattern
3. **🔧 Maintainability**: Single place to add new types - just add to base constants array
4. **🛡️ Type Safety**: Centralized type definitions prevent typos and inconsistencies
5. **🔄 Flexibility**: Easy to add new document/notice types or actions
6. **🎯 Context Awareness**: Automatic member/ppo route switching
7. **📉 Reduced Code**: Eliminated hundreds of lines of duplicate route definitions
8. **⚡ Performance**: Factory functions generate configurations at build time

## Factory Pattern Benefits

### Adding New Document Types
**Before:** Required updates in 4+ places
```javascript
// 1. Add to DOCUMENT_TYPES object
// 2. Add to DYNAMIC_ROUTES
// 3. Add to buildRoute functions
// 4. Add to document-types utility
```

**After:** Just add to base constants array
```javascript
// Single change - everything else auto-generated
const DOCUMENT_TYPE_KEYS = [
  'MINISTER_DESIGNATION_GROUP',
  'ALLOTMENT_OF_DAYS',
  'NEW_DOCUMENT_TYPE',  // ← Just add here!
];
```

### Consistent Transformations
- `MINISTER_DESIGNATION_GROUP` → `minister-designation-group` (slug)
- `MINISTER_DESIGNATION_GROUP` → `Minister Designation Group` (label)
- All transformations use the same factory functions

## Complete Migration Completed

All code has been migrated to use the new factory-based system. The migration included:

### ✅ **Updated Components**
- All route building now uses generic `buildRoute` methods
- Router.jsx updated to use consistent action names (`create` instead of `add`)
- Document-types utility cleaned up and simplified
- Navigation utilities updated to use automatic context detection

### ✅ **Standardized Actions**
- All routes now use `create` instead of inconsistent `add`
- Route patterns updated: `/my-notices/notice-for-short-notice/:id/create`
- Consistent action placement at the end of all routes

### ✅ **Clean Codebase**
```javascript
// ✅ Only way to build routes now
buildRoute.document('MINISTER_DESIGNATION_GROUP', id, 'view')
buildRoute.notice('NOTICE_FOR_SHORT_NOTICE', id, 'create', context)
buildRoute.questionNotice(id, 'edit', context)
buildRoute.question(id, 'view', context)
```

## Simplified Balloting

Balloting routes now follow the same consistent pattern:
```javascript
// Before: Complex date/timestamp handling in URL path
buildRoute.balloting(id, questionDate, status)

// After: Simple action-based approach (dates as URL params)
buildRoute.balloting(id, 'submit')
buildRoute.balloting(id, 'cancel')
```

This factory-based refactor provides a robust, scalable foundation for routing that automatically maintains consistency as the application grows.
