## [0.2.0] - 24-05-2025

[QNA-routes] 
- Revamped routes and hierarchy
- Smart breadcrumbs
- Temp home screens
- Refactored code

## [0.1.30] - 23-05-2025
[QNA-253] (https://eniyamasabha.atlassian.net/browse/QNA-253)
 
### Add
- Added Return Setup modal for Setting of starred.

## [0.1.29] - 23-05-2025
[QNA-231] (https://eniyamasabha.atlassian.net/browse/QNA-231)

### Add
- Added MLA Login Question view page and Withdraw

## [0.1.28] - 23-05-2025
 
[QNA-222] (https://eniyamasabha.atlassian.net/browse/QNA-222)
 
### Fixed
- Added the setting up of starred question
 
## [0.1.27] - 23-05-2025
[QNA-252] (https://eniyamasabha.atlassian.net/browse/QNA-252)
 
### Add
- Added Rule Clause modal for Setting of starred.
 
 
## [0.1.26] - 23-05-2025
[QNA-249] (https://eniyamasabha.atlassian.net/browse/QNA-249)
 
### Add
- Added unstarred questions setting with tab-based routing.
- Integrated MOCK API and components for listing and filtering questions
 
 
## [0.1.25] - 22-05-2025
[QNA-211] (https://eniyamasabha.atlassian.net/browse/QNA-211)
 
### Add
- Added withdraw notice modal in PM Notice.
 
 
## [0.1.24] - 21-05-2025
[QNA-237] (https://eniyamasabha.atlassian.net/browse/QNA-237)
 
### Add
- Added Save modal for Setting of unstarred
 
## [0.1.23] - 21-05-2025
[QNA-224] (https://eniyamasabha.atlassian.net/browse/QNA-224)
 
### Add
- Added rout to the read only component for short notice, private member and half an hour
- Added ui changes for notice details read only component
- set the routing for listed notices (short notice, half an hour,private member)
 
## [0.1.22] - 21-05-2025
[QNA-216] (https://eniyamasabha.atlassian.net/browse/QNA-216)
 
### Add
- fix for preview modal in half an hour section
- added partial save functionaly for explantory note in both short notice and half an hour
 
## [0.1.21] - 21-05-2025
[QNA-221] (https://eniyamasabha.atlassian.net/browse/QNA-221)
 
### Fixed
- Fix My notice, Notice bank Page heading and Tab name issue
 
## [0.1.20] - 20-05-2025
[QNA-246] (https://eniyamasabha.atlassian.net/browse/QNA-246)
 
### Add
- Added Document forward modal for Setting of unstarred
 
## [0.1.19] - 20-05-2025
[QNA-235] (https://eniyamasabha.atlassian.net/browse/QNA-235)
 
### Add
- Added Notice for Question List Modal
 
## [0.1.18] - 20-05-2025
[QNA-242] (https://eniyamasabha.atlassian.net/browse/QNA-242)
 
### Add
- Added Notice for Question List Modal
 
## [0.1.17] - 20-05-2025
[QNA-241] (https://eniyamasabha.atlassian.net/browse/QNA-241)
 
### Add
- Delay Answer Bulletin API integration
 
## [0.1.16] - 19-05-2025
 
[QNA-215] (https://eniyamasabha.atlassian.net/browse/QNA-205)
 
### Fix
- duplicate check modal ui fixes updated
- updated pagination,
- added heading and clause cards sepperately
 
## [0.1.15] - 19-05-2025
 
[QNA-206] (https://eniyamasabha.atlassian.net/browse/QNA-206)
 
### Fixed
- partial save not working properly
- explanatory accordian design not proper
- Download pdf not working - explanatory note
- After a refresh, the selected question category resets to null
- Show tick functionality not working properly
 
## [0.1.14] - 19-05-2025
 
[QNA-226] (https://eniyamasabha.atlassian.net/browse/QNA-226)
 
### Fixed
- Fixed the ballot listing route to corresponding ballots with new ballotId api
 
## [0.1.13] - 19-05-2025
[QNA-228] (https://eniyamasabha.atlassian.net/browse/QNA-228)
 
### Fixed
- Fixed Assembly and Session filter in all Documents page
 
## [0.1.12] - 19-05-2025
[QNA-219] (https://eniyamasabha.atlassian.net/browse/QNA-219)
 
### Add
- Integrated Postponed Confirmation Dialog for notice management.
 
## [0.1.11] - 19-05-2025
[QNA-233] (https://eniyamasabha.atlassian.net/browse/QNA-233)
 
### Add
- Added Notice view details page
 
 
## [0.1.10] - 16-05-2025
[QNA-223] (https://eniyamasabha.atlassian.net/browse/QNA-223)
 
### Fixed
- Added explanatory note template for short notice
 
## [0.1.9] - 16-05-2025
[QNA-214] (https://eniyamasabha.atlassian.net/browse/QNA-214)
 
### Fixed
- Fixed Apply update design
- Fixed AOD preview modal
 
## [0.1.8] - 14-05-2025
 
[QNA-208] (https://eniyamasabha.atlassian.net/browse/QNA-208)
 
### Fixed
- Fixed the ballot listing route to corresponding ballots
 
## [0.1.7] - 15-05-2025
 
[QNA-205] (https://eniyamasabha.atlassian.net/browse/QNA-205)
 
### Fix
- fix for private member notice creation page
- duplicate check modal updated
- added handler , services
 
## [0.1.6] - 15-05-2025
[QNA-207] (https://eniyamasabha.atlassian.net/browse/QNA-207)
### Add
- Return Asked Question Modal
- Added temporary mock for preview modal
- Mdm used for profile picture
 
## [0.1.5] - 14-05-2025
 
[QNA-212] (https://eniyamasabha.atlassian.net/browse/QNA-212)
 
- Refactor BasicDetails and QuestionEditPage components for improved layout and functionality
 
## [0.1.4] - 15-05-2025
[QNA-192] (https://eniyamasabha.atlassian.net/browse/QNA-192)
 
### Added
- View details and "Apply Updates" feature to sync minister changes (name, designation updates)
 
## [0.1.4] - 15-05-2025
 
[QNA-206] (https://eniyamasabha.atlassian.net/browse/QNA-206)
 
### Fixed
- partial save not working properly
- explanatory accordian design not proper
- Download pdf not working - explanatory note
- After a refresh, the selected question category resets to null
- Show tick functionality not working properly
 
## [0.1.3] - 12-05-2025
[QNA-201] (https://eniyamasabha.atlassian.net/browse/QNA-210)
 
### Fixed
- Fixed Skipped and To do test cases
 
## [0.1.2] - 10-05-2025
 
- Notice listing pages and notice bank integrated
- Issue fixes for notice creation
 
## [0.1.1] - 10-05-2025
 
- Further fixes and refactoring
 
## [0.1.0] - 10-05-2025
 
## Refactoring
- Refactored the codebase to use kebab-case for file and folder names
- Removed unused dependencies
- Updated all imports to reflect the new file and folder names
- Fixed all test errors
- Updated all component and hook names to reflect the new file and folder names
- Updated all API calls to reflect the new API endpoints
- Updated all API response handling to reflect the new API response structure
- Updated all API response types to reflect the new API response structure
 
## [0.0.91] - 09-05-2025
 
[QNA-209] (https://eniyamasabha.atlassian.net/browse/QNA-209)
 
### Fixed
- Fixed Button POsition in Ballot Result Page
- Fixed some Styling issues
 
## [0.0.90] - 09-05-2025
 
[QNA-196] (https://eniyamasabha.atlassian.net/browse/QNA-196)
 
### Add
- Added apply updates from AOD pop up on SOA page
 
## [0.0.89] - 07-05-2025
[QNA-201] (https://eniyamasabha.atlassian.net/browse/QNA-201)
 
### Fixed
- Fixed Filter tag issue in all listing pages
 
## [0.0.88] - 07-05-2025
 
[QNA-190] (https://eniyamasabha.atlassian.net/browse/QNA-190)
 
### Add
- Added read only and edited view of question edit page of basic details
- Added read only and edited view of question edit page of notice details
 
## [0.0.87] - 07-05-2025
 
[QNA-202] (https://eniyamasabha.atlassian.net/browse/QNA-202)
 
### Update
- Added Language
 
## [0.0.85] - 06-05-2025
 
[QNA-147] (https://eniyamasabha.atlassian.net/browse/QNA-147)
 
### Update
- Test case updates
 
 
## [0.0.84] - 05-05-2025
 
[QNA-200] (https://eniyamasabha.atlassian.net/browse/QNA-200)
 
### Update
- Spinner Loader added to all listing pages
- loader views , when any filtrations applies to the list.
- added common spin-loader for reuse.
- fixed router issues while creatingg new document.
 
## [0.0.83] - 02-05-2025
[QNA-159] (https://eniyamasabha.atlassian.net/browse/QNA-159)
 
### Update
- Mock added
## [0.0.82] - 02-05-2025
 
[QNA-168] (https://eniyamasabha.atlassian.net/browse/QNA-168)
 
### Add
- Added scrollbar to preview page
 
## [0.0.81] - 02-05-2025
 
[QNA-159] (https://eniyamasabha.atlassian.net/browse/QNA-159)
 
### Update
- Live server updated
 
## [0.0.80] - 02-05-2025
 
[QNA-195] (https://eniyamasabha.atlassian.net/browse/QNA-195)
 
### Add
- Added date shifting pop in AOD page
 
 
## [0.0.79] - 02-05-2025
 
[QNA-159] (https://eniyamasabha.atlassian.net/browse/QNA-159)
 
### Add
- Mocked Update Delay statement list document Draft
- Mocked GET Delay statement list Document
- Mocked Refresh LAB
- Mocked Upload delay statement PDF
- Mocked Add delay statement entry
- Mocked Remove Delay statement entry
- Mocked Reorder delay statement entries
 
## [0.0.78] - 02-05-2025
 
[QNA-168] (https://eniyamasabha.atlassian.net/browse/QNA-168)
 
### Fix
-  Added explanatory note and preview of explanatory note in short notice page
 
## [0.0.77] - 02-05-2025
 
[QNA-199] (https://eniyamasabha.atlassian.net/browse/QNA-199)
 
### Fix
-  Fixed filtering issue in all document listing page
 
 
## [0.0.76] - 29-04-2025
 
[QNA-174] (https://eniyamasabha.atlassian.net/browse/QNA-174)
 
### Fix
- Listing page Routes to corresponding docs while clicking.
- padding sorted based on the length of the name
 
## [0.0.75] - 25-04-2025
 
[QNA-194] (https://eniyamasabha.atlassian.net/browse/QNA-194)
 
### Add
-  Added COS button with multiselect dropdown functionality in AOD page
 
## [0.0.74] - 25-04-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Fix
- Review comments from demo session
 
## [0.0.73] - 25-04-2025
 
[QNA-168] (https://eniyamasabha.atlassian.net/browse/QNA-168)
 
### Add
- Added Explanatory Note component for short notice
 
## [0.0.72] - 25-04-2025
 
[QNA-167] (https://eniyamasabha.atlassian.net/browse/QNA-167)
 
### Add
- feat: Add notice for question list page
- Added Mock Data to fetch notices with filtering and pagination.
 
## [0.0.71] - 25-04-2025
 
[QNA-191] (https://eniyamasabha.atlassian.net/browse/QNA-191)
 
### Add
- Added pop up component
 
## [0.0.70] - 25-04-2025
 
[QNA-189] (https://eniyamasabha.atlassian.net/browse/QNA-189)
 
### Add
- Added Action to be taken and All Tabs of Section Notice for Question
 
## [0.0.69] - 25-04-2025
 
[QNA-166] (https://eniyamasabha.atlassian.net/browse/QNA-166)
 
### Add
- Add default assembly and session filetrs for my notices
 
## [0.0.68] - 25-04-2025
 
[QNA-171] (https://eniyamasabha.atlassian.net/browse/QNA-171)
 
### Add
- Fix for partil save
- Read only components, popup , submit handle
- Integrated Login user Profile in Private Memeber Notice For Question Component
 
## [0.0.67] - 24-04-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Fix
- Auto save functionality on Explanatory Note
 
## [0.0.66] - 22-04-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Add
- Integrated Login user Profile in Half an hour Discussion
 
## [0.0.65] - 23-04-2025
 
[QNA-172] (https://eniyamasabha.atlassian.net/browse/QNA-172)
 
### Add
- Added view list for starred questions
 
## [0.0.64] - 16-04-2025
 
[QNA-169] (https://eniyamasabha.atlassian.net/browse/QNA-169)
 
### Add
- Set uped global state for the currently logged in user profile
 
## [0.0.63] - 21-04-2025
 
[QNA-170] (https://eniyamasabha.atlassian.net/browse/QNA-170)
 
### Add
- Added list view for unstarred questions
 
 
## [0.0.62] - 21-04-2025
 
[QNA-162] (https://eniyamasabha.atlassian.net/browse/QNA-162)
 
### Add
- Added mock data for reports section
 
## [0.0.61] - 16-04-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Add
- Added RichText to PDF viewer component
 
## [0.0.60] - 11-04-2025
 
[QNA-160] (https://eniyamasabha.atlassian.net/browse/QNA-160)
 
### Add
- Add Correction of Answer page and related components
- Implement Mock API for basic details
- Add PDF viewer and assignment dialog for Correction of Answer
 
## [0.0.59] - 11-04-2025
 
[QNA-162] (https://eniyamasabha.atlassian.net/browse/QNA-162)
 
### Add
-  Added Answer Status Report screen
 
## [0.0.58] - 11-04-2025
 
[QNA-146] (https://eniyamasabha.atlassian.net/browse/QNA-146)
 
### Add
- Implemented the setting of starred question page
-  Mocked the ballotcard
-  Mocked the Pending notices
 
## [0.0.57] - 11-04-2025
 
[QNA-161] (https://eniyamasabha.atlassian.net/browse/QNA-161)
 
### Add
-  Added setting of unstarred questions page
 
## [0.0.56] - 10-04-2025
 
[QNA-168] (https://eniyamasabha.atlassian.net/browse/QNA-168)
 
### Add
-  Fix : Resolve validation errors- Short Notice
 
## [0.0.55] - 11-04-2025
 
[QNA-116] (https://eniyamasabha.atlassian.net/browse/QNA-116)
 
### Add
-  Create notice for private member question create/edit page
- Added MDMS for Private member list, resolutions
 
## [0.0.54] - 11-04-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Fix
-  Updated Half an hour discussion
 
## [0.0.53] - 09-04-2025
 
[QNA-159] (https://eniyamasabha.atlassian.net/browse/QNA-159)
 
### Add
- Added ui component styling
 
## [0.0.52] - 10-04-2025
 
[QNA-166] (https://eniyamasabha.atlassian.net/browse/QNA-166)
 
### Add
-  Pipeline Fail fix
 
## [0.0.51] - 09-04-2025
 
[QNA-166] (https://eniyamasabha.atlassian.net/browse/QNA-166)
 
### Add
-  Added unit tests for My notice listing page
-  Added notice creation modal
 
## [0.0.50] - 10-04-2025
 
[QNA-168] (https://eniyamasabha.atlassian.net/browse/QNA-168)
 
### Add
-  Added short notice creating page with validations
 
## [0.0.49] - 08-04-2025
 
[QNA-166] (https://eniyamasabha.atlassian.net/browse/QNA-166)
 
### Add
-  Added My notice listing page with Mock API
 
 
## [0.0.48] - 05-04-2025
 
[QNA-129] (https://eniyamasabha.atlassian.net/browse/QNA-129)
 
### Add
-  Added Mock db for Members
 
## [0.0.47] - 05-04-2025
 
[QNA-138] (https://eniyamasabha.atlassian.net/browse/QNA-138)
 
### Add
-  Create Delayed Answer Bulletin page
 
 
## [0.0.46] - 04-04-2025
 
[QNA-112] (https://eniyamasabha.atlassian.net/browse/QNA-112)
 
### Add
-  Create short notice question create/edit page
 
## [0.0.45] - 04-04-2025
 
[QNA-137] (https://eniyamasabha.atlassian.net/browse/QNA-137)
 
### Fix
- Update api with mock endpoint to avoid conflict
 
## [0.0.44] - 04-04-2025
 
[QNA-137] (https://eniyamasabha.atlassian.net/browse/QNA-137)
 
### Add
- Added Late Answer page with mock API for creating/fetching documents
- Implemented reusable MultiHeaderDataTable and AnswerBulletinHeader components
 
## [0.0.43] - 04-04-2025
 
[QNA-140] (https://eniyamasabha.atlassian.net/browse/QNA-140)
 
### Add
-  Unit Test and Refactoring - My notices and Notice bank components
 
## [0.0.42] - 04-04-2025
 
[QNA-139] (https://eniyamasabha.atlassian.net/browse/QNA-139)
 
### Add
- Added Unit Testing and code Refactoring
 
## [0.0.41] - 29-03-2025
 
[QNA-128] (https://eniyamasabha.atlassian.net/browse/QNA-128)
 
### Fix
- Table Date picker alignment issue
 
## [0.0.40] - 28-03-2025
 
[QNA-49] (https://eniyamasabha.atlassian.net/browse/QNA-49)
 
### Fix
- update API base URL to remove trailing slash
 
## [0.0.39] - 28-03-2025
 
[QNA-113] (https://eniyamasabha.atlassian.net/browse/QNA-113)
 
### Add
- Half an Hour Discussion component
 
## [0.0.38] - 28-03-2025
 
[QNA-129] (https://eniyamasabha.atlassian.net/browse/QNA-129)
 
### Add
- Added Multi select dropdown component in Question Notice creation page.
 
## [0.0.37] - 24-03-2025
 
[QNA-109] (https://eniyamasabha.atlassian.net/browse/QNA-109)
 
### Modified
- Fixed reordering of the group in MDG
 
## [0.0.36] - 26-03-2025
 
[QNA-111] (https://eniyamasabha.atlassian.net/browse/QNA-111)
 
### Fix
- Integrated Notice Bank with Mock APIs
- Fixed My Notices Filter
- Added Vitest for the components
 
## [0.0.35] - 27-03-2025
 
[QNA-144] (https://eniyamasabha.atlassian.net/browse/QNA-144)
 
### Add
- Updated AOD submit flow
 
 
## [0.0.34] - 26-03-2025
 
[QNA-51] (https://eniyamasabha.atlassian.net/browse/QNA-51)
 
### Fix
- Added error toast for already existing ballot
- Integrated the cancel ballot
 
## [0.0.33] - 24-03-2025
 
[QNA-63] (https://eniyamasabha.atlassian.net/browse/QNA-63)
 
### Fix
- Fixed search Text issue
 
## [0.0.32] - 21-03-2025
 
[QNA-49] (https://eniyamasabha.atlassian.net/browse/QNA-49)
 
### Fix
- Fixed Routing for Notice Question create page & minor issues
 
## [0.0.31] - 21-03-2025
 
[QNA-111] (https://eniyamasabha.atlassian.net/browse/QNA-111)
### Added
- Filter functionality
 
## [0.0.30] - 21-03-2025
 
[QNA-120] (https://eniyamasabha.atlassian.net/browse/QNA-120)
 
### Fix
- Fix the assembly updates
## [0.0.29] - 21-03-2025
 
[QNA-109] (https://eniyamasabha.atlassian.net/browse/QNA-109)
 
### Added
- Saving state indication for MDG
- Submit API and functionality for MDG
 
### Modified
- Refactored MDG with initial group creation and button validation UI improvements
- Implemented frontend validations:
  - Ensure a new group (Group 1) is created by default when the user lands on the page
  - Prevent submission until all ministers are added to at least one group
  - Disallow empty groups
 
## [0.0.28] - 20-03-2025
 
[QNA-120] (https://eniyamasabha.atlassian.net/browse/QNA-120)
 
### Fix
- Fix the SOA updates
 
## [0.0.27] - 20-03-2025
 
[QNA-60] (https://eniyamasabha.atlassian.net/browse/QNA-60)
 
### Add
 
- Added AOD integration and ui fixes
 
 
## [0.0.26] - 20-03-2025
 
[QNA-121] (https://eniyamasabha.atlassian.net/browse/QNA-121)
 
### Fix
 
- Implement a search feature for the consent list.
- Add a status tag for the "Request Sent" tab list.
- Enable language switching.
 
## [0.0.25] - 20-03-2025
 
[QNA-110] (https://eniyamasabha.atlassian.net/browse/QNA-110)
 
### Added
 
-The document create pop up  linked to all possible pages.
- Put in place holder routes which are not developed
 
## [0.0.24] - 20-03-2025
 
[QNA-51] (https://eniyamasabha.atlassian.net/browse/QNA-51)
 
### Fix
 
- Fixed the UI for ballot result page
- Fixed the POST for ballot result
 
## [0.0.23] - 20-03-2025
 
[QNA-64] (https://eniyamasabha.atlassian.net/browse/QNA-64)
 
## Fix
 
- Fields Updated , url changes and code cleaned.
 
 
## [0.0.22] - 19-03-2025
 
[QNA-63] (https://eniyamasabha.atlassian.net/browse/QNA-63)
 
## Fix
 
- Filter dropdown Updated
 
## [0.0.22] - 19-03-2025
 
[QNA-120] (https://eniyamasabha.atlassian.net/browse/QNA-120)
 
## Fix
- Fix the SOA updates
 
## [0.0.21] - 19-03-2025
 
[QNA-64] (https://eniyamasabha.atlassian.net/browse/QNA-64)
 
## Fix
 
- Filter Updated
 
## [0.0.20] - 18-03-2025
 
[QNA-49] (https://eniyamasabha.atlassian.net/browse/QNA-49)
 
### Modified
 
- ui fixes and routes
 
## [0.0.19] - 17-03-2025
 
[QNA-60] (https://eniyamasabha.atlassian.net/browse/QNA-60)
 
### Added
 
- AOD create page integration and ui fixes
 
## [0.0.18] - 17-03-2025
 
[QNA-64] (https://eniyamasabha.atlassian.net/browse/QNA-64)
 
## Added
 
- Api integrations, Filtering
- ui updates
 
## [0.0.17] - 17-03-2025
 
[QNA-63] (https://eniyamasabha.atlassian.net/browse/QNA-63)
 
### Added
 
- All documents page integration, fixes & Unit testing
 
## [0.0.16] - 17-03-2025
 
[QNA-49] (https://eniyamasabha.atlassian.net/browse/QNA-49)
 
### Added
 
- Basic details & Notice details creation page and API integration for Basic Details page.
 
## [0.0.15] - 15-03-2025
 
[QNA-78] (https://eniyamasabha.atlassian.net/browse/QNA-78)
 
### Fixes
 
- Handlers of SOA removed
- QuestionsDay hand picker removed
 
## [0.0.14] - 14-03-2025
 
[QNA-47] (https://eniyamasabha.atlassian.net/browse/QNA-47)
 
### Added
 
- My notices page, Notice bank for MLA/PPO Login
- Integrated my noticr and notice bank page with available APIs
 
## [0.0.13] - 15-03-2025
 
[QNA-48] (https://eniyamasabha.atlassian.net/browse/QNA-48)
 
### Added
 
- Consent page for MLA/PPO Login
 
## [0.0.12] - 14-03-2025
 
[QNA-59] (https://eniyamasabha.atlassian.net/browse/QNA-59)
 
### Added
 
- Integrated the MDG create page with available APIs
- Refactored the code and fixed the styles with typography and font thems.
 
## [0.0.11] - 14-03-2025
 
[QNA-77] (https://eniyamasabha.atlassian.net/browse/QNA-77)
 
### Fixes
 
- Fixed minor changes in SOA
 
## [0.0.10] - 12-03-2025
 
[QNA-61] (https://eniyamasabha.atlassian.net/browse/QNA-61)
 
### Added
 
- SOA create page integration and fixes
 
## [0.0.9] - 12-03-2025
 
[QNA-62] (https://eniyamasabha.atlassian.net/browse/QNA-62)
 
### Added
 
- Integrated the document create pop up with available APIs.
- Populated the dropdown with correct document types and routing
- Test case added
 
## [0.0.8] - 09-03-2025
 
[QNA-18](https://eniyamasabha.atlassian.net/browse/QNA-18)
 
### Added
 
- Implemented Initial SOA UI.
- Added mock API for SOA creation.
 
## [0.0.7] - 08-03-2025
 
[QNA-17](https://eniyamasabha.atlassian.net/browse/QNA-17)
 
### Added
 
- Implemented AOD UI.
- Added mock API for AOD creation.
 
## [0.0.6] - 08-03-2025
 
[QNA-16](https://eniyamasabha.atlassian.net/browse/QNA-16)
 
### Added
 
- Implemented Minister Group Creation UI with drag-and-drop.
- Added mock API for Minister Group management.
 
## [0.0.5] - 07-03-2025
 
[QNA-50](https://eniyamasabha.atlassian.net/browse/QNA-50)
 
### Added
 
- Added New page for listing ballots.
- Added New Handler, services also for the implementation.
 
## [0.0.4] - 05-03-2025
 
### Added
 
- Added Create document pop up
 
## [0.0.3] - 05-03-2025
 
### Added
 
- Added All Documents List
 
## [0.0.2] - 04-03-2025
 
### Added
 
- Added folder structure
 
## [0.0.1] - 24-02-2025
 
### Added
 
- Initial Setup
- Husky Installed
- Ui Components Imported