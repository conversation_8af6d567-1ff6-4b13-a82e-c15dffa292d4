{"name": "question-section-app", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "vite", "dev:local": "vite --mode localhost", "dev:prod": "vite --mode production", "build": "vite build", "build:dev": "vite build --mode development", "build:local": "vite build --mode localhost", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui --coverage", "coverage": "vitest run --coverage", "prepare": "husky", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css}\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@kla-v2/ui-components": "^0.0.130", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-slot": "^1.1.1", "@reduxjs/toolkit": "^2.3.0", "@testing-library/user-event": "^14.5.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cpl-section-app": "file:", "date-fns": "^4.1.0", "html2pdf.js": "^0.10.3", "i18next": "^24.2.0", "lucide-react": "^0.454.0", "pdfjs-dist": "^4.8.69", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-i18next": "^15.2.0", "react-pdf": "^9.1.1", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.6", "react-router-dom": "^6.27.0", "tailwind-merge": "^2.5.4", "uuid": "^11.1.0", "uuid4": "^2.0.3", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.20.0", "@mswjs/data": "^0.16.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "msw": "^2.6.0", "postcss": "^8.4.47", "redux-mock-store": "^1.5.5", "tailwindcss": "^3.4.14", "vite": "^5.4.13", "vitest": "^2.1.8"}, "lint-staged": {"*.{js,jsx}": "eslint"}, "msw": {"workerDirectory": ["public"]}}