import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const starredQuestionsApi = createApi({
  reducerPath: "starredQuestions",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api`,
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getStarredQuestions: builder.query({
      query: (date) => {
        return {
          url: `/documents-mockStarred`,
          params: { date },
        };
      },
      providesTags: ["starredQuestions"],
    }),
  }),
});

export const { useGetStarredQuestionsQuery } = starredQuestionsApi;
