import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const delayedAnswerBulletinApi = createApi({
  reducerPath: "delayedAnswerBulletin",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api`,
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  endpoints: (builder) => ({
    createDelayedAnswerBulletin: builder.mutation({
      query: ({ data }) => {
        return {
          url: "documents/draft",
          method: "POST",
          body: JSON.stringify(data),
          headers: {
            "Content-Type": "application/json",
          },
        };
      },
      invalidatesTags: ["DelayedAnswerBulletin"],
    }),
    getDelayedAnswerBulletin: builder.query({
      query: (documentId) => {
        return {
          url: `/documents/${documentId}`,
        };
      },
      providesTags: ["DelayedAnswerBulletin"],
    }),
  }),
});

export const {
  useCreateDelayedAnswerBulletinMutation,
  useGetDelayedAnswerBulletinQuery,
} = delayedAnswerBulletinApi;
