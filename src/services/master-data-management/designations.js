import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const designationsApi = createApi({
  reducerPath: "designationsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["Designations"],
  endpoints: (builder) => ({
    getDesignations: builder.query({
      query: () => "/designations", // Endpoint to fetch designations
      providesTags: ["Designations"], // Tag for caching
    }),
  }),
});

export const { useGetDesignationsQuery } = designationsApi;
