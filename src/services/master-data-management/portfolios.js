import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const portfoliosApi = createApi({
  reducerPath: "portfoliosApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["Portfolios"],
  endpoints: (builder) => ({
    getPortfolios: builder.query({
      query: () => "/portfolio-subjects", // Endpoint to fetch portfolios
      providesTags: ["Portfolios"], // Tag for caching
    }),
  }),
});

export const { useGetPortfoliosQuery } = portfoliosApi;
