import { createApiService } from "@/utils/api-utils";
import { createSelector } from "@reduxjs/toolkit";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

// Create the RTK Query service
export const activeAssemblyApi = createApiService({
  reducerPath: "activeAssemblyApi",
  baseUrl: BASE_URLS.MDMS,
  tagTypes: [TAG_TYPES.ASSEMBLY],
  endpoints: (builder) => ({
    getActiveAssembly: builder.query({
      query: () => ({
        url: `api/active-assembly`,
      }),
      providesTags: [TAG_TYPES.ASSEMBLY],
      // Transform the response to match the format expected by consumers
      transformResponse: (response) =>
        response || { assembly: "15", session: "13" },
    }),
  }),
});

export const { useGetActiveAssemblyQuery } = activeAssemblyApi;

// Memoized selector to maintain compatibility with existing code
export const selectActiveAssemblyItems = createSelector(
  // Input selector - gets the data from the RTK Query cache
  (state) => {
    const result =
      activeAssemblyApi.endpoints.getActiveAssembly.select()(state);
    return result?.data;
  },
  // Output selector - transforms the data if needed
  (data) => data || { assembly: "15", session: "13" },
);
