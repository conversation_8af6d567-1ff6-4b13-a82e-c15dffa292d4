import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const subSubjectsApi = createApi({
  reducerPath: "subSubjectsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["SubSubjects"],
  endpoints: (builder) => ({
    getSubSubjects: builder.query({
      query: () => "/sub-subjects", // Endpoint to fetch sub-subjects
      providesTags: ["SubSubjects"], // Tag for caching
    }),
  }),
});

export const { useGetSubSubjectsQuery } = subSubjectsApi;
