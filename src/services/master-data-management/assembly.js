import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const assemblyApi = createApi({
  reducerPath: "assemblySessionApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["Assembly", "Session"],
  endpoints: (builder) => ({
    getAssembly: builder.query({
      query: () => "assembly",
      providesTags: ["Assembly"],
    }),
    getSession: builder.query({
      query: () => "session",
      providesTags: ["Session"],
    }),
  }),
});

export const { useGetAssemblyQuery, useGetSessionQuery } = assemblyApi;
