import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const questionNoticeApi = createApi({
  reducerPath: "questionNotice",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["QuestionNotice", "QuestionNoticeBank", "NoticeDraft"],
  endpoints: (builder) => ({
    getQuestionNoticeList: builder.query({
      query: ({
        searchText,
        noticeNumber,
        ministerDesignation,
        portfolio,
        members,
        starred,
        clubbed,
        assembly,
        session,
        status,
        noticePriority,
        createdDate,
        createdDateStartDate,
        createdDateEndDate,
        questionDate,
        questionDateStartDate,
        questionDateEndDate,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc",
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_QUESTION",
          ...(searchText && { searchText }),
          ...(noticeNumber && { noticeNumber }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(members && { members }),
          ...(starred !== undefined && starred !== "" && { starred }),
          ...(clubbed !== undefined && clubbed !== "" && { clubbed }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(status && { status }),
          ...(noticePriority && { noticePriority }),
          ...(createdDate && { createdDate }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDate && { questionDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
        };

        return {
          url: `api/notices/my-notices`,
          params,
        };
      },
      providesTags: ["QuestionNotice"],
    }),

    getQuestionNoticeBank: builder.query({
      query: ({
        searchText,
        noticeNumber,
        ministerDesignation,
        portfolio,
        starred,
        assembly,
        session,
        noticePriority,
        createdDate,
        createdDateStartDate,
        createdDateEndDate,
        questionDate,
        questionDateStartDate,
        questionDateEndDate,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc",
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_QUESTION",
          ...(searchText && { searchText }),
          ...(noticeNumber && { noticeNumber }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(starred !== undefined && starred !== "" && { starred }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(noticePriority && { noticePriority }),
          ...(createdDate && { createdDate }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDate && { questionDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
        };

        return {
          url: `api/notices/notice-bank`,
          params,
        };
      },
      providesTags: ["QuestionNoticeBank"],
    }),

    saveBasicDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-questions/${id}/basic-details`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["QuestionNotice"],
    }),

    saveNoticeDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-questions/${id}/notice-details`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["QuestionNotice"],
    }),

    withdrawNotice: builder.mutation({
      query: (id) => ({
        url: `api/notice-for-questions/${id}/withdraw`,
        method: "POST",
      }),
      invalidatesTags: ["QuestionNotice"],
    }),

    getMembersByQuestionDate: builder.query({
      query: (questionDate) => ({
        url: `api/notice-for-questions/members-by-date`,
        params: { questionDate },
      }),
    }),

    getQuestionNumbers: builder.query({
      query: (questionDate) => ({
        url: `api/notice-for-questions/question-numbers`,
        params: { questionDate },
      }),
    }),

    updateQuestionsToLab: builder.mutation({
      query: (noticeIds) => ({
        url: `api/notice-for-questions/update-questions-to-lab`,
        method: "POST",
        body: { noticeIds },
      }),
      invalidatesTags: ["QuestionNotice"],
    }),

    createNoticeDraft: builder.mutation({
      query: (data) => ({
        url: `api/documents/draft`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["NoticeDraft"],
    }),

    submitDocument: builder.mutation({
      query: (id) => ({
        url: `api/documents/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: ["QuestionNotice", "QuestionNoticeBank"],
    }),

    getDocumentById: builder.query({
      query: (id) => `api/documents/${id}`,
      providesTags: (result, error, id) => [{ type: "QuestionNotice", id }],
    }),
  }),
});

export const {
  useGetQuestionNoticeListQuery,
  useGetQuestionNoticeBankQuery,
  useSaveBasicDetailsMutation,
  useSaveNoticeDetailsMutation,
  useWithdrawNoticeMutation,
  useGetMembersByQuestionDateQuery,
  useGetQuestionNumbersQuery,
  useUpdateQuestionsToLabMutation,
  useCreateNoticeDraftMutation,
  useSubmitDocumentMutation,
  useGetDocumentByIdQuery,
} = questionNoticeApi;
