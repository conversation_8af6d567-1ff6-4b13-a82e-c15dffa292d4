import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const documentsApi = createApi({
  reducerPath: "documents",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api",
    tagTypes: ["Document"],
  }),

  endpoints: (builder) => ({
    getDocumentList: builder.query({
      query: (params = {}) => {
        const url = `/documents/search`;
        return { url, params };
      },
      providesTags: ["Document"],
    }),

    getDocumentById: builder.query({
      query: ({ documentId, params }) => {
        const url = `/documents/${documentId}`;
        return {
          url,
          params,
        };
      },
      providesTags: ["Document"],
    }),

    updateDocument: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}`;
        return {
          url,
          method: "PATCH",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),

    submitDocument: builder.mutation({
      query: ({ documentId }) => {
        const url = `/documents/${documentId}/submit`;
        return {
          url,
          method: "POST",
        };
      },
      invalidatesTags: ["Document"],
    }),
    updateDocumentActlaidDays: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/days-to-lay`;
        return {
          url,
          method: "PATCH",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),
    addDocumentActReferences: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/act-references`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),
    getSroDocumentView: builder.query({
      query: ({ documentId }) => {
        const url = `/documents/sro/${documentId}`;
        return {
          url,
        };
      },
      providesTags: ["Document"],
    }),
    addReferenceSro: builder.mutation({
      query: ({ data }) => {
        const url = `/documents/reference-sro`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),
    getMinisterDetailsView: builder.query({
      query: ({ documentId }) => {
        const url = `/documents/${documentId}/minister-details`;
        return {
          url,
        };
      },
      providesTags: ["Document"],
    }),

    addMinisterDetails: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/minister-details`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),

    addCoveringLetter: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/covering-letter`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),

    addDelayStatement: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/delay-statement`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),
    forwardDocument: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/documents/${documentId}/forward`;
        return {
          url,
          method: "PATCH",
          body: data,
        };
      },
      invalidatesTags: ["Document"],
    }),
    createDocumentDraft: builder.mutation({
      query: ({ data }) => {
        const url = `/documents/draft`;
        return {
          url,
          method: "POST",
          body: JSON.stringify(data),
          headers: {
            "Content-Type": "application/json",
          },
        };
      },
      invalidatesTags: ["Document"],
    }),
    getMetadataDetails: builder.query({
      query: ({ documentId }) => `/documents/${documentId}/metadata`,
      providesTags: ["Document"],
    }),
  }),
});

export const {
  useUpdateDocumentActlaidDaysMutation,
  useAddDocumentActReferencesMutation,
  useGetDocumentByIdQuery,
  useGetDocumentListQuery,
  useGetMinisterDetailsViewQuery,
  useGetSroDocumentViewQuery,
  useLazyGetDocumentByIdQuery,
  useLazyGetDocumentListQuery,
  useSubmitDocumentMutation,
  useUpdateDocumentMutation,
  useAddReferenceSroMutation,
  useAddMinisterDetailsMutation,
  useAddCoveringLetterMutation,
  useAddDelayStatementMutation,
  useForwardDocumentMutation,
  useCreateDocumentDraftMutation,
  useGetMetadataDetailsQuery,
} = documentsApi;
