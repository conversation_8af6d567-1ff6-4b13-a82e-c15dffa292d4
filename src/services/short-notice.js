import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
const { VITE_API_BASE_URL } = import.meta.env;

export const shortNoticeApi = createApi({
  reducerPath: "shortNoticeApi",
  baseQuery: fetchBaseQuery({
    baseUrl: new URL(VITE_API_BASE_URL + "api/", location.origin).href,
    tagTypes: ["ShortNotices"],
  }),
  endpoints: (builder) => ({
    getShortNotice: builder.query({
      query: ({ documentId }) => ({
        url: `/documents/${documentId}`,
        method: "GET",
      }),
      providesTags: ["ShortNotices"],
    }),

    postBasicDetails: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/notice-for-short-notices/${documentId}/basic-details`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ShortNotices"],
    }),

    postNoticeDetails: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/notice-for-short-notices/${documentId}/notice-details`;
        return {
          url,
          body: data,
          method: "POST",
        };
      },
      invalidatesTags: ["ShortNotices"],
    }),

    postExplanatoryDetails: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/notice-for-short-notices/${documentId}/explanatory-note`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ShortNotices"],
    }),

    digitalSignature: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/notice-for-short-notices/${documentId}/signature`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ShortNotices"],
    }),

    shortNoticeSubmit: builder.mutation({
      query: ({ documentId }) => ({
        url: `/documents/${documentId}/submit`,
        method: "POST",
      }),
      invalidatesTags: ["ShortNotices"],
    }),
  }),
});

export const {
  useGetShortNoticeQuery,
  usePostBasicDetailsMutation,
  usePostNoticeDetailsMutation,
  usePostExplanatoryDetailsMutation,
  useDigitalSignatureMutation,
  useShortNoticeSubmitMutation,
} = shortNoticeApi;
