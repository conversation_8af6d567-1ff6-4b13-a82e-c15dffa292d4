import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const settingOfQuestionsUnstarredApi = createApi({
  reducerPath: "settingOfQuestionsUnstarred",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api/",
  }),

  endpoints: (builder) => ({
    getSettingOfQuestionsUnstarred: builder.query({
      query: ({
        search = "",
        questionDate = "",
        category = "",
        commentsPending = "",
        status = "",
        questionDateStartDate = "",
        questionDateEndDate = "",
        page = 0,
        size = 10,
      }) => {
        const params = {
          page: page + 1,
          size,
        };

        if (search) params.search = search;
        if (questionDate) params.questionDate = questionDate;
        if (category) params.category = category;
        if (commentsPending) params.commentsPending = commentsPending;
        if (status) params.status = status;
        if (questionDateStartDate)
          params.questionDateStartDate = questionDateStartDate;
        if (questionDateEndDate)
          params.questionDateEndDate = questionDateEndDate;

        return {
          url: "setting-of-questions-unstarred",
          params,
        };
      },
      providesTags: ["SettingOfQuestionsUnstarred"],
    }),
  }),
});

export const { useGetSettingOfQuestionsUnstarredQuery } =
  settingOfQuestionsUnstarredApi;
