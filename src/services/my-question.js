import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const myQuestionApi = createApi({
  reducerPath: "myQuestionApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api/`,
  }),
  tagTypes: ["Question"],
  endpoints: (builder) => ({
    // 🔹 List Questions
    getQuestionsList: builder.query({
      query: (limit) =>
        limit ? `documents-questions/?limit=${limit}` : `documents-questions/`,
      providesTags: (result) =>
        result
          ? [
              ...result.map((item) => ({ type: "Question", id: item.id })),
              { type: "Question", id: "LIST" },
            ]
          : [{ type: "Question", id: "LIST" }],
    }),

    // 🔹 Get Question by ID
    getQuestionById: builder.query({
      query: (documentId) => `documents-questions/${documentId}`,
      providesTags: (result, error, documentId) => [
        { type: "Question", id: documentId },
      ],
    }),
  }),
});

export const { useGetQuestionsListQuery, useGetQuestionByIdQuery } =
  myQuestionApi;
