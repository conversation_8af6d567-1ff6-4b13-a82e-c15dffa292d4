import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, afterAll, afterEach, beforeAll } from "vitest";
import { setupServer } from "msw/node";
import { http, HttpResponse } from "msw";
import { MemoryRouter } from "react-router-dom";
import NoticeBank from "../notice-for-question/notice-bank";
import { Provider } from "react-redux";
import { store } from "../../app/store";

const mockNoticeBankData = {
  content: [
    {
      id: 1,
      currentNumber: "101",
      noticeHeading: "Budget Discussion",
      ministerDesignation: "Finance Minister",
      portfolio: "Finance",
      assembly: "17th Assembly",
    },
    {
      id: 2,
      currentNumber: "102",
      noticeHeading: "Education Policy",
      ministerDesignation: "Education Minister",
      portfolio: "Education",
      assembly: "17th Assembly",
    },
  ],
  totalElements: 2,
  totalPages: 1,
};

const mockEmptyNoticeBankData = {
  content: [],
  totalElements: 0,
  totalPages: 0,
};

const mockPortfolioData = [
  { title: "Finance" },
  { title: "Education" },
  { title: "Healthcare" },
];

const mockDesignationData = [
  { title: "Finance Minister" },
  { title: "Education Minister" },
  { title: "Health Minister" },
];

const server = setupServer(
  http.get("*/api/notice-bank-for-questions/my-notices*", ({ request }) => {
    const url = new URL(request.url);
    const search = url.searchParams.get("search");

    if (search === "nonexistent") {
      return HttpResponse.json(mockEmptyNoticeBankData);
    }

    if (search === "error") {
      return new HttpResponse(null, { status: 500 });
    }

    return HttpResponse.json(mockNoticeBankData);
  }),
  http.get("*/api/mdm/portfolio*", () => {
    return HttpResponse.json(mockPortfolioData);
  }),
  http.get("*/api/mdm/designation*", () => {
    return HttpResponse.json(mockDesignationData);
  }),
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe("NoticeBank Component", () => {
  const renderComponent = (initialPath = "/") => {
    return render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[initialPath]}>
          <NoticeBank />
        </MemoryRouter>
      </Provider>,
    );
  };

  it("clears search input on Escape", async () => {
    renderComponent();
    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Test" } });
    fireEvent.keyDown(searchInput, { key: "Escape", code: "Escape" });
    expect(searchInput.value).toBe("");
  });

  it("filters table by search input", async () => {
    renderComponent();
    const searchInput = screen.getByPlaceholderText("Search");

    fireEvent.change(searchInput, { target: { value: "Budget" } });

    await waitFor(
      () => {
        expect(searchInput.value).toBe("Budget");
      },
      { timeout: 2000 },
    );
  });

  it.skip("changes page size", async () => {
    renderComponent();

    await waitFor(
      () => {
        const pageSizeSelect = screen.getByRole("combobox");
        expect(pageSizeSelect).toBeInTheDocument();

        fireEvent.change(pageSizeSelect, { target: { value: "50" } });
        expect(pageSizeSelect.value).toBe("50");
      },
      { timeout: 2000 },
    );
  });

  it("respects URL search params for initial state", async () => {
    renderComponent("/?search=Budget&page=1&size=10");

    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText("Search");
      expect(searchInput.value).toBe("Budget");
    });
  });

  it("shows empty state when no data is available", async () => {
    renderComponent();

    server.use(
      http.get("*/api/notice-bank-for-questions/my-notices*", () => {
        return HttpResponse.json(mockEmptyNoticeBankData);
      }),
    );

    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "nonexistent" } });
  });
});
