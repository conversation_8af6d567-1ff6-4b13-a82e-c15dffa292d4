import { formatDate } from "@/utils";

/**
 * Maps API response data to table row format
 * @param {Object} data - API response with content array
 * @returns {Array} - Formatted data for table display
 */
export const mapQuestionNoticeData = (data) => {
  if (!data?.content || !Array.isArray(data.content)) {
    return [];
  }

  console.log("Question notice data:", data.content);

  return data.content.map((item) => ({
    id: item.id,
    noticeNumber: item.currentNumber || item.noticeNumber,
    clubbed:
      item.secondaryMembers && item.secondaryMembers.length > 0 ? "Yes" : "No",
    category: item.starred ? "Starred" : "unStarred",
    noticeHeading: item.name || item.noticeHeading,
    ministerDesignation: item.ministerDesignation || "",
    ministerDesignationInLocal: item.ministerDesignationInLocal || "",
    portfolio: item.portfolio || "",
    portfolioInLocal: item.portfolioInLocal || "",
    createdDate: item.createdAt ? formatDate(new Date(item.createdAt)) : "",
    questionDate: item.questionDate
      ? formatDate(new Date(item.questionDate))
      : "",
    noticePriority: item.noticePriority,
    status: item.status,
  }));
};

/**
 * Maps API response data to notice bank table row format
 * @param {Object} data - API response with content array
 * @returns {Array} - Formatted data for table display
 */
export const mapNoticeBankData = (data) => {
  if (!data?.content || !Array.isArray(data.content)) {
    return [];
  }

  console.log("Notice bank data:", data.content);

  return data.content.map((item) => ({
    id: item.id,
    noticeNumber: item.currentNumber || item.noticeNumber,
    noticeHeading: item.name || item.noticeHeading,
    ministerDesignation: item.ministerDesignation || "",
    ministerDesignationInLocal: item.ministerDesignationInLocal || "",
    portfolio: item.portfolio || "",
    portfolioInLocal: item.portfolioInLocal || "",
    noticePriority: item.noticePriority,
    questionDate: item.questionDate
      ? new Date(item.questionDate).toLocaleDateString()
      : "",
  }));
};
