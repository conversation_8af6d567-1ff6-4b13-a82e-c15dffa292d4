import { mapNoticeBankData } from "./mapper";
import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { buildRoute } from "@/config/routes";
import { Pencil, Search, Delete, Eye } from "lucide-react";
import PropTypes from "prop-types";
import {
  DataTable,
  TableDropdownMenu,
  Input,
  FilterDropdown,
  PaginationSelect,
  Paginator,
} from "@kla-v2/ui-components";
import { useGetQuestionNoticeBankQuery } from "../../services/question-notice";
import {
  useGetPortfolioQuery,
  useGetDesignationQuery,
} from "../../services/master-data-management/basic-details-notice";
import {
  useGetAssemblyQuery,
  useGetSessionQuery,
} from "../../services/master-data-management/assembly";
import { useLanguage, useDebounce } from "@/hooks";
import { formatDateYYYYMMDD } from "@/utils";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useSelector } from "react-redux";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";

function NoticeBank() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || "",
  );
  const [filteredData, setFilteredData] = useState({});

  const debouncedSearchValue = useDebounce(searchInput, 500);

  const page = parseInt(searchParams.get("page") || "1") - 1;
  const size = parseInt(searchParams.get("size") || "10");

  const searchQuery = searchParams.get("search") || "";
  const activeAssembly = useSelector(selectActiveAssemblyItems);

  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: designationData } = useGetDesignationQuery();
  const { data: assemblyData } = useGetAssemblyQuery();
  const { data: sessionData } = useGetSessionQuery();

  const filters = useMemo(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      return {
        Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
        Session: { filter: "Session", value: activeAssembly?.session },
      };
    }
    return {};
  }, [activeAssembly?.assembly, activeAssembly?.session]);

  const filteredDataRef = useRef(filteredData);

  useEffect(() => {
    filteredDataRef.current = filteredData;
  }, [filteredData]);

  useEffect(() => {
    if (
      Object.keys(filters).length > 0 &&
      Object.keys(filteredDataRef.current).length === 0
    ) {
      setFilteredData(filters);
    }
  }, [filters]);

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      const { filter, value } = filterData;

      switch (key) {
        case "ministerDesignation":
          queryParams["ministerDesignation"] =
            typeof value === "object" && value !== null
              ? value.Designation || ""
              : value || "";
          break;
        case "portfolio":
          queryParams["portfolio"] =
            typeof value === "object" && value !== null
              ? value.Portfolio || ""
              : value || "";
          break;
        case "noticePriority":
          queryParams["noticePriority"] =
            typeof value === "object" && value !== null
              ? value.Priority || ""
              : value || "";
          break;
        case "Assembly":
          queryParams["assembly"] =
            typeof value === "object" && value !== null
              ? value.KLA || ""
              : value || "";
          break;
        case "Session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.Session || ""
              : value || "";
          break;
        case "category":
          queryParams["starred"] =
            typeof value === "object" && value !== null
              ? value.category === "Starred"
              : value === "Starred";
          break;
        case "createdDate":
          handleDateFilter(queryParams, filter, value, "createdDate");
          break;
        case "questionDate":
          handleDateFilter(queryParams, filter, value, "questionDate");
          break;
        default:
          break;
      }
    });

    return queryParams;
  };

  const handleDateFilter = (queryParams, filter, value, dateType) => {
    if (filter === "Is" && value) {
      queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value);
      queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value);
    } else if (filter === "between" && value) {
      if (value.from) {
        queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value.from);
      }
      if (value.to) {
        queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value.to);
      }
    }
  };

  const { data: noticeBankData, isLoading: noticeBankLoading } =
    useGetQuestionNoticeBankQuery({
      searchText: searchQuery,
      ...processFilters(filteredData),
      page,
      size,
    });

  const handleViewDetails = useCallback(
    (id) => {
      navigate(buildRoute.memberNoticeForQuestion(id));
    },
    [navigate],
  );

  const handleEdit = useCallback(
    (id) => {
      navigate(buildRoute.questionNotice(id, 'edit'));
    },
    [navigate],
  );

  const handleDelete = useCallback((id) => {
    // In a real app, you would dispatch a delete action here
    console.log(`Delete notice with ID: ${id}`);
  }, []);

  const noticeBankTableData = mapNoticeBankData(noticeBankData);

  const ActionCell = useCallback(
    ({ row }) => {
      const id = row.original.id;

      const menuItems = [
        {
          label: (
            <div className="flex items-center gap-2">
              <Eye size={16} /> View Details
            </div>
          ),
          onClick: () => handleViewDetails(id),
        },
        {
          label: (
            <div className="flex items-center gap-2">
              <Pencil size={16} /> Edit
            </div>
          ),
          onClick: () => handleEdit(id),
        },
        {
          label: (
            <div className="flex items-center gap-2">
              <Delete size={16} /> Delete
            </div>
          ),
          onClick: () => handleDelete(id),
        },
      ];

      return <TableDropdownMenu row={row} menuItems={menuItems} />;
    },
    [handleViewDetails, handleEdit, handleDelete],
  );

  ActionCell.propTypes = {
    row: PropTypes.shape({
      original: PropTypes.object.isRequired,
    }).isRequired,
  };

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }

        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (newParams.search !== undefined) {
        if (newParams.search) {
          updatedParams.set("search", newParams.search);
        } else {
          updatedParams.delete("search");
        }
      }

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchKeyDown = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ search: "" });
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      updateParams({ search: debouncedSearchValue });
    }
  }, [debouncedSearchValue, searchQuery, updateParams]);

  const getDisplayedPageInfo = () => {
    const totalElements = noticeBankData?.totalElements;
    if (!totalElements) return "";

    const startIndex = (page + 1) * size - (size - 1);
    const endIndex = Math.min((page + 1) * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const klaOptions =
    assemblyData?.map((kla) => ({
      label: kla.title,
      value: kla.title,
    })) || [];

  const sessionOptions =
    sessionData?.map((session) => ({
      label: session.title,
      value: session.title,
    })) || [];

  const designationOptions =
    designationData?.map((designation) => ({
      label: designation.title,
      value: designation.title,
    })) || [];

  const portfolioOptions =
    portfolioData?.map((portfolio) => ({
      label: portfolio.title,
      value: portfolio.title,
    })) || [];

  const categoryOptions = [
    { label: "Starred", value: "Starred" },
    { label: "Unstarred", value: "unStarred" },
  ];

  const priorityOptions = [
    { label: "P1", value: "P1" },
    { label: "P2", value: "P2" },
    { label: "P3", value: "P3" },
    { label: "NIL", value: "NIL" },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const noticeBankFilterOptions = [
    {
      label: t("table:Assembly"),
      submenu: [
        {
          comboboxOptions: klaOptions,
          label: "Assembly",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "KLA",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Assembly",
    },
    {
      label: t("table:Session"),
      submenu: [
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Session",
          truncateStringLength: 6,
          value: "Session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Session",
    },
    {
      label: t("Category"),
      submenu: [
        {
          comboboxOptions: categoryOptions,
          label: "Category",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Category",
          truncateStringLength: 6,
          value: "category",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "category",
    },
    {
      label: t("table:Designation"),
      submenu: [
        {
          comboboxOptions: designationOptions,
          label: "Designation",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Designation",
          truncateStringLength: 6,
          value: "Designation",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ministerDesignation",
    },
    {
      label: t("table:Portfolio"),
      submenu: [
        {
          comboboxOptions: portfolioOptions,
          label: "Portfolio",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Portfolio",
          truncateStringLength: 6,
          value: "Portfolio",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "portfolio",
    },
    {
      label: t("table:Priority"),
      submenu: [
        {
          comboboxOptions: priorityOptions,
          label: "Priority",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Priority",
          truncateStringLength: 6,
          value: "Priority",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "noticePriority",
    },
    {
      label: t("Created Date"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "createdDate",
    },
    {
      label: t("Question Date"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
  ];

  const tableColumns = useMemo(
    () => [
      { accessorKey: "noticeNumber", header: "Notice No." },
      {
        accessorKey: "noticeHeading",
        header: "Notice Heading",
        meta: { className: "min-w-48" },
      },
      {
        accessorKey: "ministerDesignation",
        header: "Designation",
        cell: ({ row }) => {
          const value = row.getValue("ministerDesignation");
          const localValue = row.original.ministerDesignationInLocal;
          return localValue ? `${value} (${localValue})` : value;
        },
      },
      {
        accessorKey: "portfolio",
        header: "Portfolio",
        cell: ({ row }) => {
          const value = row.getValue("portfolio");
          const localValue = row.original.portfolioInLocal;
          return localValue ? `${value} (${localValue})` : value;
        },
      },
      { accessorKey: "questionDate", header: "Question Date" },
      { accessorKey: "noticePriority", header: "Priority" },
      {
        header: "Action",
        id: "action",
        cell: ActionCell,
        meta: { className: "text-center w-[120px]" },
      },
    ],
    [ActionCell],
  );

  const renderTableContent = () => {
    if (noticeBankLoading) {
      return <SpinnerLoader />;
    }

    return (
      <>
        <DataTable columns={tableColumns} data={noticeBankTableData} />

        <div className="flex justify-between mt-4">
          <div className="flex gap-4 items-center">
            <span className="typography-body-text-s-14 text-grey-600">
              {getDisplayedPageInfo()}
            </span>
            <PaginationSelect
              onChange={handlePageSizeChange}
              defaultValue={size.toString()}
              options={paginationOptions}
            />
          </div>
          <div>
            <Paginator
              currentPage={page + 1}
              totalPages={noticeBankData?.totalPages || 0}
              onPageChange={(newPage) => handlePageChange(newPage - 1)}
              showPreviousNext={true}
            />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div className="flex justify-between items-center mt-4">
        <div className="relative w-80">
          <Input
            icon={Search}
            className="w-full"
            reserveErrorSpace={false}
            placeholder="Search"
            size="md"
            value={searchInput}
            onChange={handleSearchInputChange}
            onKeyDown={handleSearchKeyDown}
          />
        </div>
        <div className="flex gap-2">
          <FilterDropdown
            badgeContainerClassName=""
            options={noticeBankFilterOptions}
            onValueChange={setFilteredData}
            disabled={noticeBankLoading}
            defaultValue={filters}
          />
        </div>
      </div>

      <div className="mt-4">{renderTableContent()}</div>
    </>
  );
}

export default NoticeBank;
