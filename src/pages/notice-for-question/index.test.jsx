import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import NoticeForQuestion from "../notice-for-question";
import { useLanguage } from "@/hooks";
import PropTypes from "prop-types";
import { toast } from "@kla-v2/ui-components";

const mockNavigate = vi.fn();
const mockCreateNoticeDraftMutation = vi.fn();
let mockLocation = { pathname: "/my-question-notices" };

const MockNavLink = ({ to, children }) => (
  <a href={to} data-testid={`navlink-${to.replace(/\//g, "-")}`}>
    {children}
  </a>
);

MockNavLink.propTypes = {
  to: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

const MockOutlet = () => <div data-testid="router-outlet">Outlet Content</div>;

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock("@/hooks/use-auto-breadcrumb", () => ({
  useAutoBreadcrumb: vi.fn(),
}));

vi.mock("@/hooks", () => ({
  useLanguage: vi.fn(),
}));

vi.mock("@kla-v2/ui-components", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    Button:
      actual.Button ||
      ((props) => <button {...props}>{props.children}</button>),
    toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
  };
});

vi.mock("react-redux", () => ({
  useDispatch: () => vi.fn(), // Still need dispatch mock but won't be used for breadcrumbs
  useSelector: vi.fn(() => ({})),
  Provider: ({ children }) => children,
}));

vi.mock("@/services/question-notice", () => ({
  useCreateNoticeDraftMutation: () => [
    mockCreateNoticeDraftMutation,
    { isLoading: false },
  ],
}));

vi.mock("@/services/master-data-management/active-assembly", () => ({
  useGetActiveAssemblyQuery: () => ({
    data: { assembly: "15", session: "15" },
  }),
  selectActiveAssemblyItems: () => ({ assembly: "15", session: "15" }),
}));

vi.mock("react-router-dom", () => ({
  NavLink: (props) => <MockNavLink {...props} />,
  Outlet: () => <MockOutlet />,
  useLocation: () => mockLocation,
  useNavigate: () => mockNavigate,
}));

const mockTranslations = {
  t: (key) => {
    const translations = {
      create: "Create",
      noMatchingDocumentType: "No Matching Document Type",
    };
    return translations[key] || key;
  },
  noMatchingDocumentType: "No Matching Document Type",
};

describe("NoticeForQuestion", () => {
  beforeEach(() => {
    vi.resetAllMocks();
    mockLocation = { pathname: "/my-question-notices" };
    useLanguage.mockReturnValue(mockTranslations);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("Component rendering", () => {
    it("renders the main UI elements correctly", () => {
      render(<NoticeForQuestion />);

      expect(screen.getByText("noticeForQuestionList")).toBeInTheDocument();
      expect(screen.getByText("Create")).toBeInTheDocument();
      expect(
        screen.getByTestId("navlink--member-my-question-notices"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("navlink--member-my-question-notices-notice-bank"),
      ).toBeInTheDocument();
      expect(screen.getByTestId("router-outlet")).toBeInTheDocument();
    });

    it("renders tabs with correct routes", () => {
      render(<NoticeForQuestion />);

      expect(
        screen.getByTestId("navlink--member-my-question-notices"),
      ).toHaveAttribute("href", "/member/my-question-notices");
      expect(
        screen.getByTestId("navlink--member-my-question-notices-notice-bank"),
      ).toHaveAttribute("href", "/member/my-question-notices/notice-bank");
    });

    it("renders with notice-bank path and sets correct default tab", () => {
      mockLocation = { pathname: "/my-question-notices/notice-bank" };

      render(<NoticeForQuestion />);

      const tabsElement = screen.getByRole("tablist");
      expect(tabsElement).toBeInTheDocument();
      expect(
        screen.getByTestId("navlink--member-my-question-notices"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("navlink--member-my-question-notices-notice-bank"),
      ).toBeInTheDocument();
    });
  });

  describe("Auto-breadcrumb integration", () => {
    it("uses auto-breadcrumb hook", async () => {
      const { useAutoBreadcrumb } = await import("@/hooks/use-auto-breadcrumb");

      render(<NoticeForQuestion />);

      // Verify the hook is called (breadcrumbs are now handled automatically)
      expect(useAutoBreadcrumb).toHaveBeenCalled();
    });
  });

  describe("Create notice functionality", () => {
    it("calls createNoticeDraft and navigates on successful creation", async () => {
      mockCreateNoticeDraftMutation.mockResolvedValue({ data: { id: "123" } });

      render(<NoticeForQuestion />);
      fireEvent.click(screen.getByText("Create"));

      await waitFor(() => {
        expect(mockCreateNoticeDraftMutation).toHaveBeenCalled();
        expect(toast.success).toHaveBeenCalledWith("success", {
          description: "documentCreatedSuccessfully",
        });
        expect(mockNavigate).toHaveBeenCalledWith(
          "/member/my-question-notices/notice-for-question/123",
        );
      });
    });

    it.skip("shows error toast when response is falsy", async () => {
      mockCreateNoticeDraftMutation.mockResolvedValue(undefined);

      render(<NoticeForQuestion />);
      fireEvent.click(screen.getByText("Create"));

      await waitFor(() => {
        expect(mockCreateNoticeDraftMutation).toHaveBeenCalled();
        expect(toast.error).toHaveBeenCalledWith("error", {
          description: "No matching document type",
          action: expect.any(Object),
        });
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });

    it("logs error on createNoticeDraft mutation failure", async () => {
      const consoleErrorMock = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      mockCreateNoticeDraftMutation.mockRejectedValue(new Error("Test Error"));

      render(<NoticeForQuestion />);
      fireEvent.click(screen.getByText("Create"));

      await waitFor(() => {
        expect(mockCreateNoticeDraftMutation).toHaveBeenCalled();
        expect(consoleErrorMock).toHaveBeenCalled();
      });

      consoleErrorMock.mockRestore();
    });
  });
});
