import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useContextualNavigation } from "@/utils/navigation";
import { ROUTE_CONFIG } from "@/config/routes";
import {
  ArrowLeft,
  ArrowRight,
  Calendar,
  Pencil,
  StickyNote,
} from "lucide-react";

import {
  useGetDesignationQuery,
  useGetPortfolioQuery,
  useGetSubSubjectQuery,
} from "@/services/master-data-management/basic-details-notice";
import { useGetNoticeByIdQuery } from "@/services/question";
import { useAccordion, useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { formatDate } from "@/utils";
import { getPriorityClasses } from "@/utils/ui-utils";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { StarredIcon, UnStarredIcon } from "@/icons/star-icon";

import { DocumentMetadata } from "@/components/document-metadata";
import {
  Badge,
  Button,
  DatePicker,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  ExpandableAccordion,
  ExpandableContent,
  ExpandableHeader,
  ExpandableItem,
  IconButton,
} from "@kla-v2/ui-components";

export const DocumentView = () => {
  const [documentMetaData, setDocumentMetaData] = useState({
    entries: [],
    metadata: {
      kla: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });
  const [isPostponeDialogOpen, setIsPostponeDialogOpen] = useState(false);
  const [postponeDate, setPostponeDate] = useState("");
  const { navigate } = useContextualNavigation();
  const { t } = useLanguage();
  const { documentId } = useParams();
  const { data: designationData } = useGetDesignationQuery();
  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: subjectData } = useGetSubSubjectQuery();
  const { data: documentData } = useGetNoticeByIdQuery({ documentId });

  useAutoBreadcrumb();

  const accordions = ["item-1", "item-2"];
  const initialAccordion = { accordions, openAccordions: [accordions[0]] };
  const { accordionValue, setAccordionValue } = useAccordion(initialAccordion);

  useEffect(() => {
    if (documentData) {
      setDocumentMetaData({
        entries: documentMetaData.entries || [],
        metadata: {
          kla: documentData.assembly?.toString() || "",
          session: documentData.session?.toString() || "",
          documentType: documentData.type || "Minister Designation Group",
          currentNo: documentData.currentNumber || "",
          createdOn: formatDate(documentData.createdAt) || "",
          createdBy: documentData.createdBy || "",
          name: documentData.name || "Minister Designation Group",
          status: documentData.status || "",
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentData]);

  useEffect(() => {
    if (!isPostponeDialogOpen) {
      setPostponeDate("");
    }
  }, [isPostponeDialogOpen]);


  const handleDateChange = (date) => {
    setPostponeDate(date);
  };

  const handlePostponeConfirm = () => {
    setIsPostponeDialogOpen(false);
  };

  if (!documentData) {
    return <SpinnerLoader />;
  }

  return (
    <div>
      <div className="flex items-center justify-between px-4 my-4">
        <h1 className="typography-page-heading">
          {documentMetaData.metadata.name} {documentData.noticeHeading}
        </h1>
        <div className="flex items-center gap-2">
          <IconButton
            icon={Pencil}
            size="sm"
            variant="secondary"
            onClick={() => {}}
          />
          <Button
            icon={StickyNote}
            iconPosition="right"
            size="md"
            variant="neutral"
            onClick={() => {}}
          >
            {t("preview")}
          </Button>
        </div>
      </div>

      <DocumentMetadata documentMetadata={documentMetaData.metadata} />

      <ExpandableAccordion
        value={accordionValue}
        onValueChange={setAccordionValue}
        type="multiple"
      >
        <ExpandableItem value="item-1">
          <ExpandableHeader index={1} label="Basic Details" />
          <ExpandableContent>
            <div className="grid grid-cols-3 px-4 pb-6 gap-x-4 gap-y-6">
              <div>
                <p className="mb-1 text-gray-500">{t("kla")}</p>
                <p className="font-medium">{documentData.assembly}</p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("session")}</p>
                <p className="font-medium">{documentData.session}</p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("noticeNumber")}</p>
                <p className="font-medium">{documentData.noticeNumber}</p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("questionNo")}</p>
                <p className="font-medium">{documentData.noticeNumber}</p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("questionDate")}</p>
                <p className="font-medium">
                  {formatDate(documentData.questionDate)}
                </p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("designation")}</p>
                <p className="font-medium">
                  {designationData?.find(
                    (designation) =>
                      designation.id === documentData.ministerDesignationId,
                  )?.title || "–"}
                </p>
              </div>

              <div>
                <p className="mb-1 text-gray-500">
                  {t("ministerSubject/Portfolio")}
                </p>
                <p className="font-medium">
                  {portfolioData?.find((p) => p.id === documentData.portfolioId)
                    ?.title || "–"}
                </p>
              </div>
              <div>
                <p className="mb-1 text-gray-500">{t("ministerSubSubject")}</p>
                <p className="font-medium">
                  {subjectData?.find((s) => s.id === documentData.subSubjectId)
                    ?.title || "–"}
                </p>
              </div>
              <div className="col-span-3">
                <p className="mb-3 text-gray-500">{t("nameOfMembers")}</p>
                <div className="flex flex-wrap gap-3">
                  {/* Primary Member */}
                  <div className="flex items-center px-3 py-1 bg-white border border-gray-200 rounded-full">
                    <img
                      src={`/mdm-service/api/minister/${documentData.primaryMember.id}/photo`}
                      alt={documentData.primaryMember.memberDisplayName}
                      className="object-cover w-6 h-6 mr-2 border rounded-full"
                    />
                    <span className="text-sm">
                      {documentData.primaryMember.memberDisplayName}
                    </span>
                    <span className="ml-1 text-xs text-gray-500">
                      - {documentData.primaryMember.constituencyName}
                    </span>
                  </div>

                  {/* Secondary Members */}
                  {documentData.secondaryMembers.map((member) => (
                    <div
                      key={member.memberId}
                      className="flex items-center px-3 py-1 bg-white border border-gray-200 rounded-full"
                    >
                      <img
                        src={`/mdm-service/api/minister/${member.id}/photo`}
                        alt={member.memberDisplayName}
                        className="object-cover w-6 h-6 mr-2 border rounded-full"
                      />
                      <span className="text-sm">
                        {member.memberDisplayName}
                      </span>
                      <span className="ml-1 text-xs text-gray-500">
                        - {member.constituencyName}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </ExpandableContent>
        </ExpandableItem>

        <ExpandableItem value="item-2">
          <ExpandableHeader index={2} label="Question Details" />
          <ExpandableContent>
            <div className="px-4 pb-6 space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="mb-2 text-gray-600">Priority</p>
                  <Badge
                    className={`px-4 py-2 w-32 rounded-full cursor-pointer text-center ${getPriorityClasses(
                      documentData?.noticePriority,
                    )}`}
                  >
                    {documentData?.noticePriority}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Starred</span>
                  {documentData?.starred ? <StarredIcon /> : <UnStarredIcon />}
                </div>
              </div>

              <div>
                <p className="mb-2 text-gray-600">{t("noticeHeading")}</p>
                <div className="p-3 border rounded-md ">
                  <p className="typography-para-14">
                    {documentData.noticeHeading}
                  </p>
                </div>
              </div>

              <div>
                <p className="mb-6 typography-body-text-s-16">{t("clause")}</p>
                <div className="space-y-3">
                  {[...documentData.clauses]
                    .sort((a, b) => a.order - b.order)
                    .map((clause, index) => (
                      <div
                        key={clause.id}
                        className="flex items-start gap-4 p-3 border rounded-md"
                      >
                        <p className="typography-para-14">
                          ({String.fromCharCode(65 + index)})
                        </p>
                        <p className="typography-para-14">{clause.content}</p>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </ExpandableContent>
        </ExpandableItem>
      </ExpandableAccordion>
      <div className="fixed bottom-0 left-0 right-0 z-10 px-4 py-3 bg-white border-t shadow-md">
        <div className="flex justify-end gap-3 mx-auto">
          <Button
            icon={ArrowLeft}
            iconPosition="left"
            size="lg"
            variant="neutral"
            onClick={() => {
              navigate(ROUTE_CONFIG.MEMBER.MY_QUESTION_NOTICES);
            }}
          >
            {t("cancel")}
          </Button>
          <Button
            icon={Calendar}
            iconPosition="left"
            size="lg"
            variant="secondary"
            onClick={() => setIsPostponeDialogOpen(true)}
          >
            {t("postpone")}
          </Button>
          <Button
            icon={ArrowRight}
            iconPosition="right"
            size="lg"
            variant="primary"
            onClick={() => {}}
          >
            {t("withdraw")}
          </Button>
        </div>
      </div>

      {/* Postpone Confirmation Dialog */}
      <Dialog
        open={isPostponeDialogOpen}
        onOpenChange={setIsPostponeDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-left">
              {t("doYouWantToPostponeQuestion")}
            </DialogTitle>
            <DialogDescription className="text-left text-gray-500">
              <div className="mt-4">
                <DatePicker
                  label={t("postponeDate")}
                  mode="single"
                  value={postponeDate}
                  onChange={handleDateChange}
                />
              </div>
              <div className="mt-2">
                <p>{t("postponeWarningMessage")}</p>
              </div>
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="!justify-end !items-center space-x-2 mt-4">
            <Button
              variant="neutral"
              onClick={() => setIsPostponeDialogOpen(false)}
            >
              {t("cancel")}
            </Button>
            <Button
              variant="primary"
              onClick={handlePostponeConfirm}
              disabled={!postponeDate}
            >
              {t("confirm")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
