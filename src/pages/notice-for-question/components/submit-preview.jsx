import { DocumentMetadata } from "@/components/document-metadata";
import { BasicDetailsReadOnly } from "@/components/read-only/basic-details-read-only";
import { NoriceDetailsReadOnly } from "@/components/read-only/notice-details-read-only";
import { useGetPrivateMemberNoticeQuery } from "@/services/notice-for-question-to-private-members";
import { formatDateString } from "@/utils/date-utils";
import { routeHelpers } from "@/config/routes";
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  ExpandableAccordion,
  ExpandableItem,
} from "@kla-v2/ui-components";
import { Check, Pencil } from "lucide-react";
import PropTypes from "prop-types";
import { useState } from "react";

export default function SubmitPreview({
  onClose,
  onConfirm,
  isOpen,
  documentId,
}) {
  const [accordionValue, setAccordionValue] = useState([]);
  const { data: documentData } = useGetPrivateMemberNoticeQuery({
    documentId,
  });

  const amendedMetadata = {
    ...documentData,
    createdOn: formatDateString(documentData?.createdAt),
    createdBy: documentData?.createdBy || "",
    isDocTypeAdded: true,
    documentType: routeHelpers.getFormattedDocumentType(documentData?.type),
    name: documentData?.name,
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="w-[1140px] max-w-[80vw]">
          <DialogHeader>
            <DialogTitle>
              <div className="typography-page-heading">Preview & Confrim</div>
            </DialogTitle>
            <DialogDescription>
              <div className="flex items-center justify-between my-4">
                <h3 className="typography-sub-title-heading">
                  {amendedMetadata?.name}
                </h3>
              </div>

              <DocumentMetadata documentMetadata={amendedMetadata} />
            </DialogDescription>
          </DialogHeader>
          <div className="bg-background">
            <div className="mb-4">
              <h3 className="text-xl font-medium">{documentData?.name}</h3>
              <ExpandableAccordion
                type="multiple"
                value={accordionValue}
                onValueChange={setAccordionValue}
                className="w-full flex flex-col gap-4 flex-grow"
              >
                <ExpandableItem value={`item-1`}>
                  <BasicDetailsReadOnly
                    accordionOrderNo={1}
                    data={documentData}
                    variant={"privatemember"}
                  />
                </ExpandableItem>
                <ExpandableItem value={`item-2`}>
                  <NoriceDetailsReadOnly
                    accordionOrderNo={3}
                    data={documentData}
                  />
                </ExpandableItem>
              </ExpandableAccordion>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                icon={Pencil}
                iconPosition="left"
                size="lg"
                variant="secondary"
              >
                Edit
              </Button>
            </DialogClose>
            <Button
              icon={Check}
              iconPosition="right"
              size="lg"
              variant="primary"
              onClick={onConfirm}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

SubmitPreview.propTypes = {
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  documentData: PropTypes.object,
  isOpen: PropTypes.bool.isRequired,
  documentId: PropTypes.string,
};
