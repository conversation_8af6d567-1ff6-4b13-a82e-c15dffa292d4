import { useState } from "react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { PlusIcon, Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON>,
  toast,
} from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import { useCreateNoticeDraftMutation } from "@/services/question-notice";
import { useGetActiveAssemblyQuery } from "@/services/master-data-management/active-assembly";
import { CloseToastButton } from "@/components/ui/close-toast";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { buildRoute, useContextualNavigation } from "@/utils/navigation";
import MyNotices from "@/pages/notice-for-question/my-notice";
import NoticeBank from "@/pages/notice-for-question/notice-bank";
import { ROUTE_CONFIG } from "@/config/routes";

function NoticeForQuestion() {
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const [createNoticeDraft] = useCreateNoticeDraftMutation();
  const { data: activeAssembly } = useGetActiveAssemblyQuery();
  const { isPPOContext, getContextualPath } = useContextualNavigation();

  useAutoBreadcrumb();

  // Check if we're on the notice bank tab
  const isNoticeBank = location.pathname.includes("notice-bank");

  // Get the current path for this context
  const currentPath = getContextualPath(ROUTE_CONFIG.MEMBER.MY_QUESTION_NOTICES);

  /**
   * Handle document creation
   */
  const onSubmit = async () => {
    if (isCreating) return;

    setIsCreating(true);

    // Use active assembly data if available, otherwise use defaults
    const assembly = activeAssembly?.assembly || "15";
    const session = activeAssembly?.session || "13";

    const data = {
      type: "NOTICE_FOR_QUESTION",
      name: "Dhinesh",
      assembly,
      session,
    };

    try {
      const response = await createNoticeDraft(data);

      if (response?.data?.id) {
        const id = response.data.id;
        // Use contextual navigation for the notice route
        const routePath = isPPOContext
          ? buildRoute.ppoNoticeForQuestion(id)
          : buildRoute.memberNoticeForQuestion(id);

        toast.success(t("success"), {
          description: t("documentCreatedSuccessfully"),
        });
        navigate(routePath);
      } else {
        handleError(new Error(t("validation:noMatchingDocumentType")));
      }
    } catch (error) {
      handleError(error);
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Handle errors during document creation
   *
   * @param {Error} error - The error object
   */
  const handleError = (error) => {
    console.error("Error creating document:", error);

    toast.error(t("error"), {
      description:
        error?.data?.message || error?.message || t("failedToCreateDocument"),
      action: { label: <CloseToastButton /> },
    });
  };

  // Render the appropriate content based on the current route
  const renderContent = () => {
    if (isNoticeBank) {
      return <NoticeBank />;
    } else {
      return <MyNotices />;
    }
  };

  // Handle tab change
  const handleTabChange = (tabPath) => {
    navigate(tabPath, { replace: true });
  };

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">
          {t("noticeForQuestionList")}
        </h2>
        <Button
          variant="secondary"
          icon={isCreating ? Loader2 : PlusIcon}
          iconPosition="left"
          onClick={onSubmit}
          disabled={isCreating}
          className={isCreating ? "opacity-70" : ""}
        >
          {isCreating ? t("creating") : t("create")}
        </Button>
      </div>

      <div>
        <Tabs value={isNoticeBank ? "tab2" : "tab1"}>
          <TabsList variant="segmented">
            <NavLink
              to={currentPath}
              className={({ isActive }) =>
                isActive && !isNoticeBank ? "active" : ""
              }
              data-testid="navlink--member-my-question-notices"
              onClick={(e) => {
                if (isNoticeBank) {
                  e.preventDefault();
                  handleTabChange(currentPath);
                }
              }}
            >
              <TabsTrigger value="tab1" variant="segmented">
                {t("myNotices")}
              </TabsTrigger>
            </NavLink>
            <NavLink
              to={`${currentPath}/notice-bank`}
              className={({ isActive }) =>
                isActive && isNoticeBank ? "active" : ""
              }
              data-testid="navlink--member-my-question-notices-notice-bank"
              onClick={(e) => {
                if (!isNoticeBank) {
                  e.preventDefault();
                  handleTabChange(`${currentPath}/notice-bank`);
                }
              }}
            >
              <TabsTrigger value="tab2" variant="segmented">
                {t("noticeBank")}
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        {renderContent()}
      </div>
    </div>
  );
}

export default NoticeForQuestion;
