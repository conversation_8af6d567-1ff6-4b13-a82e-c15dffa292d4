import { NoticeDetailsPage } from "@/components/accordions/notice-page";
import { DocumentMetadata } from "@/components/document-metadata";
import { useAccordion, useLanguage } from "@/hooks";
import { useUserProfile } from "@/hooks/user-profile";
import { formatDateString } from "@/utils/date-utils";
import { routeHelpers } from "@/config/routes";
import {
  Button,
  ExpandableAccordion,
  ExpandableItem,
  toast,
} from "@kla-v2/ui-components";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { useContextualNavigation } from "@/utils/navigation";
import { ROUTE_CONFIG } from "@/config/routes";
import BasicDetailsAccordion from "../components/basic-details";
import SubmitPreview from "../components/submit-preview";
import { useLoaderData } from "react-router-dom";
import {
  useGetPrivateMemberNoticeQuery,
  useSubmitNoticeMutation,
} from "@/services/notice-for-question-to-private-members";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

function QuestionToPrivateMember() {
  const { documentId } = useParams();

  const basicDetailsRef = useRef();
  const noticeDetailsRef = useRef();
  const [submitDocument] = useSubmitNoticeMutation();
  const { navigate } = useContextualNavigation();
  const { t } = useLanguage();
  const { documentData: initialDocumentData } = useLoaderData();
  const { data: documentData, refetch: refetchDocument } =
    useGetPrivateMemberNoticeQuery(
      { documentId },
      { initialData: initialDocumentData },
    );

  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const { userProfile } = useUserProfile();

  const amendedMetadata = {
    ...documentData,
    createdOn: formatDateString(documentData?.createdAt),
    createdBy: documentData?.createdBy,
    isDocTypeAdded: true,
    documentType: routeHelpers.getFormattedDocumentType(documentData?.type),
    name: documentData?.name,
  };

  const goBack = () => {
    navigate(ROUTE_CONFIG.MEMBER.MY_NOTICES);
  };

  const accordions = ["item-1", "item-2"];
  const initialAccordion = { accordions, openAccordions: [accordions[0]] };
  const {
    accordionValue,
    setAccordionValue,
    openNextAccordion,
    closeAccordion,
  } = useAccordion(initialAccordion);

  const handleValidation = async () => {
    const invalidAccordions = [];

    const [isBasicDetailsValid, isNoticeDetailsValid] = await Promise.all([
      await basicDetailsRef.current.validateAndSubmit(true),
      await noticeDetailsRef.current.validateAndSubmit(true),
    ]);
    if (!isBasicDetailsValid || !isNoticeDetailsValid) {
      console.error("Validation failed:", {
        isBasicDetailsValid,
        isNoticeDetailsValid,
      });
    }
    if (!isBasicDetailsValid) invalidAccordions.push("item-1");
    if (!isNoticeDetailsValid) invalidAccordions.push("item-2");
    if (invalidAccordions.length > 0) {
      setAccordionValue([...invalidAccordions]);
      return;
    }
    setShowPreviewDialog(true);
  };

  useAutoBreadcrumb();

  const handleFinalSubmit = async () => {
    try {
      await submitDocument({
        documentId,
      }).unwrap();
      await refetchDocument();
      toast.success(t("Submitted Successfully"));
      navigate(ROUTE_CONFIG.MEMBER.MY_NOTICES);
    } catch (error) {
      toast.error(error?.message);
    }
  };

  return (
    <div className="px-5 py-3 bg-background flex flex-col min-h-screen">
      <div className="flex justify-between">
        <h4 className="typography-page-heading">{t(amendedMetadata?.name)}</h4>
      </div>
      <div className="grow overflow-hidden flex flex-col  pb-8">
        <div className="w-full py-5">
          <DocumentMetadata documentMetadata={amendedMetadata} />
        </div>
        <ExpandableAccordion
          type="multiple"
          value={accordionValue}
          onValueChange={setAccordionValue}
          className="w-full flex flex-col gap-4"
        >
          <ExpandableItem value="item-1">
            <BasicDetailsAccordion
              ref={basicDetailsRef}
              accordionOrderNo={1}
              documentId={documentId}
              getBasicDetails={initialDocumentData}
              userProfile={userProfile}
              openNext={() => openNextAccordion(accordions[0])}
              refetch={refetchDocument}
            />
          </ExpandableItem>
          <ExpandableItem value="item-2">
            <NoticeDetailsPage
              ref={noticeDetailsRef}
              accordionOrderNo={2}
              documentId={documentId}
              variant="privateMember"
              noticeDetailsData={initialDocumentData}
              openNext={() => closeAccordion(accordions[1])}
              refetch={refetchDocument}
            />
          </ExpandableItem>
        </ExpandableAccordion>
        <div className="flex justify-end items-center gap-4 py-4 bg-background">
          <Button
            variant="neutral"
            className="flex items-center gap-3 text-black border border-gray-200 rounded-md"
            onClick={goBack}
            icon={ArrowLeft}
            iconPosition="left"
          >
            <span>{t("back")}</span>
          </Button>
          <Button
            size="md"
            variant="primary"
            iconPosition="right"
            icon={ArrowRight}
            type="submit"
            onClick={handleValidation}
          >
            {t("submit")}
          </Button>
        </div>
      </div>
      {showPreviewDialog && (
        <SubmitPreview
          isOpen={showPreviewDialog}
          onClose={() => setShowPreviewDialog(false)}
          onConfirm={handleFinalSubmit}
          documentId={documentId}
        />
      )}
    </div>
  );
}

export default QuestionToPrivateMember;
