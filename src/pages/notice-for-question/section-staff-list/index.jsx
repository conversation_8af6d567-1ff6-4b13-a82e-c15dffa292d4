import { NavLink, Outlet, useLocation } from "react-router-dom";
import { ROUTE_CONFIG } from "@/config/routes";
import { PlusIcon } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, But<PERSON> } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

function SectionStaffList() {
  const { t } = useLanguage();
  const location = useLocation();

  useAutoBreadcrumb();

  const isAllTab = location.pathname.includes("/all");

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">Notice For Question List</h2>
        <Button variant="secondary" icon={PlusIcon} iconPosition="left">
          {t("Submit")}
        </Button>
      </div>

      <div>
        <Tabs defaultValue={isAllTab ? "tab2" : "tab1"}>
          <TabsList variant="segmented">
            <NavLink to={ROUTE_CONFIG.SECTION.QUESTION_EDIT_LIST}>
              <TabsTrigger value="tab1" variant="segmented">
                Action to be taken
              </TabsTrigger>
            </NavLink>
            <NavLink to={ROUTE_CONFIG.SECTION.QUESTION_EDIT_LIST_ALL}>
              <TabsTrigger value="tab2" variant="segmented">
                All
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        <Outlet />
      </div>
    </div>
  );
}

export default SectionStaffList;
