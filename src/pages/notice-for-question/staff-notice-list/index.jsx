import { NavLink, Outlet, useLocation } from "react-router-dom";
import { PlusIcon } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, But<PERSON> } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

function StaffNoticeList() {
  const { t } = useLanguage();
  const location = useLocation();

  useAutoBreadcrumb();

  const onSubmit = async () => {
    //TODO: Add logic to handle the submit action
  };

  const isNoticeBank = location.pathname.includes("notice-bank");

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex items-center justify-between">
        <h2 className="typography-page-heading">Notice For Question List</h2>
        <Button
          variant="secondary"
          icon={PlusIcon}
          iconPosition="left"
          onClick={onSubmit}
        >
          {t("submit")}
        </Button>
      </div>

      <div>
        <Tabs defaultValue={isNoticeBank ? "tab2" : "tab1"}>
          <TabsList variant="segmented">
            <NavLink to=".">
              <TabsTrigger value="tab1" variant="segmented">
                Action to be taken
              </TabsTrigger>
            </NavLink>
            <NavLink to="./all-notices">
              <TabsTrigger value="tab2" variant="segmented">
                All
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        <Outlet />
      </div>
    </div>
  );
}

export default StaffNoticeList;
