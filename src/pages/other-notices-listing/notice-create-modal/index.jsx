import { useNavigate } from "react-router-dom";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Combobox,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  toast,
} from "@kla-v2/ui-components";
import PropType from "prop-types";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { getFormFields } from "./form-fields";
import { useCreateScheduleOfActivityMutation } from "@/services/schedule-of-activity";
import { useSelector } from "react-redux";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";
import { useContextualNavigation } from "@/utils/navigation";
import { getNoticeRoute } from "@/utils/document-types/document-types";

const formSchema = z.object({
  type: z.string().nonempty("Notice type is required"),
});

export default function DocumentCreateModal({ isOpen, onClose }) {
  const navigate = useNavigate();
  const [createScheduleOfActivity] = useCreateScheduleOfActivityMutation();
  const activeAssembly = useSelector(selectActiveAssemblyItems);
  useContextualNavigation();

  const assembly = activeAssembly?.assembly;
  const session = activeAssembly?.session;

  const modalNoticeTypes = [
    {
      label: "Short Notice",
      value: "NOTICE_FOR_SHORT_NOTICE",
      routeType: "shortNotice",
    },
    {
      label: "Half an Hour",
      value: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSION",
      routeType: "halfHour",
    },
    {
      label: "Question to Private Member",
      value: "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS",
      routeType: "privateMember",
    },
  ];

  const { t } = useLanguage();
  const formFields = getFormFields(t);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "",
    },
  });

  const { handleSubmit, reset, watch } = form;

  const noticeType = watch("type");

  const onSubmit = async (value) => {
    try {
      let response = await createScheduleOfActivity({
        data: value,
        assembly: Number(assembly),
        session: Number(session),
      });

      if (response.error) {
        throw new Error(response.error);
      }

      const { type, id } = response.data;
      if (type) {
        const docType = modalNoticeTypes.find((doc) => doc.value === type);
        if (docType) {
          toast.success("Notice created successfully");
          let route = `${getNoticeRoute(docType.value, "create")}${id}`;
          navigate(route);
        } else {
          toast.error("Error", {
            description: t("noMatchingNoticeType"),
          });
        }
      }
    } catch (error) {
      console.error("Error creating notice:", error);
      toast.error("Error creating notice");
    }
    onClose();
  };

  return (
    <Dialog
      className="sm:text-left"
      open={isOpen}
      onOpenChange={onClose}
      data-testid="notice-create-modal"
    >
      <DialogPortal>
        <DialogContent onCloseAutoFocus={reset}>
          <DialogHeader>
            <DialogTitle>Create Notice</DialogTitle>
            <DialogDescription className="text-gray-500">
              Select below option(s) to create Notice
            </DialogDescription>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>
          <FormProvider {...form}>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-4"
              role="form"
              data-testid="notice-create-form"
            >
              <div>
                <FormField
                  name={formFields.NOTICE_TYPE.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          className="w-full"
                          options={modalNoticeTypes}
                          isMulti={false}
                          modalPopover={true}
                          onValueChange={field.onChange}
                          {...field}
                          placeholder={formFields.NOTICE_TYPE.placeholder}
                          label={formFields.NOTICE_TYPE.label}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="!justify-center !items-center">
                <DialogClose asChild>
                  <Button variant="neutral" data-testid="close-modal-button">
                    {t("cancel")}
                  </Button>
                </DialogClose>
                <Button variant="primary" type="submit" disabled={!noticeType}>
                  {t("confirm")}
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

DocumentCreateModal.propTypes = {
  isOpen: PropType.bool,
  onClose: PropType.func,
};
