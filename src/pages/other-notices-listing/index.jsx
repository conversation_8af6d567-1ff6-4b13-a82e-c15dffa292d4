import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { PlusIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import NoticeCreateModal from "./notice-create-modal";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useState } from "react";
import MyOtherNotices from "./other-notices";
import OtherNoticeBanks from "./other-notice-bank";
import { useContextualNavigation } from "@/utils/navigation";

function Notices() {
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const { getContextualPath } = useContextualNavigation();

  useAutoBreadcrumb();

  const isNoticeBank = location.pathname.includes("notice-bank");

  // Get the contextual path for notices
  const currentPath = getContextualPath("/member/my-notices");

  const closeModal = () => {
    setIsOpen(false);
  };

  // Handle tab change
  const handleTabChange = (tabPath) => {
    navigate(tabPath, { replace: true });
  };

  // Render the appropriate content based on the current route
  const renderContent = () => {
    if (isNoticeBank) {
      return <OtherNoticeBanks />;
    } else {
      return <MyOtherNotices />;
    }
  };

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">Notice List</h2>
        <Button
          variant="secondary"
          icon={PlusIcon}
          iconPosition="left"
          onClick={() => setIsOpen(true)}
          data-testid="button"
        >
          {t("create")}
        </Button>
      </div>

      <div>
        <Tabs value={isNoticeBank ? "tab2" : "tab1"} data-testid="tabs">
          <TabsList variant="segmented" data-testid="tabs-list">
            <NavLink
              to={currentPath}
              className={({ isActive }) =>
                isActive && !isNoticeBank ? "active" : ""
              }
              onClick={(e) => {
                if (isNoticeBank) {
                  e.preventDefault();
                  handleTabChange(currentPath);
                }
              }}
            >
              <TabsTrigger
                value="tab1"
                variant="segmented"
                data-testid="tab-tab1"
              >
                My Notices
              </TabsTrigger>
            </NavLink>
            <NavLink
              to={`${currentPath}/notice-bank`}
              className={({ isActive }) =>
                isActive && isNoticeBank ? "active" : ""
              }
              onClick={(e) => {
                if (!isNoticeBank) {
                  e.preventDefault();
                  handleTabChange(`${currentPath}/notice-bank`);
                }
              }}
            >
              <TabsTrigger
                value="tab2"
                variant="segmented"
                data-testid="tab-tab2"
              >
                Notice Bank
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        {renderContent()}
      </div>
      {isOpen && <NoticeCreateModal isOpen={isOpen} onClose={closeModal} />}
    </div>
  );
}

export default Notices;
