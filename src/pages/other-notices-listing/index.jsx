import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { PlusIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import NoticeCreateModal from "./notice-create-modal";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useState } from "react";
import MyOtherNotices from "./other-notices";
import OtherNoticeBanks from "./other-notice-bank";
import { useContextualNavigation } from "@/utils/navigation";
import { ROUTE_CONFIG } from "@/config/routes";

function Notices() {
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  useContextualNavigation();

  useAutoBreadcrumb();

  const isNoticeBank = location.pathname.includes("notice-bank");

  const closeModal = () => {
    setIsOpen(false);
  };

  // Handle tab change
  const handleTabChange = (tabPath) => {
    navigate(tabPath, { replace: true });
  };

  // Render the appropriate content based on the current route
  const renderContent = () => {
    if (isNoticeBank) {
      return <OtherNoticeBanks />;
    } else {
      return <MyOtherNotices />;
    }
  };


  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">Notice List</h2>
        <Button
          variant="secondary"
          icon={PlusIcon}
          iconPosition="left"
          onClick={() => setIsOpen(true)}
          data-testid="button"
        >
          {t("create")}
        </Button>
      </div>

      <div>
        <Tabs value={isNoticeBank ? "tab2" : "tab1"} data-testid="tabs">
          <TabsList variant="segmented" data-testid="tabs-list">
            <NavLink
              to={ROUTE_CONFIG.MEMBER.MY_NOTICES}
              className={({ isActive }) =>
                isActive && !isNoticeBank ? "active" : ""
              }
            >
              <TabsTrigger
                value="tab1"
                variant="segmented"
                data-testid="tab-tab1"
              >
                {t("myNotices")}
              </TabsTrigger>
            </NavLink>
            <NavLink
              to={ROUTE_CONFIG.MEMBER.MY_NOTICES_NOTICE_BANK}
              className={({ isActive }) =>
                isActive && isNoticeBank ? "active" : ""
              }
            >
              <TabsTrigger
                value="tab2"
                variant="segmented"
                data-testid="tab-tab2"
              >
                {t("noticeBank")}
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        {renderContent()}
      </div>
      {isOpen && <NoticeCreateModal isOpen={isOpen} onClose={closeModal} />}
    </div>
  );
}

export default Notices;
