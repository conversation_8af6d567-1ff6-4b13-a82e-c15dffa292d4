import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { getTableColumns } from "./table-columns";
import { normalizeTableData } from "./data-normalizer";
import { useSearchParams } from "react-router-dom";
import { Pencil, Search, Delete } from "lucide-react";
import PropTypes from "prop-types";
import {
  DataTable,
  TableDropdownMenu,
  Input,
  FilterDropdown,
  PaginationSelect,
  Paginator,
  toast,
} from "@kla-v2/ui-components";
import { useGetOtherNoticesListQuery } from "../../services/other-notices-notice-bank";
import {
  useGetPortfolioQuery,
  useGetDesignationQuery,
} from "../../services/master-data-management/basic-details-notice";
import {
  useGetAssemblyQuery,
  useGetSessionQuery,
} from "../../services/master-data-management/assembly";
import { useLanguage, useDebounce } from "@/hooks";
import { useSelector } from "react-redux";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";
import {
  noticeTypes,
  getNoticeRoute,
} from "@/utils/document-types/document-types";
import { routeHelpers } from "@/config/routes";
import { useContextualNavigation } from "@/utils/navigation";
import { useLazyGetDocumentByIdQuery } from "@/services/documents";
import { useNavigate } from "react-router-dom";
import SpinnerLoader from "@/utils/loaders/spinner-loader";

const ActionCell = ({ row }) => {
  const menuItems = [
    {
      label: (
        <div className="flex items-center gap-2">
          <Pencil size={16} /> Edit
        </div>
      ),
    },
    {
      label: (
        <div className="flex items-center gap-2">
          <Delete size={16} /> Delete
        </div>
      ),
    },
  ];

  return <TableDropdownMenu row={row} menuItems={menuItems} />;
};

ActionCell.propTypes = {
  row: PropTypes.shape({
    original: PropTypes.object.isRequired,
  }).isRequired,
};

function ShortNoticeBanks() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || "",
  );
  const [filteredData, setFilteredData] = useState({});
  const { isPPOContext } = useContextualNavigation();

  const debouncedSearchValue = useDebounce(searchInput, 500);

  const page = parseInt(searchParams.get("page") || "1") - 1;
  const size = parseInt(searchParams.get("size") || "10");

  const searchQuery = searchParams.get("search") || "";
  const activeAssembly = useSelector(selectActiveAssemblyItems);
  const { data: assemblyData } = useGetAssemblyQuery();
  const { data: sessionData } = useGetSessionQuery();

  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: designationData } = useGetDesignationQuery();
  const [trigger] = useLazyGetDocumentByIdQuery();

  const filters = useMemo(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      return {
        Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
        Session: { filter: "Session", value: activeAssembly?.session },
      };
    }
    return {};
  }, [activeAssembly?.assembly, activeAssembly?.session]);

  const filteredDataRef = useRef(filteredData);
  useEffect(() => {
    filteredDataRef.current = filteredData;
  }, [filteredData]);

  useEffect(() => {
    if (
      Object.keys(filters).length > 0 &&
      Object.keys(filteredDataRef.current).length === 0
    ) {
      setFilteredData(filters);
    }
  }, [filters]);

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      const { value } = filterData;

      switch (key) {
        case "noticeType":
          queryParams["noticeType"] =
            typeof value === "object" && value !== null
              ? value.noticeType || ""
              : value || "";
          break;
        case "ministerDesignation":
          queryParams["ministerDesignation"] =
            typeof value === "object" && value !== null
              ? value.Designation || ""
              : value || "";
          break;
        case "portfolio":
          queryParams["portfolio"] =
            typeof value === "object" && value !== null
              ? value.Portfolio || ""
              : value || "";
          break;
        case "Assembly":
          queryParams["assembly"] =
            typeof value === "object" && value !== null
              ? value.Assembly || ""
              : value || "";
          break;
        case "Session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.Session || ""
              : value || "";
          break;
        default:
          break;
      }
    });

    return queryParams;
  };

  const processedFilters = processFilters(filteredData);

  const { data: otherNoticesData, isLoading: noticeBankLoading } =
    useGetOtherNoticesListQuery({
      search: searchQuery,
      page,
      size,
      ...processedFilters,
    });

  const tableData = () => {
    const data = otherNoticesData?.content ?? [];
    console.log("Notice bank data:", data);
    return normalizeTableData(data);
  };

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }

        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (newParams.search !== undefined) {
        if (newParams.search) {
          updatedParams.set("search", newParams.search);
        } else {
          updatedParams.delete("search");
        }
      }

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchKeyDown = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ search: "" });
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      updateParams({ search: debouncedSearchValue });
    }
  }, [debouncedSearchValue, searchQuery, updateParams]);

  const getDisplayedPageInfo = () => {
    const totalElements = otherNoticesData?.totalElements;
    if (!totalElements) return "";

    const startIndex = (page + 1) * size - (size - 1);
    const endIndex = Math.min((page + 1) * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const assemblyOptions =
    assemblyData?.map((kla) => ({
      label: kla.title,
      value: kla.title,
    })) || [];

  const sessionOptions =
    sessionData?.map((session) => ({
      label: session.title,
      value: session.title,
    })) || [];

  const noticeTypeOptions = [
    { label: "Short Notice", value: "NOTICE_FOR_SHORT_NOTICE" },
    { label: "Half an Hour", value: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSIONr" },
    {
      label: "Question to private member",
      value: "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBER",
    },
  ];

  const designationOptions =
    designationData?.map((designation) => ({
      label: designation.title,
      value: designation.title,
    })) || [];

  const portfolioOptions =
    portfolioData?.map((portfolio) => ({
      label: portfolio.title,
      value: portfolio.title,
    })) || [];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const noticeBankFilterOptions = [
    {
      label: t("table:Assembly"),
      submenu: [
        {
          comboboxOptions: assemblyOptions,
          label: "Assembly",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "Assembly",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Assembly",
    },
    {
      label: t("table:Session"),
      submenu: [
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Session",
          truncateStringLength: 6,
          value: "Session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Session",
    },
    {
      label: t("Notice Type"),
      submenu: [
        {
          comboboxOptions: noticeTypeOptions,
          label: "noticeType",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Notice Type",
          truncateStringLength: 6,
          value: "Notice Type",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "noticeType",
    },
    {
      label: t("table:Designation"),
      submenu: [
        {
          comboboxOptions: designationOptions,
          label: "Designation",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Designation",
          truncateStringLength: 6,
          value: "Designation",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ministerDesignation",
    },
    {
      label: t("table:Portfolio"),
      submenu: [
        {
          comboboxOptions: portfolioOptions,
          label: "Portfolio",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Portfolio",
          truncateStringLength: 6,
          value: "Portfolio",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "portfolio",
    },
  ];

  const columns = () => getTableColumns({ ActionCell });

  const renderTableContent = () => {
    if (noticeBankLoading) {
      return <SpinnerLoader />;
    }

    return (
      <>
        <DataTable
          columns={columns({ t })}
          data={tableData()}
          onRowClick={(row) => {
            if (!row) return;

            // The API might return either 'type' or 'noticeType'
            const docType = row.type || row.noticeType;
            const id = row.id;

            if (!docType || !id) {
              toast.error("Missing Data", {
                description: "Could not find notice type or ID",
              });
              return;
            }

            const matchedNoticeType = noticeTypes.find(
              (doc) => doc.value === docType,
            );

            trigger({ documentId: id }).then(() => {
              if (matchedNoticeType) {
                const route = getNoticeRoute(matchedNoticeType.value, "view", isPPOContext ? "ppo" : "member");
                navigate(`${route}${id}`);
              } else {
                toast.error("Route Not Found", {
                  description: `Invalid Route For : ${routeHelpers.getFormattedDocumentType(
                    docType,
                  )}`,
                });
              }
            });
          }}
        />

        <div className="flex justify-between mt-4">
          <div className="flex gap-4 items-center">
            <span className="typography-body-text-s-14 text-grey-600">
              {getDisplayedPageInfo()}
            </span>
            <PaginationSelect
              onChange={handlePageSizeChange}
              defaultValue={size.toString()}
              options={paginationOptions}
            />
          </div>
          <div>
            <Paginator
              currentPage={page + 1}
              totalPages={otherNoticesData?.totalPages || 0}
              onPageChange={(newPage) => handlePageChange(newPage - 1)}
              showPreviousNext={true}
            />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div className="flex justify-between items-center mt-4">
        <div className="relative w-80">
          <Input
            icon={Search}
            className="w-full"
            reserveErrorSpace={false}
            placeholder="Search"
            size="md"
            value={searchInput}
            onChange={handleSearchInputChange}
            onKeyDown={handleSearchKeyDown}
          />
        </div>
        <div className="flex gap-2">
          <FilterDropdown
            badgeContainerClassName=""
            options={noticeBankFilterOptions}
            onValueChange={setFilteredData}
            disabled={noticeBankLoading}
            defaultValue={filters}
          />
        </div>
      </div>

      <div className="mt-4">{renderTableContent()}</div>
    </>
  );
}

ShortNoticeBanks.propTypes = {
  currentTab: PropTypes.string,
};

export default ShortNoticeBanks;
