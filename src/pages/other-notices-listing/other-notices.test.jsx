import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { MemoryRouter, useSearchParams } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import PropTypes from "prop-types";
import MyOtherNotices from "../other-notices-listing/other-notices";

vi.mock("@/hooks", () => ({
  useLanguage: () => ({
    t: (key) => (key.includes(":") ? key.split(":")[1] : key),
  }),
  useDebounce: (value) => value,
  useActiveAssembly: () => ({
    activeAssembly: { assembly: "15", session: "13" },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}));

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useSearchParams: vi.fn(),
  };
});

vi.mock("@/utils", () => ({
  formatDateYYYYMMDD: (date) => (date ? date.toISOString().split("T")[0] : ""),
}));

vi.mock("../../services/other-notices-my-notice", () => ({
  useGetShortNoticeMyNoticeQuery: vi.fn().mockReturnValue({
    data: {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
          createdDate: "2025-01-15",
          questionDate: "2025-01-20",
          status: "DRAFT",
        },
        {
          id: 2,
          noticeType: "Half an Hour",
          currentNumber: "HH-002",
          noticeHeading: "Test Notice 2",
          ministerDesignation: "Home Minister",
          ministerPortfolio: "Home Affairs",
          createdDate: "2025-01-16",
          questionDate: "2025-01-21",
          status: "SUBMITTED",
        },
      ],
      totalElements: 2,
      totalPages: 1,
    },
    isLoading: false,
    error: null,
  }),
}));

vi.mock("../../services/master-data-management/assembly", () => ({
  useGetAssemblyQuery: vi.fn().mockReturnValue({
    data: [
      { title: "15", id: 1 },
      { title: "14", id: 2 },
    ],
    isLoading: false,
  }),
  useGetSessionQuery: vi.fn().mockReturnValue({
    data: [
      { title: "13", id: 1 },
      { title: "12", id: 2 },
    ],
    isLoading: false,
  }),
}));

vi.mock("../../services/master-data-management/basic-details-notice", () => ({
  useGetPortfolioQuery: vi.fn().mockReturnValue({
    data: [
      { title: "Finance", id: 1 },
      { title: "Home Affairs", id: 2 },
    ],
  }),
  useGetDesignationQuery: vi.fn().mockReturnValue({
    data: [
      { title: "Finance Minister", id: 1 },
      { title: "Home Minister", id: 2 },
    ],
  }),
}));

vi.mock("lucide-react", () => ({
  Pencil: () => <div data-testid="pencil-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Delete: () => <div data-testid="delete-icon" />,
  FileClock: () => <div data-testid="file-clock-icon" />,
}));

vi.mock("@kla-v2/ui-components", () => {
  const DataTable = ({ columns, data }) => (
    <div data-testid="data-table">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c) => c.header || c.id))}
      </div>
      <div data-testid="rows">{JSON.stringify(data)}</div>
    </div>
  );

  DataTable.propTypes = {
    columns: PropTypes.array.isRequired,
    data: PropTypes.array.isRequired,
  };

  const TableDropdownMenu = ({ row, menuItems }) => (
    <div data-testid="dropdown-menu">
      <span>Actions for row: {row.original.id}</span>
      <div data-testid="menu-items">{menuItems.length} items</div>
    </div>
  );

  TableDropdownMenu.propTypes = {
    row: PropTypes.object.isRequired,
    menuItems: PropTypes.array.isRequired,
  };

  const Input = ({ placeholder, value, onChange, onKeyDown }) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
    />
  );

  Input.propTypes = {
    icon: PropTypes.element,
    placeholder: PropTypes.string,
    value: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    onKeyDown: PropTypes.func,
  };

  const FilterDropdown = ({ options, onValueChange, disabled }) => (
    <div
      data-testid="filter-dropdown"
      data-options={options.length}
      data-disabled={disabled}
      onClick={() =>
        onValueChange({
          target: { name: "noticeType", value: "Short Notice" },
        })
      }
    >
      Filter
    </div>
  );

  FilterDropdown.propTypes = {
    options: PropTypes.array.isRequired,
    onValueChange: PropTypes.func.isRequired,
    disabled: PropTypes.bool,
    badgeContainerClassName: PropTypes.string,
  };

  const PaginationSelect = ({ onChange, defaultValue, options }) => (
    <select
      data-testid="pagination-select"
      defaultValue={defaultValue}
      onChange={(e) => onChange(e.target.value)}
    >
      {options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );

  PaginationSelect.propTypes = {
    onChange: PropTypes.func.isRequired,
    defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
      .isRequired,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
          .isRequired,
        label: PropTypes.string.isRequired,
      }),
    ).isRequired,
  };

  const Paginator = ({
    currentPage,
    totalPages,
    onPageChange,
    showPreviousNext,
  }) => (
    <div
      data-testid="paginator"
      data-current-page={currentPage}
      data-total-pages={totalPages}
      data-show-previous-next={showPreviousNext}
    >
      <button
        data-testid="prev-page"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </button>
      <span data-testid="page-indicator">
        {currentPage} of {totalPages}
      </span>
      <button
        data-testid="next-page"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        Next
      </button>
    </div>
  );

  Paginator.propTypes = {
    currentPage: PropTypes.number.isRequired,
    totalPages: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired,
    showPreviousNext: PropTypes.bool,
  };

  const TableTag = ({ variant, children, className }) => (
    <span data-testid={`table-tag-${variant}`} className={className}>
      {children}
    </span>
  );

  TableTag.propTypes = {
    variant: PropTypes.string.isRequired,
    children: PropTypes.node.isRequired,
    className: PropTypes.string,
  };

  return {
    DataTable,
    TableDropdownMenu,
    Input,
    FilterDropdown,
    PaginationSelect,
    Paginator,
    TableTag,
  };
});

const createMockStore = () => {
  return configureStore({
    reducer: (state = {}) => state,
  });
};

describe("MyOtherNotices Component", async () => {
  let mockStore;
  let mockSetSearchParams;
  let mockSearchParams;
  const { useGetShortNoticeMyNoticeQuery } = vi.mocked(
    await import("../../services/other-notices-my-notice"),
  );

  const setupMocks = (initialParams = {}) => {
    mockSearchParams = new URLSearchParams();
    Object.entries(initialParams).forEach(([key, value]) => {
      mockSearchParams.set(key, value);
    });

    mockSetSearchParams = vi.fn((newParamsOrUpdater) => {
      if (typeof newParamsOrUpdater === "function") {
        const updatedParams = newParamsOrUpdater(mockSearchParams);
        for (const [key, value] of updatedParams.entries()) {
          mockSearchParams.set(key, value);
        }
        return;
      }

      mockSearchParams = new URLSearchParams(newParamsOrUpdater);
    });

    useSearchParams.mockReturnValue([mockSearchParams, mockSetSearchParams]);
  };

  beforeEach(() => {
    mockStore = createMockStore();
    setupMocks();

    vi.clearAllMocks();
  });

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <MyOtherNotices {...props} />
        </MemoryRouter>
      </Provider>,
    );
  };

  it("renders the component correctly with default state", () => {
    renderComponent();

    expect(screen.getByTestId("search-input")).toBeInTheDocument();
    expect(screen.getByTestId("filter-dropdown")).toBeInTheDocument();
    expect(screen.getByTestId("data-table")).toBeInTheDocument();
    expect(screen.getByTestId("pagination-select")).toBeInTheDocument();
    expect(screen.getByTestId("paginator")).toBeInTheDocument();
  });
  it("handles search input correctly", async () => {
    renderComponent();

    const searchInput = screen.getByTestId("search-input");
    fireEvent.change(searchInput, { target: { value: "test search" } });

    expect(searchInput.value).toBe("test search");

    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
    });
  });

  it("handles escape key in search input correctly", async () => {
    renderComponent();

    const searchInput = screen.getByTestId("search-input");
    fireEvent.change(searchInput, { target: { value: "test search" } });
    fireEvent.keyDown(searchInput, { key: "Escape" });

    expect(searchInput.value).toBe("");
    expect(mockSetSearchParams).toHaveBeenCalled();

    await waitFor(() => {
      const updatedParams = new URLSearchParams(mockSearchParams);
      expect(updatedParams.has("search")).toBe(false);
    });
  });

  it("handles pagination correctly", async () => {
    const mockData = {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
          createdDate: "2025-01-15",
          questionDate: "2025-01-20",
          status: "DRAFT",
        },
      ],
      totalElements: 25,
      totalPages: 3,
    };

    useGetShortNoticeMyNoticeQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
    });

    setupMocks({ page: "1", size: "10" });
    renderComponent();

    const nextPageButton = screen.getByTestId("next-page");
    fireEvent.click(nextPageButton);

    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
    });
    expect(mockSearchParams.get("page")).toBe("2");
  });

  it("handles page size change correctly", async () => {
    setupMocks({ page: "1", size: "10" });
    renderComponent();

    const pageSizeSelect = screen.getByTestId("pagination-select");
    fireEvent.change(pageSizeSelect, { target: { value: "50" } });

    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
    });

    expect(mockSearchParams.get("size")).toBe("50");
    expect(mockSearchParams.get("page")).toBe("1");
  });

  it("displays the correct page info", () => {
    const mockData = {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
          createdDate: "2025-01-15",
          questionDate: "2025-01-20",
          status: "DRAFT",
        },
      ],
      totalElements: 25,
      totalPages: 3,
    };

    useGetShortNoticeMyNoticeQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
    });

    setupMocks({ page: "2", size: "10" });
    renderComponent();
    expect(screen.getByText("Showing 11 - 20 of 25")).toBeInTheDocument();
  });

  it("processes URL parameters correctly on initial load", () => {
    setupMocks({ page: "3", size: "50", search: "test query" });
    renderComponent();

    expect(useGetShortNoticeMyNoticeQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 2,
        size: 50,
        search: "test query",
      }),
    );
  });

  it("renders status cells correctly", () => {
    const mockData = {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
          createdDate: "2025-01-15",
          questionDate: "2025-01-20",
          status: "DRAFT",
        },
        {
          id: 2,
          noticeType: "Half an Hour",
          currentNumber: "HH-002",
          noticeHeading: "Test Notice 2",
          ministerDesignation: "Home Minister",
          portfolio: "Home Affairs",
          createdDate: "2025-01-16",
          questionDate: "2025-01-21",
          status: "SUBMITTED",
        },
      ],
      totalElements: 2,
      totalPages: 1,
    };

    useGetShortNoticeMyNoticeQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
    });

    renderComponent();

    const dataTable = screen.getByTestId("data-table");
    expect(dataTable).toBeInTheDocument();

    const rows = screen.getByTestId("rows");
    const rowsData = JSON.parse(rows.textContent);

    expect(rowsData[0].status).toBe("DRAFT");
    expect(rowsData[1].status).toBe("SUBMITTED");
  });

  it("accepts and passes props correctly", () => {
    const testProps = { currentTab: "myNotices" };
    renderComponent(testProps);

    expect(screen.getByTestId("data-table")).toBeInTheDocument();
  });
});
