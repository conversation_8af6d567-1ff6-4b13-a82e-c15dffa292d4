import { routeHelpers } from "@/config/routes";

export const getTableColumns = ({ ActionCell }) => [
  {
    accessorKey: "currentNumber",
    header: "Sl No.",
    meta: { className: "min-w-28" },
  },
  {
    accessorKey: "type",
    header: "Notice Type",
    cell: ({ row }) => {
      const value = row.getValue("type");
      return value ? routeHelpers.getFormattedDocumentType(value) : "";
    },
  },
  {
    accessorKey: "name",
    header: "Notice Heading",
    meta: { className: "min-w-48 py-3" },
  },
  {
    accessorKey: "ministerDesignation",
    header: "Designation",
    cell: ({ row }) => {
      const value = row.getValue("ministerDesignation");
      const localValue = row.original.ministerDesignationInLocal;
      return localValue ? `${value} (${localValue})` : value;
    },
  },
  {
    accessorKey: "portfolio",
    header: "Minister Port<PERSON>lio",
    cell: ({ row }) => {
      const value = row.getValue("portfolio");
      const localValue = row.original.portfolioInLocal;
      return localValue ? `${value} (${localValue})` : value;
    },
  },
  {
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      return ActionCell({ row });
    },
    meta: { className: "text-center w-[120px]" },
  },
];
