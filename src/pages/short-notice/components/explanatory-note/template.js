const getHandlerNoticeTemplate = ({ templateValues, explanatoryDetails }) => {
  const clauseLabels = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

  const renderClauses = (clauses = []) => {
    return clauses
      .map((clause, index) => {
        const label = clauseLabels[index] || `(${index + 1})`;
        return `
          <div class="typography-body-text-r-14 flex gap-1 mb-2">
            <span>(${label})</span>
            <span>${clause.content}</span>
          </div>
        `;
      })
      .join("");
  };
  return `

  <div style="font-family: 'Noto Sans Malayalam', sans-serif; line-height: 1.5; padding: 20px;  #ddd; width: 100%;">
<div class="flex flex-col gap-y-2">
        <h2 class="text-center typography-body-text-s-16 text-foreground leading-snug">
          കേരള നിയമസഭ <br /> അടിയന്തര ചോദ്യം
        </h2>
      </div>
      <div class="flex flex-col gap-y-2 mt-8">
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">നോട്ടീസ് തീയതി:</span>
          <span class="typography-body-text-s-14 text-foreground">
${explanatoryDetails?.noticeDate || ""}
      </span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">വിഷയം:</span>
          <span class="typography-body-text-s-14 text-foreground">
${explanatoryDetails?.portfolio || ""}
          </span>
        </div>
      </div>

      <div class="flex flex-col gap-y-2 items-end">
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">തീയതി:</span>
          <span class="typography-body-text-s-14 text-foreground">
${explanatoryDetails?.noticeDate || ""}
          </span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">സ്ഥലം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.place || ""}
          </span>
        </div>
      </div>

      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-y-4">
          <div class="flex gap-2 mt-2">
            <span class="typography-body-text-s-14">
             ${explanatoryDetails?.member?.memberDisplayNameInLocal || ""}
</span>
            <span class="typography-body-text-r-14 text-foreground">
              എം എൽ എ
            </span>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="typography-body-text-r-14">
            നിയമസഭാ സെക്രട്ടറിക്ക്,
          </span>
          <span class="typography-body-text-r-14">തിരുവനതപുരം.</span>
        </div>
        <div class="flex">
          <span class="typography-body-text-r-14">സർ,</span>
        </div>
      </div>
      <div>
       ${templateValues}
      </div>

      <div class="flex flex-col gap-y-2 items-end">
        <div class="flex">
          <span class="typography-body-text-r-14">വിശ്വസ്ഥതയോടെ</span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">അംഗം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.member?.memberDisplayNameInLocal || ""}
          </span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">നിയോജകമണ്ഡലം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.member?.constituencyNameInLocal || ""}
          </span>
        </div>
      </div>

      <div class="flex flex-col gap-4 mt-6">
        <div class="flex justify-center">
          <span class="typography-body-text-s-18 text-foreground text-center">
            അടിയന്തര ചോദ്യം
          </span>
        </div>
        <div class="typography-body-text-r-14">
          <span>
         ${explanatoryDetails?.noticeHeading || ""}
          </span>
        </div>
        <div class="typography-body-text-r-14 flex gap-1">
          <span>
          ${renderClauses(explanatoryDetails?.clauses)}
          </span>
        </div>
      </div>
    </div>


  </div>
`;
};

export default getHandlerNoticeTemplate;
