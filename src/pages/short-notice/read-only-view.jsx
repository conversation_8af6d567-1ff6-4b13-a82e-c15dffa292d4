import { DocumentMetadata } from "@/components/document-metadata";
import { BasicDetailsReadOnly } from "@/components/read-only/basic-details-read-only";
import { NoriceDetailsReadOnly } from "@/components/read-only/notice-details-read-only";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useGetShortNoticeQuery } from "@/services/short-notice";
import { useContextualNavigation } from "@/utils/navigation";
import { ROUTE_CONFIG, routeHelpers } from "@/config/routes";
import { formatDateString } from "@/utils/date-utils";
import {
  Button,
  ExpandableAccordion,
  ExpandableItem,
} from "@kla-v2/ui-components";
import { useParams } from "react-router-dom";
import { MoveRightIcon, MoveLeftIcon } from "lucide-react";

export default function ShortNoticeReadOnlyView() {
  const { documentId } = useParams();

  const { data: documentData } = useGetShortNoticeQuery({ documentId });
  const { t } = useLanguage();
  const { navigate } = useContextualNavigation();

  useAutoBreadcrumb();

  const goBack = () => {
    navigate(ROUTE_CONFIG.MEMBER.MY_NOTICES);
  };


  const amendedMetadata = {
    ...documentData,
    createdOn: formatDateString(documentData?.createdAt),
    createdBy: documentData?.createdBy || "",
    isDocTypeAdded: true,
    documentType: routeHelpers.getFormattedDocumentType(
      documentData?.type || "Questions to Private Member",
    ),
    name: documentData?.name || "Create Questions to Private Member",
  };

  return (
    <div className="px-5 py-3 bg-background flex flex-col min-h-screen">
      <div className="flex justify-between">
        <h4 className="typography-page-heading">{t(documentData?.name)}</h4>
      </div>
      <div className="grow overflow-hidden flex flex-col  pb-8">
        <div className="w-full py-5">
          <DocumentMetadata documentMetadata={amendedMetadata} />
        </div>
        <ExpandableAccordion
          type="multiple"
          className="w-full flex flex-col gap-4"
          defaultValue={["item-1"]}
        >
          <ExpandableItem value="item-1">
            <BasicDetailsReadOnly
              accordionOrderNo={1}
              data={amendedMetadata}
              variant="shortNotice"
            />
          </ExpandableItem>
          <ExpandableItem value="item-2">
            <NoriceDetailsReadOnly accordionOrderNo={2} data={documentData} />
          </ExpandableItem>
        </ExpandableAccordion>
        <div className="flex justify-end items-center gap-4 py-4 bg-background">
          <Button
            variant="secondary"
            onClick={goBack}
            icon={MoveLeftIcon}
            iconPosition="left"
            size="lg"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            icon={MoveRightIcon}
            iconPosition="right"
            size="lg"
            onClick={() => true}
          >
            Button
          </Button>
        </div>
      </div>
    </div>
  );
}
