import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import DateShiftingModal from "./dateshift-modal";

vi.mock("@kla-v2/ui-components", () => ({
  Button: vi.fn(({ children, type, variant, disabled, onClick }) => (
    <button
      data-testid={`button-${variant || "default"}`}
      type={type}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  )),
  RadioGroup: vi.fn(({ children, onValueChange }) => (
    <div
      data-testid="radio-group"
      onChange={(e) => onValueChange && onValueChange(e.target.value)}
    >
      {children}
    </div>
  )),
  RadioGroupItem: vi.fn(({ value, id }) => (
    <input type="radio" value={value} id={id} data-testid={`radio-${id}`} />
  )),
  DatePicker: vi.fn(({ label, value, onChange }) => (
    <div
      data-testid={`date-picker-${label.replace(/\s+/g, "-").toLowerCase()}`}
    >
      <label>{label}</label>
      <input
        type="date"
        value={value || ""}
        onChange={(e) => onChange && onChange(e.target.value)}
        data-testid={`date-input-${label.replace(/\s+/g, "-").toLowerCase()}`}
      />
    </div>
  )),
  Dialog: vi.fn(({ children, open }) =>
    open ? <div data-testid="dialog">{children}</div> : null,
  ),
  DialogPortal: vi.fn(({ children }) => (
    <div data-testid="dialog-portal">{children}</div>
  )),
  DialogContent: vi.fn(({ children, className }) => (
    <div data-testid="dialog-content" className={className}>
      {children}
    </div>
  )),
  DialogHeader: vi.fn(({ children }) => (
    <div data-testid="dialog-header">{children}</div>
  )),
  DialogTitle: vi.fn(({ children }) => (
    <div data-testid="dialog-title">{children}</div>
  )),
  DialogDescription: vi.fn(({ children, className }) => (
    <div data-testid="dialog-description" className={className}>
      {children}
    </div>
  )),
  DialogFooter: vi.fn(({ children, className }) => (
    <div data-testid="dialog-footer" className={className}>
      {children}
    </div>
  )),
  DialogClose: vi.fn(({ children }) => (
    <div data-testid="dialog-close">{children}</div>
  )),
  toast: {
    success: vi.fn(),
  },
}));

vi.mock("@/components/ui/form", () => ({
  FormProvider: vi.fn(({ children }) => (
    <div data-testid="form-provider">{children}</div>
  )),
  FormField: vi.fn(({ name, render }) => {
    const field = { name, value: "", onChange: vi.fn() };
    return <div data-testid={`form-field-${name}`}>{render({ field })}</div>;
  }),
  FormItem: vi.fn(({ children, className }) => (
    <div data-testid="form-item" className={className}>
      {children}
    </div>
  )),
  FormLabel: vi.fn(({ children, htmlFor }) => (
    <label data-testid={`form-label-${htmlFor}`} htmlFor={htmlFor}>
      {children}
    </label>
  )),
  FormControl: vi.fn(({ children }) => (
    <div data-testid="form-control">{children}</div>
  )),
}));

vi.mock("react-hook-form", () => {
  const originalModule = vi.importActual("react-hook-form");

  return {
    ...originalModule,
    useForm: vi.fn(() => ({
      handleSubmit: vi.fn((fn) => (e) => {
        e?.preventDefault();
        return fn();
      }),
      watch: vi.fn((name) => {
        if (name === "shiftOption") return mockShiftOption;
        if (name === "selectDate") return mockSelectDate;
        if (name === "fromDate") return mockFromDate;
        if (name === "toDate") return mockToDate;
        return "";
      }),
      reset: vi.fn(),
      register: vi.fn(),
      control: {},
      formState: { errors: {} },
    })),
    FormProvider: ({ children }) => children,
  };
});

let mockShiftOption = "anotherDate";
let mockSelectDate = "";
let mockFromDate = "";
let mockToDate = "";

describe("DateShiftingModal", () => {
  const onCloseMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockShiftOption = "anotherDate";
    mockSelectDate = "";
    mockFromDate = "";
    mockToDate = "";
  });

  it("renders when isOpen is true", () => {
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    expect(screen.getByTestId("dialog")).toBeInTheDocument();
    expect(screen.getByTestId("dialog-title")).toBeInTheDocument();
    expect(screen.getByText("Move to")).toBeInTheDocument();
    expect(screen.getByTestId("dialog-description")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Choose an option for the date move in allotment of days",
      ),
    ).toBeInTheDocument();
  });

  it("does not render when isOpen is false", () => {
    render(<DateShiftingModal isOpen={false} onClose={onCloseMock} />);
    expect(screen.queryByTestId("dialog")).not.toBeInTheDocument();
  });

  it("renders all radio options correctly", () => {
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    expect(screen.getByTestId("radio-anotherDate")).toBeInTheDocument();
    expect(screen.getByTestId("radio-serialDate")).toBeInTheDocument();
    expect(screen.getByTestId("radio-lateAnswer")).toBeInTheDocument();

    expect(screen.getByTestId("form-label-anotherDate")).toHaveTextContent(
      "Another Date Shifting",
    );
    expect(screen.getByTestId("form-label-serialDate")).toHaveTextContent(
      "Serial Date Shifting",
    );
    expect(screen.getByTestId("form-label-lateAnswer")).toHaveTextContent(
      "Late Answer Bulletin",
    );
  });

  it('shows the appropriate date picker for "anotherDate" option', () => {
    mockShiftOption = "anotherDate";
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    expect(screen.getByTestId("date-picker-select-date")).toBeInTheDocument();
    expect(
      screen.queryByTestId("date-picker-from-date"),
    ).not.toBeInTheDocument();
    expect(screen.queryByTestId("date-picker-to-date")).not.toBeInTheDocument();
  });

  it('shows the appropriate date pickers for "serialDate" option', () => {
    mockShiftOption = "serialDate";
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    expect(
      screen.queryByTestId("date-picker-select-date"),
    ).not.toBeInTheDocument();
    expect(screen.getByTestId("date-picker-from-date")).toBeInTheDocument();
    expect(screen.getByTestId("date-picker-to-date")).toBeInTheDocument();
  });

  it('shows no date pickers for "lateAnswer" option', () => {
    mockShiftOption = "lateAnswer";
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    expect(
      screen.queryByTestId("date-picker-select-date"),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("date-picker-from-date"),
    ).not.toBeInTheDocument();
    expect(screen.queryByTestId("date-picker-to-date")).not.toBeInTheDocument();
  });

  it("disables Apply button when required fields are empty for anotherDate option", () => {
    mockShiftOption = "anotherDate";
    mockSelectDate = "";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).toBeDisabled();
  });

  it("enables Apply button when required fields are filled for anotherDate option", () => {
    mockShiftOption = "anotherDate";
    mockSelectDate = "2025-04-30";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).not.toBeDisabled();
  });

  it("disables Apply button when required fields are empty for serialDate option", () => {
    mockShiftOption = "serialDate";
    mockFromDate = "";
    mockToDate = "";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).toBeDisabled();
  });

  it("disables Apply button when only fromDate is filled for serialDate option", () => {
    mockShiftOption = "serialDate";
    mockFromDate = "2025-04-30";
    mockToDate = "";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).toBeDisabled();
  });

  it("disables Apply button when only toDate is filled for serialDate option", () => {
    mockShiftOption = "serialDate";
    mockFromDate = "";
    mockToDate = "2025-05-15";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).toBeDisabled();
  });

  it("enables Apply button when all required fields are filled for serialDate option", () => {
    mockShiftOption = "serialDate";
    mockFromDate = "2025-04-30";
    mockToDate = "2025-05-15";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).not.toBeDisabled();
  });

  it("enables Apply button for lateAnswer option regardless of date fields", () => {
    mockShiftOption = "lateAnswer";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    expect(applyButton).not.toBeDisabled();
  });

  it("calls onClose when Cancel button is clicked", async () => {
    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);
  });

  it("calls handleApply when Apply button is clicked and conditions are met", async () => {
    const { toast } = await import("@kla-v2/ui-components");

    mockShiftOption = "anotherDate";
    mockSelectDate = "2025-04-30";

    render(<DateShiftingModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    fireEvent.click(applyButton);

    expect(toast.success).toHaveBeenCalledWith("Date shifted successfully");
    expect(onCloseMock).toHaveBeenCalled();
  });
});
