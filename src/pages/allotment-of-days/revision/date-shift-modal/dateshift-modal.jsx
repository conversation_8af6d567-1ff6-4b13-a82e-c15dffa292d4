import PropTypes from "prop-types";
import {
  Button,
  RadioGroup,
  RadioGroupItem,
  DatePicker,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  toast,
} from "@kla-v2/ui-components";
import { FormProvider, useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";

const DateShiftingModal = ({ isOpen, onClose }) => {
  const form = useForm({
    defaultValues: {
      shiftOption: "anotherDate",
      selectDate: "",
      fromDate: "",
      toDate: "",
    },
  });

  const { handleSubmit, watch, reset } = form;

  const selectedOption = watch("shiftOption");
  const selectDate = watch("selectDate");
  const fromDate = watch("fromDate");
  const toDate = watch("toDate");

  const isApplyDisabled = () => {
    switch (selectedOption) {
      case "anotherDate":
        return !selectDate;
      case "serialDate":
        return !fromDate || !toDate;
      case "lateAnswer":
        return false;
      default:
        return false;
    }
  };

  const handleApply = () => {
    toast.success("Date shifted successfully");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent
          className="w-[600px] max-w-[50vw]"
          onCloseAutoFocus={() => reset()}
        >
          <DialogHeader>
            <DialogTitle>Move to</DialogTitle>
            <DialogDescription className="text-gray-500">
              Choose an option for the date move in allotment of days
            </DialogDescription>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>

          <FormProvider {...form}>
            <form
              onSubmit={handleSubmit(handleApply)}
              className="space-y-4"
              role="form"
              data-testid="date-shifting-form"
            >
              <FormField
                name="shiftOption"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex justify-between"
                      >
                        <div className="flex items-center mr-2">
                          <RadioGroupItem
                            value="anotherDate"
                            id="anotherDate"
                          />
                          <FormLabel htmlFor="anotherDate">
                            Another Date Shifting
                          </FormLabel>
                        </div>
                        <div className="flex items-center mr-2">
                          <RadioGroupItem value="serialDate" id="serialDate" />
                          <FormLabel htmlFor="serialDate">
                            Serial Date Shifting
                          </FormLabel>
                        </div>
                        <div className="flex items-center mr-2">
                          <RadioGroupItem value="lateAnswer" id="lateAnswer" />
                          <FormLabel htmlFor="lateAnswer">
                            Late Answer Bulletin
                          </FormLabel>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              {selectedOption === "anotherDate" && (
                <FormField
                  name="selectDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="mt-6">
                          <DatePicker
                            label="Select Date"
                            mode="single"
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}

              {selectedOption === "serialDate" && (
                <>
                  <FormField
                    name="fromDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div>
                            <DatePicker
                              label="From Date"
                              mode="single"
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    name="toDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div>
                            <DatePicker
                              label="To Date"
                              mode="single"
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </>
              )}

              <DialogFooter className="flex">
                <DialogClose asChild>
                  <Button variant="neutral">Cancel</Button>
                </DialogClose>
                <Button
                  variant="primary"
                  type="submit"
                  disabled={isApplyDisabled()}
                >
                  Apply
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

DateShiftingModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default DateShiftingModal;
