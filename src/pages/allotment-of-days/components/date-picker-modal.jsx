import { useState } from "react";
import PropTypes from "prop-types";
import {
  Dialog,
  DialogPortal,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  Button,
  Calendar,
} from "@kla-v2/ui-components";
import { Loader2 } from "lucide-react";
import { format } from "date-fns";

export default function DatePickerModal({ isOpen, onClose, onSelectDate }) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleConfirm = () => {
    setIsSubmitting(true);

    try {
      // Format date to match backend expectations (DD/MM/YYYY)
      const formattedDate = format(selectedDate, "dd/MM/yyyy");

      // Simulate a brief delay for better UX
      setTimeout(() => {
        onSelectDate(formattedDate);
        setIsSubmitting(false);
        onClose();
      }, 300);
    } catch (error) {
      console.error("Error formatting date:", error);
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Date</DialogTitle>
            <DialogDescription>
              Choose a date to assign to this minister group
            </DialogDescription>
          </DialogHeader>

          <div className="p-4 flex justify-center">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              className="rounded-md border"
              initialFocus
            />
          </div>

          <DialogFooter className="sm:justify-end gap-2">
            <Button variant="neutral" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Confirming...
                </>
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

DatePickerModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectDate: PropTypes.func.isRequired,
};
