import { useMemo } from "react";
import PropTypes from "prop-types";
import { GroupBadge } from "@/components/group-badge";

export default function MinisterGroupCard({ group }) {
  const formatDateForDisplay = (dateString) => {
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
      return dateString;
    }

    try {
      const date = new Date(dateString);
      return date
        .toLocaleDateString("en-GB", {
          day: "numeric",
          month: "numeric",
          year: "numeric",
        })
        .replace(/\//g, "/");
    } catch (e) {
      console.error(e);
      return dateString;
    }
  };

  // Organize dates into columns
  const dateColumns = useMemo(() => {
    if (!group.dates || group.dates.length === 0) return [];

    const columns = [];
    for (let i = 0; i < group.dates.length; i += 4) {
      columns.push(group.dates.slice(i, i + 4));
    }

    return columns;
  }, [group.dates]);

  return (
    <div className="p-4 bg-white border rounded-md border-border-1">
      <div className="flex items-center mb-4">
        <GroupBadge groupNumber={group.name.replace("Group ", "")} />
      </div>

      <div className="flex justify-between">
        <div className="flex-1 space-y-3 max-w-fit">
          {group.ministers.map((minister, index) => (
            <div key={minister.id || index} className="flex">
              <div className="flex-shrink-0 mr-3">
                <div className="overflow-hidden bg-gray-200 rounded-full w-9 h-9">
                  <img
                    src={"https://placehold.co/400"}
                    alt={minister.designation}
                    className="object-cover w-full h-full"
                  />
                </div>
              </div>
              <div>
                <div className="typography-body-text-m-16">
                  {minister.designation}
                </div>
                <div className="typography-body-text-r-14 text-grey-400">
                  {minister.displayName}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Dates organized in vertical columns on the right side */}
        {dateColumns.length > 0 && (
          <div className="flex flex-col justify-center gap-2 ">
            {dateColumns.map((column, colIndex) => (
              <div
                key={`col-${colIndex}`}
                className="flex flex-row-reverse gap-2"
              >
                {column.map((date, idx) => (
                  <div
                    key={`${date}-${idx}`}
                    className="h-7 w-[99px] inline-flex items-center px-3 py-2 rounded-full bg-white border border-gray-400 typography-para-14"
                  >
                    {formatDateForDisplay(date)}
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

MinisterGroupCard.propTypes = {
  group: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    ministers: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        displayName: PropTypes.string.isRequired,
        designation: PropTypes.string.isRequired,
      }),
    ).isRequired,
    dates: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};
