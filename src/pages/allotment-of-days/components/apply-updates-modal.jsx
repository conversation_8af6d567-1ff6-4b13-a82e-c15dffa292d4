import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogPortal,
  toast,
  Badge,
  MultiSelectDropdown,
} from "@kla-v2/ui-components";
import { useState, useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";
import CalendarIcon from "@/icons/calendar-icon";
import { GroupBadge } from "../../../components/group-badge";
import ErrorCircleIcon from "@/icons/error-circle-icon";

const ApplyUpdatesModal = ({ isOpen, onClose, isPreview = false }) => {
  const { t } = useLanguage();
  const [groups, setGroups] = useState([]);
  const [assignedGroups, setAssignedGroups] = useState({});

  const rows = useMemo(
    () => [
      {
        date: "10/06/2024",
        status: "Canceled",
        statusType: "danger",
        group: 1,
      },
      {
        date: "12/06/2024",
        status: "Canceled",
        statusType: "danger",
        group: 2,
      },
      {
        date: "21/06/2024",
        status: "Newly Added",
        statusType: "success",
        group: null,
      },
    ],
    [],
  );

  useEffect(() => {
    // Generate group options (Group 1 to Group 5)
    const groupOptions = Array.from({ length: 5 }, (_, index) => ({
      label: <GroupBadge groupNumber={index + 1} />,
      value: index + 1,
      group: "",
    }));
    setGroups(groupOptions);
  }, []);

  useEffect(() => {
    const initialAssignments = {};
    rows.forEach((row, idx) => {
      if (row.group === null) {
        initialAssignments[idx] = [4];
      }
    });
    setAssignedGroups(initialAssignments);
  }, [rows]);

  const handleSubmit = () => {
    try {
      onClose();
      toast.success("Success", { description: t("Submitted") });
    } catch (err) {
      toast.error("Error submitting document", {
        description: err?.message,
      });
    }
  };

  const handleRemoveGroup = (index) => {
    const updatedGroups = { ...assignedGroups };
    updatedGroups[index] = [];
    setAssignedGroups(updatedGroups);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[1255px] h-[443px] flex flex-col">
          <DialogHeader>
            <DialogTitle className="px-2">
              Updates from Approved Calendar of Sitting
            </DialogTitle>
            <div className="pt-1 px-2 text-sm text-gray-400">
              Updated by Question Section Assistant on 12-10-2024, 2:00 am
            </div>
          </DialogHeader>

          <div className="flex-2 overflow-y-auto w-full">
            <div className="flex justify-between text-gray-600 text-sm py-2">
              <div className="w-[220px] flex justify-between px-3">
                <div className="text-left text-gray-400">Date</div>
                <div className="text-left text-gray-400">Status</div>
              </div>
              <div className=" mr-2 text-right text-gray-400">
                Assigned Groups
              </div>
            </div>

            {rows.map((row, index) => (
              <div
                key={index}
                className="grid grid-cols-12 items-center px-2 py-3 border border-gray-200 rounded mb-3 bg-white"
              >
                {/* Date and Calendar Icon */}
                <div className="col-span-7 flex items-center gap-3">
                  <div
                    className={`flex items-center gap-2 text-sm font-normal ${
                      row.statusType === "danger"
                        ? "text-error"
                        : "text-success"
                    }`}
                    style={{ fontFamily: "Inter" }}
                  >
                    <div className="px-2">{row.date}</div>
                    <div>
                      <CalendarIcon statusType={row.statusType} />
                    </div>
                  </div>

                  {/* Status */}
                  <div className="col-span-4 flex items-center text-sm">
                    <Badge
                      variant="solid"
                      className={`text-sm flex w-[105px] h-[30px] justify-center items-center gap-[10px] ${
                        row.statusType === "danger"
                          ? "bg-[#FEF3F2] text-error"
                          : "bg-[#E4F9EC] text-success"
                      }`}
                    >
                      {row.status}
                    </Badge>
                  </div>
                </div>
                {/* Assigned Groups */}
                <div className="col-span-5 flex justify-end items-center gap-2 text-sm">
                  {row.group ? (
                    <GroupBadge groupNumber={row.group} />
                  ) : (
                    <div className="flex items-center gap-2 h-[20px]">
                      {assignedGroups[index]?.length > 0 ? (
                        <>
                          <GroupBadge groupNumber={assignedGroups[index][0]} />
                          <ErrorCircleIcon
                            onClick={() => handleRemoveGroup(index)}
                          />
                        </>
                      ) : (
                        <>
                          <span className="text-gray-400">Not Assigned</span>
                          <MultiSelectDropdown
                            options={groups}
                            value={assignedGroups[index] || []} // Ensure value is an array of values (not objects)
                            onSelectionChange={(selected) => {
                              const updatedGroups = {
                                ...assignedGroups,
                                [index]: selected
                                  .slice(0, 1)
                                  .map((m) => m.value), // Ensure you're using values, not objects
                              };
                              setAssignedGroups(updatedGroups);
                            }}
                            addLabel="Add Group"
                            maxCount={1}
                          />
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <DialogFooter className="flex justify-end gap-2 px-1">
            {isPreview ? (
              <DialogClose asChild>
                <Button size="sm" variant="primary">
                  {t("Close")}
                </Button>
              </DialogClose>
            ) : (
              <>
                <DialogClose asChild>
                  <Button size="sm" variant="secondary">
                    {t("Cancel")}
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button size="sm" variant="primary" onClick={handleSubmit}>
                    {t("Apply")}
                  </Button>
                </DialogClose>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ApplyUpdatesModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  isPreview: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
};

export default ApplyUpdatesModal;
