import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import {
  useGetAllotmentOfDaysQuery,
  useGetMinisterGroupsQuery,
  useUpdateAllotmentOfDaysMutation,
} from "@/services/allotment-of-days";
import { useGetMinisterListQuery } from "@/services/master-data-management/minister-list";
import { Button, toast, Calendar as COSCalendar } from "@kla-v2/ui-components";
import {
  ArrowLeft,
  ArrowRightIcon,
  ArrowRight,
  Calendar,
  Loader2,
  Save,
  Users,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import MinisterGroupCard from "./components/minister-group-card";
import MinisterGroupsPreviewModal from "./components/minister-groups-preview-modal";
import PreviewSubmitPopup from "./components/preview-submit-popup";
import { DocumentMetadata } from "@/components/document-metadata";
import { File } from "lucide-react";
import CalendarOfSittingsModal from "./components/calendar-of-sittings-modal";
import { jsxData } from "./constants";
import { useSubmitDocumentMutation } from "@/services/documents";
import { AttachToFile } from "@/icons";
import ApplyUpdatesModal from "./components/apply-updates-modal";

export default function AllotmentOfDaysPage() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { documentId } = useParams();

  useAutoBreadcrumb();

  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isApplyUpdatesModalOpen, setIsApplyUpdatesModalOpen] = useState(false);
  const showApplyButton = true;
  const [allotmentData, setAllotmentData] = useState({
    allotments: [],
    metadata: {
      kla: "",
      session: "",
      documentType: "Allotment of Days",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });

  const [showCalendarOfSittingsModal, setShowCalendarOfSittingsModal] =
    useState(false);
  const [showMinisterGroupModal, setShowMinisterGroupModal] = useState(false);
  const [isSubmissionCompleted, setIsSubmissionCompleted] = useState(false);

  // RTK Query hooks
  const {
    data: allotmentResponse,
    isLoading: allotmentLoading,
    error: allotmentError,
  } = useGetAllotmentOfDaysQuery(documentId);

  const ministerDesignationGroupId = useMemo(() => {
    return allotmentResponse?.referredMinisterDesignationGroupId || null;
  }, [allotmentResponse]);

  const { data: ministerGroupsResponse } = useGetMinisterGroupsQuery(
    ministerDesignationGroupId,
    {
      skip: !ministerDesignationGroupId,
    },
  );

  const { data: ministerListData } = useGetMinisterListQuery();

  const ministerMap = useMemo(() => {
    return ministerListData
      ? ministerListData.reduce((acc, minister) => {
          acc[minister.id] = minister.user.displayName;
          return acc;
        }, {})
      : {};
  }, [ministerListData]);

  const groupEntriesMap = useMemo(() => {
    return ministerGroupsResponse
      ? ministerGroupsResponse.groups.reduce((acc, group) => {
          acc[group.id] = group.groupEntries || [];
          return acc;
        }, {})
      : {};
  }, [ministerGroupsResponse]);

  const [updateAllotmentOfDays, { isLoading: isUpdating }] =
    useUpdateAllotmentOfDaysMutation();

  const [submitAllotmentOfDays, { isLoading: isSubmitting }] =
    useSubmitDocumentMutation();

  // Map API data to component state
  useEffect(() => {
    if (allotmentResponse) {
      const updatedAllotments = allotmentResponse.allotments.map(
        (allotment) => ({
          ...allotment,
          ministers: (groupEntriesMap[allotment.groupId] || []).map(
            (minister) => ({
              ...minister,
              displayName: ministerMap[minister.ministerId] || "Unknown",
            }),
          ),
        }),
      );
      const updatedMetadata = {
        kla: allotmentResponse.assembly?.toString() || "15",
        session: allotmentResponse.session?.toString() || "",
        documentType: "Allotment of Days",
        currentNo: allotmentResponse.currentNumber || "",
        createdOn: allotmentResponse.createdAt
          ? new Date(allotmentResponse.createdAt).toLocaleDateString("en-GB")
          : "",
        createdBy: allotmentResponse.createdBy || "",
        name: allotmentResponse.name || "Allotment of Days",
      };

      setAllotmentData((prevState) => ({
        ...prevState,
        allotments: updatedAllotments,
        metadata: updatedMetadata,
      }));
    }
  }, [allotmentResponse, groupEntriesMap, ministerMap]);

  const handleSave = async () => {
    try {
      const formattedData = {
        allotments: allotmentData.allotments.map(
          ({ id, groupId, groupName, dates }) => ({
            id,
            groupId,
            groupName,
            dates,
          }),
        ),
      };

      await updateAllotmentOfDays({
        documentId,
        data: formattedData,
      }).unwrap();

      toast.success("Allotment of days saved successfully");
    } catch (error) {
      console.error("Error saving allotment data:", error);
      toast.error("Failed to save allotment of days");
    }
  };

  const handleSubmit = async () => {
    // Check if there are dates for at least one group
    const hasAnyDates = allotmentData.allotments.some(
      (allotment) => allotment.dates && allotment.dates.length > 0,
    );

    if (!hasAnyDates) {
      toast.error("Please allocate at least one date before submitting");
    }
    try {
      const response = await submitAllotmentOfDays({ documentId }).unwrap();
      toast.success("Success", {
        description: response?.message || "Submission successful!",
      });
      setIsPreviewOpen(true);
      setIsSubmissionCompleted(true);
    } catch (error) {
      console.error("Error submitting allotment data:", error);
      toast.error("Failed to submit allotment of days");
    }
  };

  const goBack = () => {
    navigate(-1);
  };

  if (allotmentLoading && !allotmentData.metadata.name) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-medium">Loading allotment data...</p>
        </div>
      </div>
    );
  }

  if (allotmentError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-6 bg-destructive/10 rounded-lg max-w-md text-center">
          <h2 className="text-xl font-bold text-destructive mb-2">
            Error Loading Data
          </h2>
          <p className="mb-4">
            Unable to load allotment of days data. Please try again.
          </p>
          <Button onClick={goBack} variant="neutral">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-background min-h-screen">
      <div className="px-5 py-3">
        <div className="p-5 rounded-md">
          <div className="mb-4 flex justify-between">
            <h1 className="typography-page-heading">
              {allotmentData.metadata.name}
            </h1>
            <Button
              variant="neutral"
              icon={File}
              iconPosition="right"
              className=""
              type="button"
              size="sm"
            >
              Preview
            </Button>
          </div>
          <DocumentMetadata documentMetadata={allotmentData.metadata} />
          <div className="bg-background-container py-6 px-12">
            <div className="flex justify-between mb-4 border-b pt-2 pb-4">
              <div className="flex items-center px-2 pb-2 gap-2">
                <h2 className="text-gray-500 typography-sub-title-heading">
                  {t("allotmentOfDays")}
                </h2>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  icon={Calendar}
                  iconPosition="right"
                  className="gap-1 text-info text-sm h-7 w-auto px-3 bg-primary-tint-90 border-none rounded-md hover:bg-primary-tint-90 hover:text-primary-tint-10"
                  onClick={() => setShowCalendarOfSittingsModal(true)}
                >
                  <span className="typography-body-text-s-14">
                    {t("approvedCOS")}
                  </span>
                </Button>
                <Button
                  variant="ghost"
                  icon={Users}
                  iconPosition="right"
                  className="gap-1 text-info text-sm h-7 w-auto px-3 bg-primary-tint-90 border-none rounded-md hover:bg-primary-tint-90 hover:text-primary-tint-10"
                  onClick={() => setShowMinisterGroupModal(true)}
                >
                  <span className="typography-body-text-s-14">
                    {t("approvedMinisterGroup")}
                  </span>
                </Button>
              </div>
            </div>
            {showApplyButton && (
              <div className="flex justify-end mb-4 pt-2 pb-4">
                <Button
                  type="button"
                  icon={ArrowRightIcon}
                  iconPosition="right"
                  variant="secondary"
                  className="flex items-center"
                  onClick={() => setIsApplyUpdatesModalOpen(true)}
                >
                  Apply Update
                </Button>
              </div>
            )}
            <div className="space-y-4">
              {allotmentData.allotments.map((group) => (
                <MinisterGroupCard
                  key={group.groupId}
                  group={{
                    id: group.groupId,
                    name: group.groupName,
                    ministers: group.ministers || [],
                    dates: group.dates || [],
                  }}
                />
              ))}
            </div>
          </div>
          <div className="flex justify-end mt-6">
            <div className="flex gap-2">
              <Button
                variant="neutral"
                className="flex items-center gap-3 text-black border border-gray-200 rounded-md px-6 py-6 text-xl"
                onClick={goBack}
              >
                <ArrowLeft size={24} />
                <span>{t("back")}</span>
              </Button>
              <Button
                variant="secondary"
                className="flex items-center gap-3 text-pink-500 border border-pink-500 rounded-md px-6 py-6 text-xl"
                onClick={handleSave}
                disabled={isUpdating || isSubmitting}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("saving")}
                  </>
                ) : (
                  <>
                    <Save size={24} className="text-pink-500" />
                    <span>{t("save")}</span>
                  </>
                )}
              </Button>
              {!isSubmissionCompleted ? (
                <Button
                  variant="primary"
                  className="flex items-center gap-3 bg-pink-500 text-white rounded-md px-6 py-6 text-xl"
                  onClick={handleSubmit}
                  disabled={isUpdating || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("submitting")}
                    </>
                  ) : (
                    <>
                      <span>{t("submit")}</span>
                      <ArrowRight size={24} />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  className="flex items-center gap-3 bg-pink-500 text-white rounded-md px-6 py-6 text-xl"
                  variant="primary"
                  iconPosition="right"
                  icon={AttachToFile}
                >
                  {t("attachToFile")}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Calendar of Sittings Modal */}
      <CalendarOfSittingsModal
        isOpen={showCalendarOfSittingsModal}
        onClose={() => setShowCalendarOfSittingsModal(false)}
        documentData={allotmentData.metadata}
      >
        <COSCalendar data={jsxData} isHorizontalView={true} />
      </CalendarOfSittingsModal>

      {/*Minister group preview modal*/}
      <MinisterGroupsPreviewModal
        isOpen={showMinisterGroupModal}
        onClose={() => setShowMinisterGroupModal(false)}
        documentData={allotmentData.metadata}
      >
        <div className="space-y-2">
          {allotmentData.allotments.map((group) => (
            <MinisterGroupCard
              key={group.groupId}
              group={{
                id: group.groupId,
                name: group.groupName,
                ministers: group.ministers || [],
              }}
            />
          ))}
        </div>
      </MinisterGroupsPreviewModal>

      <PreviewSubmitPopup
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        documentId={documentId}
        documentdata={allotmentData.metadata}
      >
        <div className="space-y-2">
          {allotmentData.allotments.map((group) => (
            <MinisterGroupCard
              key={group.groupId}
              group={{
                id: group.groupId,
                name: group.groupName,
                ministers: group.ministers || [],
                dates: group.dates || [],
              }}
            />
          ))}
        </div>
      </PreviewSubmitPopup>

      {/* Apply Updates Modal */}
      <ApplyUpdatesModal
        isOpen={isApplyUpdatesModalOpen}
        onClose={() => setIsApplyUpdatesModalOpen(false)}
      />
    </div>
  );
}
