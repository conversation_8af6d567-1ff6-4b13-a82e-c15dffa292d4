import { DocumentMetadata } from "@/components/document-metadata";
import PreviewSubmitPopup from "@/components/preview-submit-modal/preview-submit-modal";
import { useLanguage } from "@/hooks";
import {
  useGetDelayStatementListByIdQuery,
  useRefreshLABMutation,
  useUpdateDelayStatementDraftMutation,
} from "@/services/delay-statement";
import {
  Button,
  // DataTable,
  DatePicker,
  ExpandableAccordion,
  ExpandableContent,
  ExpandableHeader,
  ExpandableItem,
  IconButton,
  Input,
} from "@kla-v2/ui-components";
import {
  ArrowLeft,
  Plus,
  Save,
  ArrowRight,
  Info,
  File,
  RotateCcw,
} from "lucide-react";

import { useState } from "react";
import RemovefromList from "./components/remove-from-list";
import AddDelayStatement from "./components/add-delay-statement";
import { format } from "date-fns";
import DraggableTable from "./components/sortable-row-wrapper";
import { useEffect } from "react";

function DelayStatementList() {
  const [kla, setKla] = useState("");
  const [session, setSession] = useState("");
  const [listNumber, setListNumber] = useState("");
  const [dateToBeLaid, setDateToBeLaid] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isRemove, setIsRemove] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  // const { documentId } = useParams();
  const documentId = "d1fdd7a6-9e83-4c14-9c03-8f45ec58d8ad";
  const { t } = useLanguage();
  const documentMeta = [
    {
      metadata: {
        assembly: "13",
        session: "15",
        documentType: "Delay Statement",
        currentNo: "fd",
        createdOn: "3948",
        createdBy: "",
        name: "Delay Statement",
      },
    },
  ];
  const [accordionValue, setAccordionValue] = useState([]);
  const [updateDraft, { isLoading, isSuccess, error }] =
    useUpdateDelayStatementDraftMutation();
  const { data: delayList } = useGetDelayStatementListByIdQuery(documentId);
  const [refreshLAB] = useRefreshLABMutation();
  const rulenumber = "47(2)";
  useEffect(() => {
    if (delayList) {
      setKla(delayList.kla || "");
      setSession(delayList.session || "");
      setDateToBeLaid(
        delayList.dateToBeLaid ? new Date(delayList.dateToBeLaid) : null,
      );
      setListNumber(delayList.listNumber || "");
    }
  }, [delayList]);

  const ministerWisePrefix = (minister) => {
    return `1. ${minister.ministerDisplayNameInLocal}, ${minister.designationInLocal}`;
  };
  const onSubmit = async () => {
    try {
      setIsPreviewOpen(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };

  const handleSave = async () => {
    const formattedDate = dateToBeLaid
      ? new Date(dateToBeLaid).toISOString()
      : null;
    try {
      await updateDraft({
        type: "DELAY_STATEMENT_LIST",
        listNumber,
        dateToBeLaidInAssembly: formattedDate,
      }).unwrap();
    } catch (err) {
      console.error("Failed to save:", err);
    }
  };

  const handleRefresh = async () => {
    try {
      await refreshLAB().unwrap();
    } catch (err) {
      console.error("Refresh failed:", err);
    }
  };

  const handleAdd = async () => {
    try {
      setIsAdd(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };

  return (
    <div className="px-5 py-3 bg-background flex flex-col min-h-screen">
      <div className="flex justify-between p-2">
        <h1 className="typography-page-heading">
          Delay Statement List for {format(dateToBeLaid, "dd.MM.yyyy")}
        </h1>
        <div className="flex justify-between gap-2">
          <IconButton
            icon={RotateCcw}
            variant="secondary"
            size="sm"
            className="text-grey-500 border-grey-300"
            onClick={handleRefresh}
          />
          <Button
            variant="neutral"
            icon={Info}
            iconPosition="right"
            className=""
            type="button"
            size="sm"
          >
            {t("rule")} {rulenumber}
          </Button>
          <Button
            variant="neutral"
            icon={File}
            iconPosition="right"
            className=""
            type="button"
            size="sm"
            onClick={() => onSubmit()}
          >
            {t("preview")}
          </Button>
        </div>
      </div>
      <DocumentMetadata documentMetadata={documentMeta[0].metadata} />
      <div className="flex justify-between gap-1 w-full">
        <ExpandableAccordion
          type="multiple"
          value={accordionValue}
          onValueChange={setAccordionValue}
          className="w-full flex flex-col gap-4 flex-grow"
        >
          <ExpandableItem value="item-1">
            <ExpandableHeader index={1} label={t("basicDetails")} />
            <ExpandableContent>
              <div className="w-full flex flex-col pt-6 pr-12 pb-6 pl-12">
                <div className="flex justify-between">
                  <div className="w-[23%] [&_label]:text-sm [&_input]:placeholder:text-sm [&_input]:placeholder:items-center">
                    <Input
                      label={t("kla")}
                      placeholder="Enter KLA"
                      value={kla}
                      maxLength={2}
                      onChange={(e) => setKla(e.target.value)}
                    />
                  </div>
                  <div className="w-[23%] [&_label]:text-sm [&_input]:placeholder:text-sm [&_input]:placeholder:items-center">
                    <Input
                      label={t("session")}
                      placeholder="Enter session"
                      value={String(session)}
                      maxLength={2}
                      onChange={(e) => setSession(e.target.value)}
                    />
                  </div>
                  <div className="w-[23%] flex items-center [&_label]:text-sm [&_input]:placeholder:text-sm [&_input]:placeholder:items-center">
                    <DatePicker
                      label={t("form:dateToBeLaidInAssembly")}
                      mode="single"
                      value={dateToBeLaid}
                      onChange={setDateToBeLaid}
                    />
                  </div>

                  <div className="w-[23%] flex items-center [&_label]:text-sm [&_input]:placeholder:text-sm [&_input]:placeholder:items-center">
                    <Input
                      label={t("form:listNumber")}
                      placeholder="Enter List Number"
                      value={listNumber}
                      onChange={(e) => setListNumber(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex justify-end">
                  <Button
                    iconPosition="right"
                    onClick={handleSave}
                    variant="secondary"
                    disabled={isLoading}
                  >
                    {isLoading ? t("saving...") : t("save")}
                    <Save size={24} />
                  </Button>
                </div>
                {isSuccess && (
                  <p className="text-green-500 pt-4">Saved successfully!</p>
                )}
                {error && <p className="text-red-500 pt-4">Failed to save.</p>}
              </div>
            </ExpandableContent>
          </ExpandableItem>
          <ExpandableItem value="item-2">
            <ExpandableHeader index={2} label={t("delayStatementLaying")} />
            <ExpandableContent>
              {isLoading ? (
                <div>Loading...</div>
              ) : (
                delayList?.ministerWiseLayingDocuments.map((minister) => (
                  <div
                    key={minister.id}
                    className="w-full flex flex-col pt-6 pr-12 pb-6 pl-12"
                  >
                    {/* Header */}
                    <div className="flex justify-between ">
                      <h1 className="typography-body-text-s-16">
                        {`${ministerWisePrefix(
                          minister,
                        )} മേശപ്പുറത്ത് വയ്ക്കുന്നു.`}
                      </h1>
                      <IconButton
                        icon={Plus}
                        size="sm"
                        variant="secondary"
                        className="text-grey-500 border-grey-300 w-8 h-8"
                        onClick={() => handleAdd(minister.id)}
                      />
                    </div>
                    <div className="p-4 ">
                      <DraggableTable
                        data={minister}
                        docId={documentId}
                        tableData={delayList}
                      />
                    </div>
                  </div>
                ))
              )}
            </ExpandableContent>
          </ExpandableItem>
        </ExpandableAccordion>
      </div>
      <div className="flex justify-end mt-6">
        <div className="flex gap-2">
          <Button variant="neutral" size="lg">
            <ArrowLeft size={24} />
            <span>{t("back")}</span>
          </Button>
          <Button variant="secondary" size="lg">
            <Save size={24} />
            <span>{t("save")}</span>
          </Button>
          <Button variant="primary" size="lg">
            <span>{t("submit")}</span>
            <ArrowRight size={24} />
          </Button>
        </div>
      </div>
      <PreviewSubmitPopup
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        metaData={documentMeta[0].metadata}
      />
      <RemovefromList isOpen={isRemove} onClose={() => setIsRemove(false)} />
      <AddDelayStatement isOpen={isAdd} onClose={() => setIsAdd(false)} />
    </div>
  );
}

export default DelayStatementList;
