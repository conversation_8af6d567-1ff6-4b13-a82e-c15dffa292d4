import { useEffect, useState, createContext } from "react";
import { useParams } from "react-router-dom";
import { useLanguage } from "@/hooks";
import { DocumentMetadata } from "@/components/document-metadata";
import { useContextualNavigation } from "@/utils/navigation";
import { buildRoute, ROUTE_CONFIG } from "@/config/routes";
import {
  ExpandableAccordion,
  ExpandableItem,
  toast,
} from "@kla-v2/ui-components";
import { BasicDetailsPage } from "./components/basic-details/basic-details-section";
import { NoticeDetailsPage } from "./components/notice-page/notice-details-section";
import { Button } from "@kla-v2/ui-components";
import { ArrowLeft, Save } from "lucide-react";
import ArrowRight from "@/icons/arrow-right-icon";
import {
  useSubmitNoticeMutation,
  useGetNoticeByIdQuery,
  useCreateBasicDetailsMutation,
  useCreateNoticeDetailsMutation,
} from "@/services/question";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

const NoticeContext = createContext();

function QuestionCreatePage() {
  const { t } = useLanguage();
  const documentName = t("Create Notice for Question");
  const { documentId } = useParams();
  const { navigate } = useContextualNavigation();
  const [accordionValue, setAccordionValue] = useState([]);
  const [isBasicDetailsComplete, setIsBasicDetailsComplete] = useState(false);
  const [isNoticeDetailsComplete, setIsNoticeDetailsComplete] = useState(false);
  const [submitNotice] = useSubmitNoticeMutation();
  const [createQuestionNotice] = useCreateBasicDetailsMutation();
  const [createNoticeDetails] = useCreateNoticeDetailsMutation();
  const {
    data: documentData,
    isLoading,
    error,
  } = useGetNoticeByIdQuery({ documentId });

  const [basicDetails, setBasicDetails] = useState({
    type: "NOTICE_FOR_QUESTION",
    assembly: "",
    session: "",
    questionDate: "",
    ministerDesignationId: "",
    ministerId: "456",
    portfolioId: "",
    subSubjectId: "",
    source: "PPO",
    primaryMember: {},
    secondaryMembers: [],
  });
  const [noticeDetails, setNoticeDetails] = useState({
    id: documentId,
    noticePriority: "NIL",
    starred: false,
    noticeHeading: "",
    clauses: [],
  });

  const contextValue = {
    id: documentId,
    basicDetails: {
      ...basicDetails,
    },
    noticeDetails: {
      ...noticeDetails,
    },
    isBasicDetailsComplete,
    setIsBasicDetailsComplete,
    isNoticeDetailsComplete,
    setIsNoticeDetailsComplete,
    setBasicDetails,
    setNoticeDetails,
    setAccordionValue,
  };

  useAutoBreadcrumb();

  useEffect(() => {
    if (documentData) {
      // Check document status and redirect if not DRAFT
      if (documentData.status !== "DRAFT") {
        navigate(buildRoute.questionNotice(documentId, 'view'));
        return;
      }

      setBasicDetails((prev) => ({
        ...prev,
        type: documentData.type || "",
        assembly: documentData.assembly || "",
        session: documentData.session || "",
      }));
    }
  }, [documentData, documentId, navigate]);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading document: {error.message}</div>;

  const formatCreatedOn = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    const options = { year: "numeric", month: "numeric", day: "numeric" };
    return date.toLocaleDateString(undefined, options);
  };

  const amendedMetadata = {
    type: documentData.type || "N/A",
    typeName: "SRO",
    createdOn: formatCreatedOn(documentData.createdAt),
    createdBy: documentData.createdBy || "",
    isDocTypeAdded: true,
    title: "SRO",
    documentType: "Notice For Question",
    assembly: documentData.assembly || 0,
    session: documentData.session || "N/A",
  };

  const goBack = () => {
    navigate(ROUTE_CONFIG.MEMBER.MY_QUESTION_NOTICES);
  };

  const handleSaveToNoticeBank = async () => {
    try {
      if (!isBasicDetailsComplete) {
        await createQuestionNotice({
          id: documentId,
          data: contextValue.basicDetails,
        }).unwrap();
      }

      if (!isNoticeDetailsComplete) {
        await createNoticeDetails({
          id: documentId,
          data: contextValue.noticeDetails,
        }).unwrap();
      }

      if (isBasicDetailsComplete || isNoticeDetailsComplete) {
        toast.success("Success", {
          description: "Notice saved to Notice Bank successfully!",
        });

        navigate(ROUTE_CONFIG.MEMBER.MY_QUESTION_NOTICES_NOTICE_BANK);
      }
    } catch (error) {
      toast.error("Error", {
        description: error?.data?.message || "Failed to save notice.",
      });
    }
  };

  const handleSubmit = async () => {
    try {
      await submitNotice({ documentId }).unwrap();
      toast.success("Success", {
        description: "Notice submitted successfully!",
      });
    } catch (error) {
      toast.error("Error", {
        description: error?.data?.message || "Failed to submit notice.",
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen px-5 py-3 bg-background">
      <div className="flex justify-between">
        <h1 className="typography-page-heading">{documentName}</h1>
      </div>
      <div className="w-full py-5">
        <DocumentMetadata documentMetadata={amendedMetadata} />
      </div>
      <NoticeContext.Provider value={contextValue}>
        <ExpandableAccordion
          type="multiple"
          value={accordionValue}
          onValueChange={setAccordionValue}
          className="flex flex-col flex-grow w-full gap-4"
        >
          <ExpandableItem value={`item-1`}>
            <BasicDetailsPage accordionOrderNo={1} />
          </ExpandableItem>
          <ExpandableItem value={`item-2`}>
            <NoticeDetailsPage accordionOrderNo={2} />
          </ExpandableItem>
        </ExpandableAccordion>
      </NoticeContext.Provider>

      <div className="flex items-center justify-end gap-4 py-4 bg-background">
        <Button
          variant="neutral"
          className="flex items-center gap-3 text-black border border-gray-200 rounded-md"
          onClick={goBack}
          icon={ArrowLeft}
          iconPosition="left"
        >
          <span>{t("back")}</span>
        </Button>
        <Button
          variant="secondary"
          className="flex items-center"
          onClick={handleSaveToNoticeBank}
        >
          Save To Notice Bank <Save className="w-4 h-4" />
        </Button>

        <Button
          variant="primary"
          disabled={
            !contextValue.isBasicDetailsComplete ||
            !contextValue.isNoticeDetailsComplete
          }
          onClick={handleSubmit}
        >
          Submit <ArrowRight />
        </Button>
      </div>
    </div>
  );
}

export { QuestionCreatePage, NoticeContext };
