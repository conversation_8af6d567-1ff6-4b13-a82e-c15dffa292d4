import { useState, useContext } from "react";
import {
  Form,
  FormField,
  FormItem,
} from "../../../../../../components/ui/form";
import { Button, Textarea, Badge, toast } from "@kla-v2/ui-components";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { NoticeFormSchema } from "@/components/ui/form/form-schema";
import { LanguageContext } from "../../../../../../components/languages/language-context";
import { Save } from "lucide-react";
import { StarredIcon, UnStarredIcon } from "@/icons/star-icon";
import CornerIcon from "@/icons/corner-icon";
import ClauseCardsList from "../clause-cards-list";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import AddClauseIcon from "@/icons/add-clause-icon";
import CompareIcon from "@/icons/compare-icon";
import { useCreateNoticeDetailsMutation } from "@/services/question";
import { NoticeContext } from "../../index";
const priorities = ["P1", "P2", "P3", "NIL"];

const NoticePageContent = () => {
  const [selected, setSelected] = useState("");
  const [isStarred, setIsStarred] = useState(false);
  const [noticeText, setNoticeText] = useState("");
  const [clauses, setClauses] = useState([{ id: "A", text: "" }]);
  const [usedIds, setUsedIds] = useState(new Set(["A"]));
  const [createNoticeDetails] = useCreateNoticeDetailsMutation();
  const contextValue = useContext(NoticeContext);
  const id = contextValue.id;

  const generateUniqueId = () => {
    let newId;
    let charCode = 65;
    while (true) {
      newId = String.fromCharCode(charCode);
      if (!usedIds.has(newId)) {
        break;
      }
      charCode++;
      if (charCode > 90) {
        return null;
      }
    }
    return newId;
  };
  const { t } = useContext(LanguageContext);
  const formSchema = NoticeFormSchema(t);

  const form = useForm({
    mode: "onSubmit",
    resolver: zodResolver(formSchema),
    defaultValues: contextValue.noticeDetails,
  });

  const { handleSubmit, control } = form;
  const onSubmit = async (data) => {
    const updatedNoticeDetails = {
      ...contextValue.noticeDetails,
      noticePriority: data.noticePriority || "",
      starred: isStarred,
      noticeHeading: data.noticeHeading.trim(),
      clauses: clauses.map((clause) => ({
        content: clause.text || "",
      })),
    };

    contextValue.setNoticeDetails(updatedNoticeDetails);

    try {
      const response = await createNoticeDetails({
        id,
        data: updatedNoticeDetails,
      }).unwrap();
      if (response) {
        toast.success("Success", {
          description: response?.message || "Details added successfully",
        });
        contextValue.setIsNoticeDetailsComplete(true);
        contextValue.setAccordionValue((prev) =>
          prev.filter((item) => item !== "item-2"),
        );
      }
    } catch (error) {
      toast.error("Error", {
        description: error?.data?.message || "Failed to submit data.",
      });
    }
  };
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = clauses.findIndex((clause) => clause.id === active.id);
      const newIndex = clauses.findIndex((clause) => clause.id === over.id);

      const reorderedClauses = arrayMove(clauses, oldIndex, newIndex).map(
        (clause, i) => ({
          ...clause,
          order: i,
        }),
      );

      setClauses(reorderedClauses);
    }
  };

  const getBadgeStyle = (priority) => {
    if (selected === priority) {
      switch (priority) {
        case "P1":
          return "border border-blue-200 bg-blue-100 text-blue-500";
        case "P2":
          return "border border-green-200 bg-green-100 text-green-500";
        case "P3":
          return "border border-red-200 bg-red-100 text-red-500";
        case "NIL":
          return "border border-gray-300 bg-gray-100 text-gray-700";
        default:
          return "border border-gray-300 text-gray-700";
      }
    }
    return "border border-gray-300 text-gray-700";
  };

  const handleNoticeTextChange = (e) => {
    const text = e.target.value;
    const words = text.trim().split(/\s+/);

    if (words.length <= 15) {
      setNoticeText(text);
    } else {
      const truncatedText = words.slice(0, 15).join(" ");
      setNoticeText(truncatedText);
      e.target.value = truncatedText;
    }
  };

  const handleClauseTextChange = (index, text) => {
    if (text.length <= 500) {
      const updatedClauses = clauses.map((clause, i) =>
        i === index ? { ...clause, text } : clause,
      );
      setClauses(updatedClauses);
    }
  };
  const handleAddClause = () => {
    const newId = generateUniqueId();
    if (newId) {
      setClauses([...clauses, { id: newId, text: "" }]);
      setUsedIds(new Set(usedIds).add(newId));
    }
  };

  const handleDeleteClause = (clauseId) => {
    const updatedClauses = clauses.filter((clause) => clause.id !== clauseId);
    setClauses(updatedClauses);
  };

  return (
    <div className="flex flex-col gap-4 p-6">
      <Form className="w-full">
        <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-6">
          <div className="flex justify-between items-center">
            <div className="flex flex-col items-start gap-2">
              <FormField
                control={control}
                name="noticePriority"
                render={({ field }) => (
                  <FormItem>
                    <label className="typography-body-text-m-14 text-gray-700">
                      Priority
                    </label>
                    <div className="flex gap-2">
                      {priorities.map((priority) => (
                        <Badge
                          key={priority}
                          onClick={() => {
                            setSelected(priority);
                            field.onChange(priority);
                          }}
                          className={`px-4 py-2 w-32 rounded-full cursor-pointer text-center ${getBadgeStyle(
                            priority,
                          )}`}
                        >
                          {priority}
                        </Badge>
                      ))}
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => setIsStarred(!isStarred)}
            >
              <FormField
                control={control}
                name="starred"
                render={({ field }) => (
                  <FormItem>
                    <div
                      className="flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setIsStarred(!isStarred);
                        field.onChange(!isStarred);
                      }}
                    >
                      <span className="typography-body-text-m-14 text-gray-700">
                        {isStarred ? "Starred" : "Unstarred"}
                      </span>
                      {isStarred ? <StarredIcon /> : <UnStarredIcon />}
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={control}
            name="noticeHeading"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <label className="typography-body-text-m-14 text-gray-700">
                  Notice Heading
                </label>
                <div className="relative mt-1">
                  <Textarea
                    placeholder="Enter Notice Heading"
                    className="border border-gray-300 rounded-lg p-3 text-black font-bold text-sm focus:outline-none focus:border-blue-500 resize-none w-full"
                    rows="4"
                    {...field}
                    value={noticeText}
                    onChange={(e) => {
                      handleNoticeTextChange(e);
                      field.onChange(e.target.value);
                    }}
                  />

                  <div className="absolute bottom-3 right-3 flex items-center gap-2">
                    <CornerIcon />
                    <div className="text-gray-500 text-sm">
                      {noticeText.trim().split(/\s+/).filter(Boolean).length}/15
                    </div>
                  </div>
                  <div className="absolute top-3 right-3 flex items-center">
                    <CompareIcon />
                  </div>
                </div>
              </FormItem>
            )}
          />

          <div className="flex items-center justify-between">
            <div className="ml-auto">
              <button
                type="button"
                className="flex items-center justify-center w-10 h-10 p-2 rounded-md border border-gray-300 bg-white"
                onClick={handleAddClause}
              >
                <AddClauseIcon />
              </button>
            </div>
          </div>

          <div className="flex flex-col ">
            <FormField
              control={control}
              name="clauses"
              render={({ field }) => (
                <FormItem>
                  <label className="text-base font-semibold text-black-600 leading-6 tracking-[-0.02em] font-inter">
                    Clause
                  </label>
                  <DndContext
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <ClauseCardsList
                      clauses={clauses}
                      onClauseTextChange={(index, word) => {
                        handleClauseTextChange(index, word);
                        field.onChange(clauses); // Update form state
                      }}
                      onDeleteClause={(clauseId) => {
                        handleDeleteClause(clauseId);
                        field.onChange(clauses); // Ensure form state updates
                      }}
                    />
                  </DndContext>
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end mt-2">
            <Button
              type="submit"
              variant="secondary"
              className="flex items-center"
            >
              Save <Save className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default NoticePageContent;
