import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, toast } from "@kla-v2/ui-components";
import { DocumentMetadata } from "@/components/document-metadata";
import AddCustomIcon from "@/icons/add-custom-icon";
import { useSearchParams, useNavigate } from "react-router-dom";
import { ROUTE_CONFIG } from "@/config/routes";
import {
  useGetBallotPerformListQuery,
  usePerformBallotingMutation,
  useGetRandomizedBallotResultQuery,
  useGetBallotByIdQuery,
} from "@/services/perform-balloting";
import MinisterCard from "./minister-card";
import { MoveRight, ArrowLeft } from "lucide-react";
import { useLanguage } from "@/hooks";
import CancelBallotModal from "./cancel-ballot-modal";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import BallotResultTable from "./ballot-result-table";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";
import { formatDate } from "@/utils";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import SpinnerLoader from "@/utils/loaders/spinner-loader";

const PerformBalloting = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const queryQuestionDate = searchParams.get("questionDate") || null;
  const ballotIdFromUrl = searchParams.get("ballotId") || null;
  const [selectedDate, setSelectedDate] = useState();
  const [showMembers, setShowMembers] = useState(!ballotIdFromUrl);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [ballotResult, setBallotResult] = useState(null);
  const [, setBallotId] = useState(ballotIdFromUrl);
  const [cancellationReason, setCancellationReason] = useState("");
  const [isBallotCancelled, setIsBallotCancelled] = useState(false);
  const [questionDate, setQuestionDate] = useState("");
  const [isMetadataLoading, setIsMetadataLoading] = useState(true);
  const [isViewingFromList, setIsViewingFromList] = useState(false);
  const [isNewlyPerformedBallot, setIsNewlyPerformedBallot] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);

  const navigate = useNavigate();
  const { t } = useLanguage();

  const activeAssembly = useSelector(selectActiveAssemblyItems);
  const assembly = activeAssembly?.assembly;
  const session = activeAssembly?.session;

  const { ballotingId, questionDate: pathQuestionDate } = useParams();

  useEffect(() => {
    if (ballotingId) {
      setIsViewingFromList(true);
      setBallotId(ballotingId);
      setShowMembers(false);
      setIsDataLoading(true);
    } else {
      setIsViewingFromList(false);
    }
  }, [ballotingId]);

  useEffect(() => {
    if (queryQuestionDate) {
      setQuestionDate(queryQuestionDate);
    }
    if (pathQuestionDate) {
      setQuestionDate(pathQuestionDate);
    }
  }, [queryQuestionDate, pathQuestionDate]);

  useEffect(() => {
    if (questionDate) {
      setSelectedDate(questionDate);
    }
  }, [questionDate]);

  const { data: ballotData } = useGetBallotPerformListQuery(
    { questionDate },
    { skip: !questionDate || !!ballotingId },
  );

  const [performBalloting, { isLoading: isPerforming }] =
    usePerformBallotingMutation();

  const { data: ballotById, isSuccess: ballotByIdSuccess } =
    useGetBallotByIdQuery(ballotingId, { skip: !ballotingId });
  const { data: ballotResults, isSuccess: ballotResultsSuccess } =
    useGetRandomizedBallotResultQuery(
      { params: selectedDate },
      { skip: !selectedDate || ballotingId },
    );

  const openCancelModal = () => setIsCancelModalOpen(true);
  const closeCancelModal = () => setIsCancelModalOpen(false);

  const updateParams = (newParams) => {
    const updatedParams = new URLSearchParams(searchParams);
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        updatedParams.set(key, value.toString());
      } else {
        updatedParams.delete(key);
      }
    });
    setSearchParams(updatedParams);
  };

  const handleDateChange = (date) => {
    const formattedDate = date.toISOString().split("T")[0];
    setSelectedDate(formattedDate);
    setCancellationReason("");
    setIsBallotCancelled(false);
    updateParams({ questionDate: formattedDate });

    setShowTable(false);
    setBallotResult(null);
    setBallotId(null);
    setIsViewingFromList(false);
    setIsNewlyPerformedBallot(false);
    setIsDataLoading(true);

    const updatedParams = new URLSearchParams(searchParams);
    updatedParams.delete("ballotId");
    updatedParams.set("questionDate", formattedDate);
    setSearchParams(updatedParams);
  };

  const goBack = () => {
    navigate(ROUTE_CONFIG.SECTION.BALLOT.LIST);
  };

  const handlePerformBalloting = async () => {
    try {
      setIsMetadataLoading(true);
      setIsDataLoading(true);
      const response = await performBalloting({
        data: {
          questionDate: selectedDate,
          assembly: assembly,
          session: session,
        },
      }).unwrap();

      setBallotResult(response);
      setShowTable(true);
      setIsMetadataLoading(false);
      setIsDataLoading(false);
      setIsNewlyPerformedBallot(true);
      setCancellationReason("");
      setIsBallotCancelled(false);

      if (response && response.id) {
        setBallotId(response.id);

        updateParams({
          questionDate: selectedDate,
          ballotId: response.id,
        });
      }
    } catch (error) {
      setIsMetadataLoading(false);
      setIsDataLoading(false);

      if (
        error?.data?.message?.includes("Ballot already exist") ||
        error?.status === 409 ||
        error?.data?.error === "BALLOT_ALREADY_EXISTS"
      ) {
        toast.error("Ballot already exists for this date.");
      } else {
        toast.error("Ballot already exist.");
      }
    }
  };

  const handleCancellationReason = (reason) => {
    setCancellationReason(reason);
    setIsBallotCancelled(true);
    closeCancelModal();
  };

  useEffect(() => {
    if (questionDate && ballotIdFromUrl) {
      setSelectedDate(questionDate);
      setBallotId(ballotIdFromUrl);
      setShowTable(true);
      setIsMetadataLoading(false);
      setIsDataLoading(true);
    } else if (questionDate) {
      setSelectedDate(questionDate);
      setShowTable(false);
      setIsMetadataLoading(true);
      setIsDataLoading(true);
    }
  }, [questionDate, ballotIdFromUrl]);

  useEffect(() => {
    if (ballotByIdSuccess && ballotById) {
      setBallotResult(ballotById);
      setIsMetadataLoading(false);
      setIsDataLoading(false);
      setShowTable(true);

      if (ballotById.questionDate) {
        setSelectedDate(ballotById.questionDate);
        setQuestionDate(ballotById.questionDate);
      }

      if (
        ballotById.ballotStatus === "CANCELLED" &&
        ballotById.reasonForCancellation
      ) {
        setCancellationReason(ballotById.reasonForCancellation);
        setIsBallotCancelled(true);
      } else {
        setCancellationReason("");
        setIsBallotCancelled(false);
      }
    }
  }, [ballotById, ballotByIdSuccess]);

  useEffect(() => {
    if (ballotResultsSuccess && ballotResults) {
      setBallotResult(ballotResults);
      setIsMetadataLoading(false);
      setIsDataLoading(false);

      if (
        ballotResults.ballotStatus === "CANCELLED" &&
        ballotResults.reasonForCancellation
      ) {
        setCancellationReason(ballotResults.reasonForCancellation);
        setIsBallotCancelled(true);
      } else {
        setCancellationReason("");
        setIsBallotCancelled(false);
      }
    }
  }, [ballotResults, ballotResultsSuccess]);

  useAutoBreadcrumb();

  const renderContent = () => {
    if (isViewingFromList && isDataLoading) {
      return (
        <div className="flex justify-center items-center h-full">
          <SpinnerLoader />
        </div>
      );
    }

    if (!selectedDate) {
      return (
        <p className="text-gray-500 font-medium mt-52">
          Please select Question Date
        </p>
      );
    }
    if (selectedDate && !showTable) {
      return (
        <Button
          data-testid="button-perform-balloting"
          onClick={handlePerformBalloting}
          icon={MoveRight}
          variant="primary"
          iconPosition="right"
          className="my-auto"
          disabled={isPerforming}
        >
          {isPerforming ? "Processing..." : "Perform Balloting"}
        </Button>
      );
    }
    if (selectedDate && showTable) {
      return (
        <div className="w-full h-full flex flex-col overflow-hidden">
          <BallotResultTable
            ballotEntries={ballotResult?.ballotEntries}
            t={t}
          />
        </div>
      );
    }
  };
  const shouldShowCancelButton = () => {
    return (
      ballotResult &&
      !isBallotCancelled &&
      ballotResult?.ballotStatus !== "CANCELLED" &&
      (isViewingFromList || isNewlyPerformedBallot || showTable) &&
      !isDataLoading
    );
  };

  const shouldShowBackButton = () => {
    return !isDataLoading;
  };

  return (
    <div className="flex flex-col h-[calc(100vh-112px)]">
      <div className="flex-none">
        <h2 className="typography-page-heading ml-5 mt-5">Perform Balloting</h2>
        <div className="mx-4 mt-2">
          <DocumentMetadata
            documentMetadata={{
              assembly: assembly,
              session: session,
              documentType: "Balloting",
              createdOn:
                !isMetadataLoading && ballotResult
                  ? formatDate(ballotResult.createdAt)
                  : "Waiting for ballot creation...",
              createdBy:
                !isMetadataLoading && ballotResult
                  ? ballotResult.createdBy || "System"
                  : "Waiting for ballot creation...",
            }}
          />
        </div>
      </div>
      <div className="flex-1 overflow-hidden px-4 mt-3">
        <div className="grid grid-cols-3 gap-4 h-full">
          {showMembers && (
            <div className="col-span-1 p-4 rounded-lg bg-white shadow w-full flex flex-col h-full overflow-hidden">
              <DatePicker
                label="Question Date"
                mode="single"
                className="ml-1"
                value={selectedDate}
                onChange={(selected) => {
                  if (selected !== questionDate) {
                    handleDateChange(new Date(selected));
                  }
                }}
              />
              {selectedDate ? (
                <>
                  <h3 className="typography-page-heading font-inter text-base font-semibold ml-3">
                    Notice Submitted Members ({ballotData?.length || 0})
                  </h3>
                  <div className="flex-grow overflow-y-auto [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden mt-2">
                    {ballotData?.length > 0 ? (
                      ballotData.map((member) => (
                        <MinisterCard
                          key={member.id}
                          id={member.id}
                          memberDisplayName={member.memberDisplayName}
                          constituencyName={member.constituencyName}
                          politicalPartyName={member.politicalPartyName}
                        />
                      ))
                    ) : (
                      <p className="text-gray-500 text-sm mt-2 ml-3">
                        No members available.
                      </p>
                    )}
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center my-auto">
                  <AddCustomIcon />
                  <p className="text-gray-500 text-sm">
                    Please select Question Date
                  </p>
                </div>
              )}
            </div>
          )}
          <div
            className={`${
              showMembers ? "col-span-2" : "col-span-3"
            } bg-white flex flex-col items-center rounded-lg shadow h-full overflow-hidden`}
          >
            {cancellationReason &&
              showTable &&
              isBallotCancelled &&
              ballotResult &&
              ballotResult.ballotStatus === "CANCELLED" && (
                <div className="w-full p-5">
                  <h3 className="text-gray-600 typography-body-text-r-17">
                    Reason for Cancel Balloting
                  </h3>
                  <p className="text-error typography-body-text-m-18 mt-2">
                    {cancellationReason}
                  </p>
                  <hr className="border border-border-1 opacity-31 mt-3" />
                </div>
              )}
            {renderContent()}
          </div>
        </div>
      </div>
      {selectedDate && (
        <div className="flex-none py-2 px-4">
          <div className="flex justify-end gap-4">
            {shouldShowBackButton() && (
              <Button
                variant="neutral"
                className="flex items-center gap-3 text-black border border-gray-200 rounded-md mr-2"
                onClick={goBack}
                icon={ArrowLeft}
                iconPosition="left"
              >
                <span>{t("back")}</span>
              </Button>
            )}
            {showMembers ? (
              <Button
                variant="primary"
                type="submit"
                icon={MoveRight}
                iconPosition="right"
                disabled={!showTable}
                onClick={() => setShowMembers(false)}
              >
                {t("close")}
              </Button>
            ) : (
              shouldShowCancelButton() && (
                <Button
                  variant="secondary"
                  icon={ArrowLeft}
                  iconPosition="left"
                  onClick={openCancelModal}
                  data-testid="button-cancel"
                >
                  {t("cancelBalloting")}
                </Button>
              )
            )}
            <CancelBallotModal
              isOpen={isCancelModalOpen}
              onClose={closeCancelModal}
              ballotId={ballotResult?.id}
              onCancelConfirm={handleCancellationReason}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformBalloting;
