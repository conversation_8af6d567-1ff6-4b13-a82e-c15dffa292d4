import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import BallotResultTable from "./ballot-result-table";

const mockTranslate = (key) => key;

const mockBallotEntries = [
  {
    id: "1",
    ballotOrder: 1,
    memberDisplayName: "John Doe",
    constituencyName: "District 1",
    politicalPartyName: "Party A",
  },
  {
    id: "2",
    ballotOrder: 2,
    memberDisplayName: "Jane Smith",
    constituencyName: "District 2",
    politicalPartyName: "Party B",
  },
];

describe("BallotResultTable", () => {
  it("renders the table with ballot entries", () => {
    render(
      <BallotResultTable ballotEntries={mockBallotEntries} t={mockTranslate} />,
    );

    expect(screen.getByText("Balloting Result:")).toBeInTheDocument();
    expect(screen.getByText("Ballot Order")).toBeInTheDocument();
    expect(screen.getByText("Members")).toBeInTheDocument();
    expect(screen.getByText("Constituency")).toBeInTheDocument();
    expect(screen.getByText("Political Party")).toBeInTheDocument();

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("District 1")).toBeInTheDocument();
    expect(screen.getByText("Party A")).toBeInTheDocument();

    expect(screen.getByText("Jane Smith")).toBeInTheDocument();
    expect(screen.getByText("District 2")).toBeInTheDocument();
    expect(screen.getByText("Party B")).toBeInTheDocument();
  });

  it("renders an empty table when no data is provided", () => {
    render(<BallotResultTable ballotEntries={[]} t={mockTranslate} />);

    expect(screen.getByText("Balloting Result:")).toBeInTheDocument();
    expect(screen.getByText("Ballot Order")).toBeInTheDocument();
    expect(screen.getByText("Members")).toBeInTheDocument();
    expect(screen.getByText("Constituency")).toBeInTheDocument();
    expect(screen.getByText("Political Party")).toBeInTheDocument();
  });
});
