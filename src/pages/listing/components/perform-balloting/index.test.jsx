import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi } from "vitest";
import PerformBalloting from "./index";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import * as performBallotingService from "@/services/perform-balloting";
import * as documentsService from "@/services/documents";
import { describe, test, beforeEach, expect, afterEach } from "vitest";

const mockUseParams = vi.fn();
const mockUseSearchParams = vi.fn();
const mockUseNavigate = vi.fn();

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useSearchParams: () => mockUseSearchParams(),
    useParams: () => mockUseParams(),
    useNavigate: () => mockUseNavigate(),
  };
});

vi.mock("@/hooks", () => ({
  useLanguage: () => ({ t: (key) => key }),
}));

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock("@/hooks/use-auto-breadcrumb", () => ({
  useAutoBreadcrumb: vi.fn(),
}));

vi.mock("@kla-v2/ui-components", async () => {
  const PropTypes = await import("prop-types");

  const DatePicker = ({ onChange, value }) => (
    <input
      data-testid="date-picker"
      type="date"
      value={value || ""}
      onChange={(e) => onChange(new Date(e.target.value))}
    />
  );

  DatePicker.propTypes = {
    onChange: PropTypes.func.isRequired,
    value: PropTypes.string,
  };

  const Button = ({
    onClick,
    children,
    disabled,
    "data-testid": dataTestId,
  }) => (
    <button
      data-testid={
        dataTestId ||
        `button-${children.toString().replace(/\s+/g, "-").toLowerCase()}`
      }
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );

  Button.propTypes = {
    onClick: PropTypes.func.isRequired,
    children: PropTypes.node.isRequired,
    disabled: PropTypes.bool,
    "data-testid": PropTypes.string,
  };

  return {
    DatePicker,
    Button,
    toast: {
      error: vi.fn(),
    },
  };
});

vi.mock("@/components/document-metadata/index", () => ({
  DocumentMetadata: () => (
    <div data-testid="document-metadata">Document Metadata</div>
  ),
}));

vi.mock("@/icons/add-custom-icon", () => ({
  default: () => <div data-testid="add-custom-icon">Add Custom Icon</div>,
}));

vi.mock("./minister-card", () => ({
  default: ({ memberDisplayName }) => (
    <div data-testid="minister-card">{memberDisplayName}</div>
  ),
}));

vi.mock("./ballot-result-table", () => ({
  default: () => <div data-testid="ballot-result-table">Ballot Results</div>,
}));

vi.mock("./cancel-ballot-modal", () => ({
  default: ({ isOpen, onClose, onCancelConfirm }) =>
    isOpen ? (
      <div data-testid="cancel-ballot-modal">
        <button onClick={() => onCancelConfirm("Test cancellation reason")}>
          Confirm Cancel
        </button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

const mockStore = configureStore({
  reducer: {
    breadcrumb: (state = {}, action) => {
      switch (action.type) {
        case "breadcrumb/setBreadcrumb":
          return { ...state, breadcrumbs: action.payload };
        case "breadcrumb/resetBreadcrumb":
          return { ...state, breadcrumbs: [] };
        default:
          return state;
      }
    },
    // Add activeAssemblyApi reducer
    activeAssemblyApi: (
      state = {
        queries: {},
        mutations: {},
        provided: {},
        subscriptions: {},
        config: {
          reducerPath: "activeAssemblyApi",
          keepUnusedDataFor: 60,
          refetchOnMountOrArgChange: false,
          refetchOnReconnect: false,
          refetchOnFocus: false,
        },
      },
    ) => state,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

vi.mock("@/services/perform-balloting", () => ({
  useGetBallotPerformListQuery: vi.fn(),
  usePerformBallotingMutation: vi.fn(),
  useGetRandomizedBallotResultQuery: vi.fn(),
  useGetBallotByIdQuery: vi.fn(),
  useCancelBallotMutation: vi.fn().mockReturnValue([
    vi.fn().mockResolvedValue({
      unwrap: () => Promise.resolve({}),
    }),
    { isLoading: false },
  ]),
}));

vi.mock("@/services/documents", () => ({
  useGetMetadataDetailsQuery: vi.fn(),
}));

// Create a stable reference for the mock data
const mockActiveAssembly = {
  assembly: "15",
  session: "13",
};

vi.mock("@/services/master-data-management/active-assembly", () => ({
  selectActiveAssemblyItems: () => mockActiveAssembly,
}));

const renderWithProviders = (component) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>{component}</BrowserRouter>
    </Provider>,
  );
};

describe("PerformBalloting Component", () => {
  beforeEach(() => {
    // Set default mock values
    mockUseParams.mockReturnValue({ documentId: "12345" });
    mockUseSearchParams.mockReturnValue([
      new URLSearchParams({ questionDate: "2025-04-01" }),
      vi.fn(),
    ]);
    mockUseNavigate.mockReturnValue(vi.fn());

    performBallotingService.useGetBallotPerformListQuery.mockReturnValue({
      data: [
        {
          id: "1",
          memberDisplayName: "John Doe",
          constituencyName: "North District",
          politicalPartyName: "Party A",
        },
        {
          id: "2",
          memberDisplayName: "Jane Smith",
          constituencyName: "South District",
          politicalPartyName: "Party B",
        },
      ],
    });

    documentsService.useGetMetadataDetailsQuery.mockReturnValue({
      data: {
        kla: 15,
        session: 3,
        documentType: "Ballot",
        createdOn: "2025-03-15",
        createdBy: "Admin",
      },
    });

    const mockPerformBallot = vi.fn();
    mockPerformBallot.mockImplementation(() => ({
      unwrap: () =>
        Promise.resolve({
          id: "ballot-123",
          ballotStatus: "SUBMITTED",
          createdAt: "2025-03-15T10:00:00Z",
          createdBy: "Admin",
          ballotEntries: [
            {
              ballotOrder: 1,
              id: "1",
              memberDisplayName: "John Doe",
              constituencyName: "North District",
              politicalPartyName: "Party A",
            },
            {
              ballotOrder: 2,
              id: "2",
              memberDisplayName: "Jane Smith",
              constituencyName: "South District",
              politicalPartyName: "Party B",
            },
          ],
        }),
    }));

    performBallotingService.usePerformBallotingMutation.mockReturnValue([
      mockPerformBallot,
      { isLoading: false },
    ]);

    performBallotingService.useGetRandomizedBallotResultQuery.mockReturnValue({
      data: null,
      isSuccess: false,
    });

    performBallotingService.useGetBallotByIdQuery.mockReturnValue({
      data: null,
      isSuccess: false,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test("renders component with initial state", () => {
    renderWithProviders(<PerformBalloting />);

    expect(screen.getByTestId("document-metadata")).toBeInTheDocument();
    expect(screen.getByTestId("date-picker")).toBeInTheDocument();
    expect(
      screen.getByText("Notice Submitted Members (2)"),
    ).toBeInTheDocument();
  });

  test("shows cancel ballot modal and handles cancellation", async () => {
    renderWithProviders(<PerformBalloting />);

    // Should show the perform balloting button initially
    const performButton = screen.getByTestId("button-perform-balloting");
    expect(performButton).toBeInTheDocument();

    // Click perform balloting
    fireEvent.click(performButton);

    // Wait for the ballot to be performed and table to show
    await waitFor(() => {
      expect(screen.getByTestId("ballot-result-table")).toBeInTheDocument();
    });

    // First close the members view to show the cancel button
    const closeButton = screen.getByTestId("button-close");
    fireEvent.click(closeButton);

    // Now the cancel button should be visible
    await waitFor(() => {
      const cancelButton = screen.getByTestId("button-cancel");
      expect(cancelButton).toBeInTheDocument();
      fireEvent.click(cancelButton);
    });

    // Should show cancel modal
    expect(screen.getByTestId("cancel-ballot-modal")).toBeInTheDocument();

    // Confirm cancellation
    const confirmButton = screen.getByText("Confirm Cancel");
    fireEvent.click(confirmButton);
  });

  test("loads ballot results from URL params", async () => {
    // Mock useParams to return ballotingId
    mockUseParams.mockReturnValue({
      ballotingId: "12345",
      documentId: "12345",
    });

    renderWithProviders(<PerformBalloting />);

    // Initially should show spinner when loading from URL
    const spinner = document.querySelector(".animate-spin");
    expect(spinner).toBeInTheDocument();

    // Should show document metadata even while loading
    expect(screen.getByTestId("document-metadata")).toBeInTheDocument();

    // Should show the page title
    expect(screen.getByText("Perform Balloting")).toBeInTheDocument();
  });
});
