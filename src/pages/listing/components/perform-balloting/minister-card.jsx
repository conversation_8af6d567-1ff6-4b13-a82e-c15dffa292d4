import PropTypes from "prop-types";
const MinisterCard = ({
  memberDisplayName,
  constituencyName,
  politicalPartyName,
  id,
}) => {
  return (
    <div className="flex mt-2 p-2 mb-2 rounded border border-border-1 bg-white ml-3 ">
      <img
        src={`/mdm-service/api/private-member-list/${id}/photo`}
        alt={memberDisplayName}
        className="w-14 h-14 rounded-lg object-cover ml-3 "
      />
      <div className="flex flex-col ml-6">
        <h2 className="text-gray-950 text-sm font-medium">
          {memberDisplayName}
        </h2>
        <p className="text-slate-600 text-xs font-medium mt-1">
          {constituencyName}
        </p>
        <p className="text-gray-500 text-xs font-normal mt-1">
          {politicalPartyName}
        </p>
      </div>
    </div>
  );
};

MinisterCard.propTypes = {
  memberDisplayName: PropTypes.string.isRequired,
  constituencyName: PropTypes.string.isRequired,
  politicalPartyName: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
};

export default MinisterCard;
