import { useState, useEffect } from "react";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Textarea,
  toast,
} from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import PropTypes from "prop-types";
import { useCancelBallotMutation } from "@/services/perform-balloting";

export default function CancelBallotModal({
  isOpen,
  onClose,
  ballotId,
  onCancelConfirm,
}) {
  const { t } = useLanguage();
  const [reasonForCancellation, setReasonForCancellation] = useState("");
  const [cancelBallot, { isLoading }] = useCancelBallotMutation();

  useEffect(() => {
    if (isOpen) {
      setReasonForCancellation("");
    }
  }, [isOpen]);

  const handleCancel = async () => {
    if (!ballotId) {
      return;
    }

    try {
      await cancelBallot({
        ballotId,
        body: {
          id: ballotId,
          reasonForCancellation: reasonForCancellation,
        },
      }).unwrap();
      toast.success("Ballot successfully cancelled");
      onCancelConfirm(reasonForCancellation);
    } catch (error) {
      console.error("Failed to cancel ballot:", error);
      toast.error("Failed to cancel ballot");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("cancelBalloting")}</DialogTitle>
            <DialogDescription>{t("chooseWorkFlowActions")}</DialogDescription>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>
          <div>
            <Textarea
              label="Reason"
              placeholder="Type Reason"
              value={reasonForCancellation}
              onChange={(e) => setReasonForCancellation(e.target.value)}
            />
          </div>
          <DialogFooter className="!justify-center !items-center">
            <DialogClose asChild>
              <Button variant="neutral">{t("cancel")}</Button>
            </DialogClose>
            <Button
              variant="primary"
              type="submit"
              onClick={handleCancel}
              disabled={isLoading || !reasonForCancellation.trim()}
            >
              {isLoading ? "Processing..." : t("proceed")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

CancelBallotModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  ballotId: PropTypes.string,
  onCancelConfirm: PropTypes.func.isRequired,
};
