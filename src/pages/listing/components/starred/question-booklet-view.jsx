import { formatMalayalamDate } from "@/utils";
import PropTypes from "prop-types";

const QuestionBookletView = ({ questionData }) => {
  const questions = questionData?.starredQuestions || [];
  let memberCounter = 0;

  return (
    <div className="space-y-2">
      <div className="mx-auto p-6 bg-white rounded shadow-sm text-center">
        <div className="text-sm font-semibold space-y-1">
          <p>{questionData?.assembly}-ാം കേരള നിയമസഭ</p>
          <p>{questionData?.session}-ാം സമ്മേളനം</p>
        </div>

        <div className="mt-4 text-sm font-medium text-gray-600">
          {formatMalayalamDate(questionData?.createdAt)}
        </div>

        <div className="mt-3 font-semibold text-sm">
          നക്ഷത്രചിഹ്നമിട്ട ചോദ്യങ്ങൾ
          <br />
          <span className="font-normal text-xs">
            (ആകെ ചൊദ്യങ്ങള്‍: {questions.length})
          </span>
        </div>

        <div className="w-[645px] mt-5 border border-gray-300 p-4 rounded-md text-sm text-left inline-block">
          <p className="font-semibold text-center mb-2">
            മറുപടി നൽകുന്ന മന്ത്രിമാർ
          </p>
          {questionData.groupEntries?.map((minister, index) => (
            <div key={index} className="pl-10">
              <p>
                {minister.designationInLocal || minister.ministerDesignation}
              </p>
            </div>
          ))}
        </div>
      </div>

      {questions.map((question, index) => {
        const bgColors = ["bg-warning-100", "bg-error-100", "bg-green-100"];
        const randomColor =
          bgColors[Math.floor(Math.random() * bgColors.length)];
        const members =
          Array.isArray(question.members) && question.members.length > 0
            ? question.members.map((member) => ({
                id: member.memberId || member.id || `member-${index}`,
                memberDisplayName:
                  member.memberDisplayName ||
                  question.memberDisplayName ||
                  "Member Name",
                constituency:
                  member.constituency ||
                  question.constituencyName ||
                  "Constituency",
                politicalParty:
                  member.politicalParty ||
                  question.politicalPartyName ||
                  "Political Party",
              }))
            : [
                {
                  id: `default-member-${index}`,
                  memberDisplayName:
                    question.memberDisplayName || "Member Name",
                  constituency: question.constituencyName || "Constituency",
                  politicalParty:
                    question.politicalPartyName || "Political Party",
                },
              ];

        const clauses =
          Array.isArray(question.clauses) && question.clauses.length > 0
            ? question.clauses
            : [
                {
                  order: 1,
                  content: question.noticeHeading || "No content available",
                },
              ];

        return (
          <div
            key={`question-${index}`}
            className="bg-white p-6 rounded-md shadow-sm space-y-4 border"
          >
            <div className="items-center gap-2 flex justify-center">
              <span
                className={`w-6 h-6 flex items-center justify-center rounded-full ${randomColor} text-black text-sm`}
              >
                {index + 1}
              </span>
              <span className="text-primary typography-body-text-s-18">
                {question.noticeHeading || "Question Notice"}
              </span>
            </div>

            <div className="space-y-2">
              {members.map((member, memberIndex) => {
                memberCounter++;

                return (
                  <div
                    key={`member-${memberIndex}`}
                    className="flex items-center gap-4"
                  >
                    <span className="typography-body-text-m-14 w-2">
                      {memberCounter}
                    </span>
                    <img
                      src={"https://placehold.co/400"}
                      alt={member.memberDisplayName}
                      className="w-11 h-11 rounded-full"
                    />
                    <div className="flex flex-row items-center gap-2 text-sm">
                      <span className="typography-body-text-m-14">
                        {member.memberDisplayName}
                      </span>
                      <span className="typography-body-text-m-12 text-grey-600">
                        {member.constituency}
                      </span>
                      <span className="typography-body-text-r-12 text-grey-500">
                        {member.politicalParty}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="space-y-2">
              <span className="typography-para-16">
                താഴെ കാണുന്ന ചോദ്യങ്ങൾക്കു{" "}
                {question.ministerDesignationInLocal || "മന്ത്രി"} സദയം മറുപടി
                പറയാമോ?
              </span>
            </div>

            <div className="space-y-2">
              {clauses.map((clause, clauseIndex) => (
                <div
                  key={`clause-${clauseIndex}`}
                  className="border p-3 rounded-md flex flex-row"
                >
                  <span className="typography-para-14 mr-2">
                    (
                    {String.fromCharCode(
                      65 + (clause.order ? clause.order - 1 : clauseIndex),
                    )}
                    )
                  </span>
                  <div>{clause.content}</div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

QuestionBookletView.propTypes = {
  questionData: PropTypes.shape({
    assembly: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    session: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    createdAt: PropTypes.string,
    groupEntries: PropTypes.array,
    starredQuestions: PropTypes.array,
  }),
};

export default QuestionBookletView;
