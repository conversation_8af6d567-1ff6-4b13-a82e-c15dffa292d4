import PropTypes from "prop-types";
import { useState } from "react";
import { User, FileText, RotateCcw } from "lucide-react";
import { Checkbox, Button } from "@kla-v2/ui-components";
import { StarredIcon } from "@/icons/star-icon";
import { useDroppable } from "@dnd-kit/core";
import { useLanguage } from "@/hooks";

const getPriorityBadgeStyle = (priority) => {
  switch (priority) {
    case "P1":
      return "border border-primary bg-blue-100 text-primary typography-body-text-s-11 w-20 h-8 flex items-center justify-center rounded-full ";
    default:
      return "border bg-gray-100 text-gray-700 typography-body-text-s-11 w-20 h-8 flex items-center justify-center rounded-full";
  }
};
const getCounterStyle = (confirmedCount) => {
  if (confirmedCount === 0) {
    return "text-gray-400 typography-body-text-s-18 mr-5";
  } else {
    return "text-primary typography-body-text-s-18 font-semibold m-2";
  }
};
const SelectedBallotCard = ({
  selectedEntries,
  onRemove,
  onConfirm,
  onClear,
  confirmedEntryIds = [],
  maxNotices = 30,
}) => {
  const { t } = useLanguage();

  const [selectedNotice, setSelectedNotice] = useState(null);
  const [confirmedNotices, setConfirmedNotices] = useState([]);

  const { setNodeRef } = useDroppable({
    id: "selected-ballot-dropzone",
  });

  const totalConfirmedNotices = confirmedNotices.length;

  const handleClear = () => {
    if (onRemove && typeof onRemove === "function") {
      selectedEntries?.forEach((entry) => onRemove(entry.id));
    }

    setSelectedNotice(null);
    setConfirmedNotices([]);

    if (onClear && typeof onClear === "function") {
      onClear();
    }
  };

  const handleCheckboxChange = (noticeId) => {
    if (selectedNotice === noticeId) {
      setSelectedNotice(null);
    } else {
      setSelectedNotice(noticeId);
    }
  };

  const handleConfirmNotice = () => {
    if (selectedNotice) {
      let selectedEntry = null;
      let selectedNoticeObject = null;

      for (const entry of selectedEntries) {
        const notices = generateMockNotices(entry);
        const notice = notices.find((n) => n.id === selectedNotice);
        if (notice) {
          selectedEntry = entry;
          selectedNoticeObject = notice;
          break;
        }
      }

      if (selectedEntry && selectedNoticeObject) {
        if (onConfirm && typeof onConfirm === "function") {
          const newConfirmedCount = confirmedNotices.length + 1;
          onConfirm(selectedEntry.id, newConfirmedCount);
        }

        setConfirmedNotices((prev) => [...prev, selectedNoticeObject.id]);

        setSelectedNotice(null);
      }
    }
  };

  const generateMockNotices = (entry, count = 3) => {
    if (entry && entry.noticeHeading) {
      return Array(count)
        .fill()
        .map((_, index) => ({
          id: `notice-${entry.id}-${index}`,
          text: entry.noticeHeading,
          clubbed: entry.clubbed || index % 2 === 0 ? "false" : "true",
          starred: true,
          priority: "P1",
          entryId: entry.id,
        }));
    }
  };

  return (
    <div className="w-full p-2 selected-ballot-container flex flex-col h-full">
      <div className="flex items-center justify-between mb-4 p-2">
        <div className="flex-1">
          <div className="text-primary typography-body-text-s-18">
            Setting Up
          </div>
          <p className="text-gray-400 text-sm mt-1">
            Drag and drop members into Question Setting UP
          </p>
        </div>
        <div className="flex items-center">
          <div className={getCounterStyle(totalConfirmedNotices, maxNotices)}>
            {totalConfirmedNotices}/{maxNotices}
          </div>
          <Button
            variant="neutral"
            onClick={handleClear}
            className="flex items-center"
          >
            <RotateCcw size={16} className="text-gray-400" />
            <span className="mr-1">Clear</span>
          </Button>
        </div>
      </div>

      {!selectedEntries || selectedEntries.length === 0 ? (
        <div
          ref={setNodeRef}
          className="border rounded-lg h-80 min-h-[300px] w-full empty-state flex items-center justify-center text-gray-400"
        ></div>
      ) : (
        <div
          ref={setNodeRef}
          className="w-full overflow-y-auto [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden flex-grow min-h-[300px] max-h-[60vh]"
          style={{ overflowY: "auto" }}
        >
          {selectedEntries
            .filter((entry) => confirmedEntryIds.includes(entry.id))
            .map((entry, entryIndex) => {
              const notices = generateMockNotices(entry);
              const confirmedNoticesForEntry = notices.filter((notice) =>
                confirmedNotices.includes(notice.id),
              );

              return (
                <div
                  key={entry.id}
                  className="mt-4 border rounded-lg overflow-hidden bg-white shadow-sm"
                >
                  <div className="p-3 ">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-success-100 flex items-center justify-center text-primary-80 font-semibold text-sm mr-3">
                        {entryIndex + 1}
                      </div>
                      <div className="w-8 h-8 mr-3">
                        <img
                          src={"https://placehold.co/400"}
                          alt={entry.memberDisplayName || "Shri A.K.M Ashraf"}
                          className="h-8 w-8 rounded-full object-cover border"
                        />
                      </div>
                      <div className="flex-1 ml-1 ">
                        <h3 className="font-medium ">
                          {entry.memberDisplayName || "Shri A.K.M Ashraf"}
                          <span className="text-xs text-gray-600 ml-1">
                            {entry.constituencyName || "Manjeswar"}
                            {entry.politicalPartyName && (
                              <> · {entry.politicalPartyName}</>
                            )}
                            {!entry.politicalPartyName &&
                              " · Indian Union Muslim League"}
                          </span>
                        </h3>
                      </div>
                      <div className="ml-auto text-sm font-semibold">
                        {entry.totalNotices || "3"} Notices
                      </div>
                    </div>
                  </div>

                  <div>
                    {confirmedNoticesForEntry.map((notice) => (
                      <div
                        key={notice.id}
                        className="flex mt-2 p-2 mb-2 rounded border border-primary-tint-90 m-2"
                      >
                        <Checkbox
                          className="mr-3 m-2"
                          name={`notice-${notice.id}`}
                          checked={true}
                          disabled={true}
                        />
                        <div className="flex items-center mx-2">
                          <User size={16} className="text-gray-500" />
                        </div>
                        <div className="flex items-center flex-1 mx-2">
                          <div className="mr-2">
                            <StarredIcon />
                          </div>
                          <span className="text-sm">{notice.text}</span>
                        </div>
                        <div className="flex items-center ml-auto">
                          <div
                            className={getPriorityBadgeStyle(notice.priority)}
                          >
                            {notice.priority}
                          </div>
                          <span className="text-primary typography-body-text-s-11 cursor-pointer ml-3">
                            View
                          </span>
                          <FileText
                            size={18}
                            className="text-gray-300 ml-3 m-1"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          {selectedEntries
            .filter((entry) => !confirmedEntryIds.includes(entry.id))
            .map((entry, entryIndex) => {
              const notices = generateMockNotices(entry);

              return (
                <div
                  key={entry.id}
                  className="mt-4 border rounded-lg overflow-hidden bg-white shadow-sm"
                >
                  <div className="p-3 border border-b-primary-tint-90 bg-gray-50">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-success-100 flex items-center justify-center text-primary-80 font-semibold text-sm mr-3">
                        {confirmedEntryIds.length + entryIndex + 1}
                      </div>
                      <div className="w-8 h-8 mr-3">
                        <img
                          src={"https://placehold.co/400"}
                          alt={entry.memberDisplayName || "Shri A.K.M Ashraf"}
                          className="h-8 w-8 rounded-full object-cover border"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">
                          {entry.memberDisplayName || "Shri A.K.M Ashraf"}
                          <span className="text-xs text-gray-600 ml-1">
                            {entry.constituencyName || "Manjeswar"}
                            {entry.politicalPartyName && (
                              <> · {entry.politicalPartyName}</>
                            )}
                            {!entry.politicalPartyName &&
                              " · Indian Union Muslim League"}
                          </span>
                        </h3>
                      </div>
                      <div className="ml-auto text-sm font-semibold">
                        {entry.totalNotices || "3"} Notices
                      </div>
                    </div>
                  </div>

                  <div>
                    {notices.map((notice) => {
                      const isDisabled =
                        selectedNotice !== null && selectedNotice !== notice.id;

                      return (
                        <div
                          key={notice.id}
                          className={`flex mt-2 p-2 mb-2 rounded border border-primary-tint-90 bg-white m-2 ${
                            selectedNotice === notice.id
                              ? "bg-gray-50 "
                              : isDisabled
                                ? "cursor-not-allowed opacity-50"
                                : "hover:shadow-md transition-shadow"
                          }`}
                        >
                          <Checkbox
                            className="mr-3 mt-2"
                            name={`notice-${notice.id}`}
                            checked={selectedNotice === notice.id}
                            disabled={isDisabled}
                            onCheckedChange={() =>
                              handleCheckboxChange(notice.id)
                            }
                          />
                          <div className="flex items-center mx-2">
                            <User size={16} className="text-gray-500" />
                          </div>
                          <div className="flex items-center flex-1 mx-2">
                            <div className="mr-2">
                              <StarredIcon />
                            </div>
                            <span className="text-sm">{notice.text}</span>
                          </div>
                          <div className="flex items-center ml-auto">
                            <div
                              className={getPriorityBadgeStyle(notice.priority)}
                            >
                              {notice.priority}
                            </div>
                            <span className="text-primary typography-body-text-s-11 cursor-pointer ml-3">
                              View
                            </span>
                            {selectedNotice === notice.id && (
                              <FileText
                                size={18}
                                className="text-gray-300 ml-2"
                              />
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          {selectedNotice && (
            <div className="mt-4 flex justify-end items-end sticky bottom-0 bg-white p-2 ">
              <Button
                size="sm"
                variant="secondary"
                onClick={handleConfirmNotice}
              >
                {t("Confirm")}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

SelectedBallotCard.propTypes = {
  selectedEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      memberDisplayName: PropTypes.string,
      constituencyName: PropTypes.string,
      politicalPartyName: PropTypes.string,
      ballotOrder: PropTypes.number,
      totalNotices: PropTypes.number,
      noticeHeading: PropTypes.string,
      clubbed: PropTypes.bool,
    }),
  ),
  onRemove: PropTypes.func,
  onConfirm: PropTypes.func,
  onClear: PropTypes.func,
  confirmedEntryIds: PropTypes.array,
  maxNotices: PropTypes.number,
};

SelectedBallotCard.defaultProps = {
  onRemove: () => {},
  onConfirm: () => {},
  onClear: () => {},
  confirmedEntryIds: [],
  maxNotices: 30,
};

export default SelectedBallotCard;
