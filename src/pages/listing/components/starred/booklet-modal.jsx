import { DocumentMetadata } from "@/components/document-metadata";
import { useLanguage } from "@/hooks";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogClose,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { Edit } from "lucide-react";
import { CircleCheck, ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ROUTE_CONFIG } from "@/config/routes";
import PropTypes from "prop-types";
const BookletPreviewModal = ({ isOpen, onClose, children, documentdata }) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const documentMetaData = {
    assembly: documentdata?.assembly?.toString() || "15",
    session: documentdata?.session?.toString() || "",
    documentType: documentdata?.documentType || "Setting of Starred Questions",
    currentNo: documentdata?.currentNumber || "",
    createdOn: new Date().toLocaleDateString("en-GB"),
    createdBy: documentdata?.createdBy || "",
    name: documentdata?.name || "Setting of Starred Questions",
  };
  const handleGoBack = () => {
    navigate(ROUTE_CONFIG.SECTION.STARRED_QUESTIONS);
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("Generated View")}
              </p>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose>
              <Button variant="neutral" onClick={handleGoBack} className="mr-2">
                <ArrowLeft className="mr-1" size={16} />
                Back
              </Button>
              <Button
                size="sm"
                variant="secondary"
                iconPosition="left"
                icon={Edit}
                className="p-5"
              >
                {t("Cancel")}
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                size="sm"
                variant="primary"
                iconPosition="right"
                className="p-5"
              >
                {t("Approve")}
                <CircleCheck size={20} className="fill-white text-secondary" />
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

BookletPreviewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentdata: PropTypes.object,
};

export default BookletPreviewModal;
