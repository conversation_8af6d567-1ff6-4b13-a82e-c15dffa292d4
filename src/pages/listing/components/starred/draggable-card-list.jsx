import PropTypes from "prop-types";
import { useDraggable } from "@dnd-kit/core";

const DraggableItem = ({ entry, isEnabled, confirmedEntryIds }) => {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: entry.id,
    data: {
      entry,
    },
    disabled: !isEnabled,
  });

  const style = isDragging ? { opacity: 0.3 } : undefined;

  const getBackgroundColor = () => {
    if (confirmedEntryIds.includes(entry.id)) {
      return "bg-warning-200";
    }
    return "bg-white";
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex mt-2 p-2 mb-2 rounded border border-border-1 ${getBackgroundColor()} ${
        isEnabled
          ? "cursor-move hover:shadow-md transition-shadow"
          : "cursor-not-allowed opacity-50"
      }`}
      {...attributes}
      {...(isEnabled ? listeners : {})}
    >
      <div className="relative flex w-[54px] h-[60px]">
        <div className="absolute -top-3 -left-3 w-6 h-6 bg-white rounded-full flex items-center border-2 border-pink-500 justify-center text-black font-medium text-sm z-10 ml-1">
          {entry.ballotOrder}
        </div>
        <img
          src={"https://placehold.co/400"}
          alt={entry.memberDisplayName}
          className="h-14 w-14 rounded-lg object-cover border"
        />
      </div>
      <div className="flex flex-col ml-3 flex-1">
        <div className="flex justify-between items-start">
          <h4 className="text-gray-900 font-medium">
            {entry.memberDisplayName}
          </h4>
        </div>
        <span className="text-gray-600 text-sm">{entry.constituencyName}</span>
        <p className="text-gray-500 text-xs">{entry.politicalPartyName}</p>
      </div>
      <span className="text-xl font-bold mt-3">{entry.totalNotices}</span>
    </div>
  );
};

DraggableItem.propTypes = {
  entry: PropTypes.shape({
    id: PropTypes.string.isRequired,
    ballotOrder: PropTypes.number.isRequired,
    memberDisplayName: PropTypes.string.isRequired,
    constituencyName: PropTypes.string,
    politicalPartyName: PropTypes.string,
    totalNotices: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
  isEnabled: PropTypes.bool.isRequired,
  confirmedEntryIds: PropTypes.array,
  isSelected: PropTypes.bool,
};

DraggableItem.defaultProps = {
  confirmedEntryIds: [],
  isSelected: false,
};

const DraggableCardList = ({
  groupEntries,
  selectedEntries,
  confirmedEntryIds,
  totalConfirmedNotices = 0,
}) => {
  if (!groupEntries || groupEntries.length === 0) {
    return <div>No ballot entries available</div>;
  }

  if (!Array.isArray(selectedEntries)) {
    console.error("selectedEntries is not an array:", selectedEntries);
    return <div>Error: Selected entries data is invalid</div>;
  }

  const selectedEntryIds = selectedEntries.map((entry) => entry.id);

  if (groupEntries.length === 0) {
    return (
      <div className="text-gray-500 text-center p-4">No entries available</div>
    );
  }

  return (
    <div
      className="w-full overflow-y-auto [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden min-h-[300px] max-h-[60vh]"
      style={{ overflowY: "auto" }}
    >
      {groupEntries.map((entry) => {
        const isConfirmed = confirmedEntryIds.includes(entry.id);
        const isSelected = selectedEntryIds.includes(entry.id);

        const sortedEntries = [...groupEntries].sort(
          (a, b) => a.ballotOrder - b.ballotOrder,
        );
        const entryPosition = sortedEntries.findIndex((e) => e.id === entry.id);
        const shouldBeEnabled =
          entryPosition === 0 || totalConfirmedNotices >= entryPosition;
        const isEnabled = !isConfirmed && shouldBeEnabled;

        return (
          <DraggableItem
            key={entry.id}
            entry={entry}
            isEnabled={isEnabled}
            isSelected={isSelected}
            confirmedEntryIds={confirmedEntryIds || []}
          />
        );
      })}
    </div>
  );
};

DraggableCardList.propTypes = {
  groupEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      ballotOrder: PropTypes.number.isRequired,
      memberDisplayName: PropTypes.string.isRequired,
      constituencyName: PropTypes.string,
      politicalPartyName: PropTypes.string,
      totalNotices: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ).isRequired,
  selectedEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
    }),
  ),
  confirmedEntryIds: PropTypes.array,
  totalConfirmedNotices: PropTypes.number,
};

DraggableCardList.defaultProps = {
  selectedEntries: [],
  confirmedEntryIds: [],
  totalConfirmedNotices: 0,
};

export default DraggableCardList;
