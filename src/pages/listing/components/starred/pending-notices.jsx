import { DataTable } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { Users, User } from "lucide-react";

export default function PendingNotices({ ballotEntries, t }) {
  const totalPendingNotices = ballotEntries ? ballotEntries.length : 0;
  const columns = [
    { accessorKey: "noticeNumber", header: t("Notice No.") },
    {
      accessorKey: "clubbed/single",
      header: t("Clubbed/Single"),
      cell: ({ row }) => {
        const { clubbed } = row.original;
        return clubbed === false ? <User size={17} /> : <Users size={17} />;
      },
    },
    { accessorKey: "noticeHeading", header: t("Notice Heading") },
    {
      accessorKey: "designation",
      header: t("Designation"),
      cell: ({ row }) => (
        <span className="whitespace-nowrap">{row.original.designation}</span>
      ),
    },
    { accessorKey: "portfolio", header: t("Portfolio") },
    {
      accessorKey: "currentlyWith",
      header: t("Currently With"),
      cell: ({ row }) => {
        const { currentlyWith } = row.original;
        return currentlyWith ? (
          <>
            {currentlyWith.name}{" "}
            <div className="text-sm text-gray-500 inline ">
              {currentlyWith.designation}
            </div>
          </>
        ) : (
          ""
        );
      },
    },
  ];
  return (
    <div className="w-full px-5 flex flex-col h-full">
      <h3 className="typography-page-heading font-inter text-lg font-semibold mb-4 mt-2 p-1 flex-none">
        Pending Notices ({totalPendingNotices})
      </h3>
      <div className="flex-1 overflow-auto [-ms-overflow-style:'none'] [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden">
        <div className="h-full">
          <DataTable
            columns={columns}
            data={ballotEntries || []}
            stickyHeader={true}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
}
PendingNotices.propTypes = {
  ballotEntries: PropTypes.array,
  t: PropTypes.func.isRequired,
};
PendingNotices.defaultProps = {
  ballotEntries: [],
};
