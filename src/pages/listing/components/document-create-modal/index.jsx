import { useNavigate } from "react-router-dom";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Combobox,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Input,
  toast,
} from "@kla-v2/ui-components";
import PropType from "prop-types";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { getFormFields } from "./form-fields";
import { useCreateDocumentDraftMutation } from "@/services/documents";
import { useCreateScheduleOfActivityMutation } from "@/services/schedule-of-activity";
import {
  useCreateAllotmentOfDaysMutation,
  useLazyGetLatestApprovedQuery,
} from "@/services/allotment-of-days";
import { useLazyGetLatestApprovedMinisterGroupsQuery } from "@/services/minister-designation-group";
import { useSelector } from "react-redux";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";
import { useCreateLateAnswerBulletinMutation } from "@/services/late-answer-bulletin";
import { useCreateDelayedAnswerBulletinMutation } from "@/services/delayed-answer-bulletin";
import { useUpdateDelayStatementDraftMutation } from "@/services/delay-statement";
import { useCreateAnswerStatusReportMutation } from "@/services/answer-status-report";
import documentTypes from "@/utils/document-types/document-types";
const formSchema = z.object({
  type: z.string(),
  name: z.string(),
});
export default function DocumentCreateModal({ isOpen, onClose }) {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const formFields = getFormFields(t);
  const [createDocumentDraft] = useCreateDocumentDraftMutation();
  const [createScheduleOfActivity] = useCreateScheduleOfActivityMutation();
  const [createAllotmentOfDays] = useCreateAllotmentOfDaysMutation();
  const [createLateAnswerBulletin] = useCreateLateAnswerBulletinMutation();
  const [createAnswerStatusReport] = useCreateAnswerStatusReportMutation();
  const [createDelayedAnswerBulletin] =
    useCreateDelayedAnswerBulletinMutation();
  const [createDelayStatementin] = useUpdateDelayStatementDraftMutation();
  const [fetchLatestMinisterGroups] =
    useLazyGetLatestApprovedMinisterGroupsQuery();
  const [fetchLatestApprovedID] = useLazyGetLatestApprovedQuery();
  const activeAssembly = useSelector(selectActiveAssemblyItems);

  const assembly = activeAssembly?.assembly;
  const session = activeAssembly?.session;
  const form = useForm({ resolver: zodResolver(formSchema) });
  const {
    handleSubmit,
    reset,
    formState: { isValid, isDirty },
  } = form;

  const onSubmit = async (data) => {
    try {
      let response;

      if (data.type === "SCHEDULE_OF_ACTIVITY") {
        const referredAllotmentId =
          await fetchLatestApprovedID("ALLOTMENT_OF_DAYS");
        const referredAllot = referredAllotmentId?.data?.id;
        response = await createScheduleOfActivity({
          data,
          referredAllotmentOfDaysId: referredAllot,
          assembly: assembly,
          session: session,
        });
      }
      // Check if selected document type is "ALLOTMENT_OF_DAYS"
      else if (data.type === "ALLOTMENT_OF_DAYS") {
        const ministerGroupResponse = await fetchLatestMinisterGroups(
          "MINISTER_DESIGNATION_GROUP",
        );

        const ministerGroupId = ministerGroupResponse?.data?.id; // Extract ID from response
        response = await createAllotmentOfDays({
          data: {
            ...data,
            referredMinisterDesignationGroupId: ministerGroupId,
          }, // Pass the minister group ID
        });
      } else if (data.type === "DELAYED_ANSWER_BULLETIN") {
        response = await createDelayedAnswerBulletin({
          data: {
            ...data,
            assembly: assembly,
            session: session,
          },
        });
      } else if (data.type === "LATE_ANSWER_BULLETIN") {
        response = await createLateAnswerBulletin({
          data: {
            ...data,
            assembly: assembly,
            session: session,
          },
        });
      } else if (data.type === "ANSWER_STATUS_REPORT") {
        response = await createAnswerStatusReport({
          data: {
            ...data,
            assembly: assembly,
            session: session,
          },
        });
      } else if (data.type === "DELAY_STATEMENT_LIST") {
        response = await createDelayStatementin({
          data: {
            ...data,
            assembly: assembly,
            session: session,
          },
        });
      } else {
        response = await createDocumentDraft({ data });
      }

      if (response.error) {
        throw new Error(response.error);
      }

      const { type, id } = response.data;

      if (type) {
        const docType = documentTypes.find((doc) => doc.value === type);
        if (docType) {
          toast.success("Document created successfully");
          let route = `${docType.route}/${id}`;
          navigate(route);
        } else {
          toast.error("Error", {
            description: t("noMatchingDocumentType"),
          });
        }
      }
    } catch (error) {
      console.error("Error creating document:", error);
    }
    onClose();
  };
  return (
    <Dialog className="sm:text-left" open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent onCloseAutoFocus={reset}>
          <DialogHeader>
            <DialogTitle>Create Document</DialogTitle>
            <DialogDescription className="text-gray-500">
              Select below option(s) to create Document
            </DialogDescription>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>
          <FormProvider {...form}>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-4"
              role="form"
              data-testid="document-create-form"
            >
              <div>
                <FormField
                  name={formFields.DOCUMENT_TYPE.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          className="w-full"
                          options={documentTypes}
                          isMulti={false}
                          modalPopover={true}
                          onValueChange={field.onChange}
                          {...field}
                          {...formFields.DOCUMENT_TYPE}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div>
                <FormField
                  name={formFields.DOCUMENT_NAME.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input {...formFields.DOCUMENT_NAME} {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="!justify-center !items-center">
                <DialogClose asChild>
                  <Button variant="neutral">{t("cancel")}</Button>
                </DialogClose>
                <Button
                  variant="primary"
                  type="submit"
                  disabled={!isValid || !isDirty}
                >
                  {t("confirm")}
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

DocumentCreateModal.propTypes = {
  isOpen: PropType.bool,
  onClose: PropType.func,
};
