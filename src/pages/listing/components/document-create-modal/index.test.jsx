import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { vi } from "vitest";
import { describe, test, beforeEach, expect } from "vitest";
import DocumentCreateModal from "./index";
import { useCreateDocumentDraftMutation } from "@/services/documents";
import { useLanguage } from "@/hooks";

vi.mock("@/services/documents", () => ({
  useCreateDocumentDraftMutation: vi.fn(),
}));

vi.mock("@/hooks", () => ({
  useLanguage: vi.fn(),
}));

const navigateMock = vi.fn();
vi.mock("react-router-dom", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useNavigate: () => navigateMock,
  };
});

const mockStore = configureStore([]);

describe("DocumentCreateModal Component", () => {
  let store;
  let createDocumentDraftMock;
  let tMock;
  let onCloseMock;

  beforeEach(() => {
    store = mockStore({});
    onCloseMock = vi.fn();
    createDocumentDraftMock = vi.fn(() => ({
      unwrap: vi.fn().mockResolvedValue({ id: "123" }),
    }));
    useCreateDocumentDraftMutation.mockReturnValue([createDocumentDraftMock]);
    tMock = vi.fn((key) => key);
    useLanguage.mockReturnValue({ t: tMock });
    navigateMock.mockClear();
  });

  test("renders DocumentCreateModal component", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DocumentCreateModal isOpen={true} onClose={onCloseMock} />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByText("Create Document")).toBeInTheDocument();
    expect(
      screen.getByText("Select below option(s) to create Document"),
    ).toBeInTheDocument();
  });

  test("disables confirm button initially", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <DocumentCreateModal isOpen={true} onClose={onCloseMock} />
        </MemoryRouter>
      </Provider>,
    );

    const confirmButton = screen.getByText("confirm");
    expect(confirmButton).toBeDisabled();
  });

  test("calls createDocumentDraft and navigates on form submit", async () => {
    const documentType = "Minister Designation Group";
    const documentName = "Test Document";

    vi.mock("./form-fields", () => ({
      getFormFields: () => ({
        DOCUMENT_TYPE: { name: "type" },
        DOCUMENT_NAME: { name: "name" },
      }),
    }));

    render(
      <Provider store={store}>
        <MemoryRouter>
          <DocumentCreateModal isOpen={true} onClose={onCloseMock} />
        </MemoryRouter>
      </Provider>,
    );

    const form = screen.getByTestId("document-create-form");

    Object.defineProperty(form, "elements", {
      get: () => ({
        type: { value: documentType },
        name: { value: documentName },
      }),
    });

    const confirmButton = screen.getByText("confirm");
    Object.defineProperty(confirmButton, "disabled", { value: false });

    fireEvent.submit(form);

    waitFor(() => {
      expect(createDocumentDraftMock).toHaveBeenCalledWith({
        type: documentType,
        name: documentName,
      });
      expect(onCloseMock).toHaveBeenCalled();

      expect(navigateMock).toHaveBeenCalled();
    });
  });
});
