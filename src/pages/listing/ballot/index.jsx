import { useLanguage, useDebounce, useActiveAssembly } from "@/hooks";
import { useGetBallotListQuery } from "@/services/ballots";
import {
  Button,
  DataTable,
  FilterDropdown,
  Input,
  PaginationSelect,
  Paginator,
  TableDropdownMenu,
  TableTag,
} from "@kla-v2/ui-components";
import { Eye, Pencil, Plus, RotateCcw, Search } from "lucide-react";
import PropTypes from "prop-types";
import { useState, useEffect, useMemo, useCallback } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { buildRoute } from "@/config/routes";
import { getTableStatus, getTableVariant } from "@/utils/tag-variants";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useRef } from "react";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

const StatusCell = ({ renderValue }) => {
  const status = renderValue();
  return (
    <TableTag variant={getTableVariant(status)} className={"ml-auto w-max"}>
      {getTableStatus(status)}
    </TableTag>
  );
};

StatusCell.propTypes = {
  renderValue: PropTypes.func,
};

const BallotList = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState({});
  const [searchInput, setSearchInput] = useState(
    searchParams.get("searchText") || "",
  );

  const page = parseInt(searchParams.get("page") || "1");
  const size = searchParams.get("size") || "10";
  const searchQuery = searchParams.get("searchText") || "";
  const { activeAssembly } = useActiveAssembly();
  const debouncedSearchValue = useDebounce(searchInput, 500);

  const filters = useMemo(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      return {
        Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
        Session: { filter: "Session", value: activeAssembly?.session },
      };
    }
    return {};
  }, [activeAssembly?.assembly, activeAssembly?.session]);

  useAutoBreadcrumb();
  const filteredDataRef = useRef(filteredData);

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      Object.entries(newParams).forEach(([key, value]) => {
        if (value !== undefined) {
          updatedParams.set(key, value.toString());
        }
      });

      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  useEffect(() => {
    filteredDataRef.current = filteredData;
  }, [filteredData]);

  useEffect(() => {
    if (
      Object.keys(filters).length > 0 &&
      Object.keys(filteredDataRef.current).length === 0
    ) {
      setFilteredData(filters);
    }
  }, [filters]);

  useEffect(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      const updatedFilters = {
        assembly: {
          filter: "equals",
          value: String(activeAssembly.assembly),
        },
        session: {
          filter: "equals",
          value: String(activeAssembly.session),
        },
      };
      setFilteredData(updatedFilters);
      updateParams({
        assembly: String(activeAssembly.assembly),
        session: String(activeAssembly.session),
      });
    }
  }, [activeAssembly, updateParams]);

  useEffect(() => {
    updateParams({ searchText: debouncedSearchValue });
  }, [debouncedSearchValue, updateParams]);

  const extractFilterValue = (filterData) => {
    if (!filterData) return null;
    if (Array.isArray(filterData.value)) {
      return filterData.value.length > 0 ? filterData.value : null;
    }

    return filterData.value !== undefined && filterData.value !== ""
      ? filterData.value
      : null;
  };

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      if (!filterData || !filterData.filter) return;

      const { filter } = filterData;
      const value = extractFilterValue(filterData);

      if (value === null) return;

      switch (key) {
        case "assembly":
          queryParams["assembly"] =
            typeof value === "object" && value !== null
              ? value.assembly || ""
              : value || "";
          break;
        case "session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.session || ""
              : value || "";
          break;
        case "groupName":
          if (typeof value === "object" && value !== null) {
            queryParams["groupName"] = value.undefined || "";
          } else if (Array.isArray(value)) {
            queryParams["groupName"] = value[0] || "";
          } else {
            queryParams["groupName"] = value || "";
          }
          break;
        case "ballotStatus":
          if (typeof value === "object" && value !== null) {
            queryParams["ballotStatus"] = value.undefined || "";
          } else if (Array.isArray(value)) {
            queryParams["ballotStatus"] = value[0] || "";
          } else {
            queryParams["ballotStatus"] = value || "";
          }
          break;
        case "questionDate":
          if (filter === "between" && value?.from && value?.to) {
            queryParams["questionDateStartDate"] = value.from;
            queryParams["questionDateEndDate"] = value.to;
          } else if (filter === "Is" && value) {
            queryParams["questionDate"] = value;
          }
          break;
        case "createdAt":
          if (filter === "between" && value?.from && value?.to) {
            queryParams["createdAtStartDate"] = value.from;
            queryParams["createdAtEndDate"] = value.to;
          } else if (filter === "Is" && value) {
            queryParams["createdAt"] = value;
          }
          break;
        default:
          if (Array.isArray(value)) {
            queryParams[key] = value.join(",");
          } else {
            queryParams[key] = value;
          }
      }
    });

    return queryParams;
  };

  const {
    data: documentsListData,
    isLoading: isDocumentsListLoading,
    isFetching: isDocumentsListFetching,
    error: documentsListError,
    refetch: refetchDocumentsList,
  } = useGetBallotListQuery(
    {
      ...processFilters(filteredData),
      searchText: searchQuery,
      page: page - 1,
      size,
    },
    {
      refetchOnMountOrArgChange: true,
    },
  );

  const isLoading = isDocumentsListFetching;

  useEffect(() => {
    refetchDocumentsList();
  }, []);

  const ActionCell = ({ row }) => {
    const { id } = row?.original || {};

    const menuItems = [
      {
        label: (
          <div className="flex items-center gap-2">
            <Eye size={16} /> View
          </div>
        ),
        onClick: () => {
          navigate(buildRoute.balloting(id));
        },
      },
      {
        label: (
          <div className="flex items-center gap-2">
            <Pencil size={16} /> Edit
          </div>
        ),
        onClick: () => {},
      },
      {
        label: (
          <div className="flex items-center gap-2">
            <RotateCcw size={16} /> Delete
          </div>
        ),
        onClick: () => {},
      },
    ];

    return (
      <div onClick={(e) => e.stopPropagation()}>
        <TableDropdownMenu row={row} menuItems={menuItems} />
      </div>
    );
  };

  ActionCell.propTypes = {
    row: PropTypes.object,
  };

  const handleRowClick = (row) => {
    if (!row) return;

    const { id, questionDate } = row;

    sessionStorage.setItem("fromBallotList", "true");
    navigate(buildRoute.ballotingWithTimestamp(id, questionDate));
  };

  handleRowClick.propTypes = {
    row: PropTypes.object,
    children: PropTypes.node,
  };

  const columns = ({ t }) => [
    {
      accessorKey: "heading",
      header: t("table:ballotheading"),
      meta: { className: "w-50, py-3" },
      enableSorting: true,
    },
    {
      accessorKey: "referenceMinisterDesignationGroupName",
      header: t("table:group"),
      enableSorting: true,
    },
    {
      accessorKey: "questionDate",
      meta: { className: "text-center" },
      header: t("table:questionDate"),
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      meta: { className: "text-center" },
      header: t("table:ballotDate"),
      enableSorting: true,
    },
    {
      header: t("table:status"),
      meta: { className: "text-center w-[100px] capitalize" },
      accessorKey: "ballotStatus",
      cell: StatusCell,
      enableSorting: true,
    },
    {
      id: "action",
      header: t("table:action"),
      meta: { className: "text-center w-[100px]" },
      cell: ActionCell,
      enableSorting: false,
    },
  ];

  const getFilteredTableData = () => {
    if (!documentsListData?.content || !searchInput.trim()) {
      return documentsListData?.content ?? [];
    }

    return documentsListData.content.filter((item) => {
      return (
        (item.heading &&
          item.heading.toLowerCase().includes(searchInput.toLowerCase())) ||
        (item.referenceMinisterDesignationGroupName &&
          item.referenceMinisterDesignationGroupName
            .toLowerCase()
            .includes(searchInput.toLowerCase()))
      );
    });
  };

  const tableData = () => {
    return getFilteredTableData();
  };

  const getTotalPages = () => {
    return documentsListData?.totalPages ?? 0;
  };

  const getDisplayedPageInfo = () => {
    const totalElements = documentsListData?.totalElements;
    if (!totalElements) return "";

    const startIndex = page * size - (size - 1);
    const endIndex = Math.min(page * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const getTableEmptyLabel = () => {
    if (isDocumentsListLoading) return "Loading...";
    if (isLoading) return "Loading...";
    if (documentsListError) return "Error";
    return "No Data";
  };

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchUpdate = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ searchText: "" });
    } else if (event.key === "Enter") {
      updateParams({ searchText: event.target.value });
    }
  };

  const handlePageChange = (newPage) => updateParams({ page: newPage });

  const handlePageSizeChange = (newSize) =>
    updateParams({ size: newSize, page: 1 });

  const handleFilterChange = (data) => {
    setFilteredData(data);

    const apiParams = processFilters(data);

    const updatedParams = { ...apiParams, page: 1 };

    Object.keys(filteredData).forEach((key) => {
      if (!apiParams[key]) {
        updatedParams[key] = "";
      }
    });

    updateParams(updatedParams);
  };

  const filterOptions = [
    {
      label: t("kla"),
      submenu: [
        {
          comboboxOptions: [
            { label: "15", value: "15" },
            { label: "12", value: "12" },
            { label: "13", value: "13" },
          ],
          label: t("kla"),
          maxCount: 1,
          size: "lg",
          placeholder: "Select KLA",
          value: "assembly",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "assembly",
    },
    {
      label: t("session"),
      submenu: [
        {
          comboboxOptions: [
            { label: "1", value: "1" },
            { label: "2", value: "2" },
            { label: "3", value: "3" },
          ],
          label: t("session"),
          maxCount: 1,
          size: "lg",
          placeholder: "Select Session",
          value: "session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "session",
    },
    {
      label: t("questionDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
    {
      label: t("group"),
      submenu: [
        {
          comboboxOptions: [
            { label: "Group 1", value: "Group 1" },
            { label: "Group 2", value: "Group 2" },
            { label: "Group 3", value: "Group 3" },
            { label: "Group 4", value: "Group 4" },
            { label: "Group 5", value: "Group 5" },
          ],
          maxCount: 5,
          placeholder: "Select Group",
          size: "lg",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "groupName",
    },
    {
      label: t("ballotDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "createdAt",
    },

    {
      label: t("status"),
      submenu: [
        {
          comboboxOptions: [
            { label: "Cancelled", value: "CANCELLED" },
            { label: "Submitted", value: "SUBMITTED" },
          ],
          maxCount: 2,
          placeholder: "Ballot Status",
          size: "lg",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ballotStatus",
    },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const handleClearFilters = () => {
    setFilteredData({});
    setSearchInput("");
    const paramsToReset = {
      assembly: "",
      session: "",
      searchText: "",
      page: "1",
      questionDate: "",
      questionDateStartDate: "",
      questionDateEndDate: "",
      ballotDate: "",
      ballotDateStartDate: "",
      ballotDateEndDate: "",
      createdAt: "",
      createdAtStartDate: "",
      createdAtEndDate: "",
      groupName: "",
      ballotStatus: "",
    };

    updateParams(paramsToReset);
  };

  return (
    <div className="px-5 py-3 bg-background">
      <div className="p-5 bg-background-container relative">
        <div className="flex flex-col gap-4">
          <div className="flex justify-between">
            <h2 className="typography-page-heading">{t("ballotList")}</h2>
            <Button
              onClick={() => navigate(buildRoute.balloting())}
              icon={Plus}
              variant="secondary"
              iconPosition="left"
            >
              {t("perform")}
            </Button>
          </div>
          <div className="flex justify-between items-center">
            <Input
              icon={Search}
              className="w-80"
              reserveErrorSpace={false}
              placeholder={"Search"}
              size="md"
              value={searchInput}
              onChange={handleSearchInputChange}
              onKeyDown={handleSearchUpdate}
            />
            <FilterDropdown
              badgeContainerClassName=""
              defaultValue={filters}
              options={filterOptions}
              onValueChange={handleFilterChange}
              onClearAll={handleClearFilters}
            />
          </div>
          <DataTable
            columns={columns({ t })}
            data={isLoading ? [] : tableData()}
            emptyLabel={isLoading ? <SpinnerLoader /> : getTableEmptyLabel()}
            onRowClick={handleRowClick}
          />
          <div className="flex justify-between">
            <div className="flex gap-4 items-center">
              <span className="typography-body-text-s-14 text-grey-600">
                {getDisplayedPageInfo()}
              </span>
              <PaginationSelect
                onChange={handlePageSizeChange}
                defaultValue={size}
                options={paginationOptions}
                disabled={isLoading}
              />
            </div>
            <div>
              <Paginator
                currentPage={page}
                totalPages={getTotalPages()}
                onPageChange={handlePageChange}
                showPreviousNext={true}
                disabled={isLoading}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BallotList;
