import { useDebounce, useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import {
  useGetDocumentListQuery,
  useLazyGetDocumentByIdQuery,
} from "@/services/documents";
import {
  useGetAssemblyQuery,
  useGetSessionQuery,
} from "../../services/master-data-management/assembly";
import { formatDateWithFallback, formatDateYYYYMMDD } from "@/utils";
import { getTableStatus, getTableVariant } from "@/utils/tag-variants";
import {
  Button,
  DataTable,
  FilterDropdown,
  Input,
  PaginationSelect,
  Paginator,
  TableDropdownMenu,
  TableTag,
  toast,
} from "@kla-v2/ui-components";
import {
  Eye,
  FileClock,
  Pencil,
  PlusIcon,
  RotateCcw,
  Search,
} from "lucide-react";
import PropTypes from "prop-types";
import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import DocumentCreateModal from "./components/document-create-modal";
import documentTypes from "@/utils/document-types/document-types";
import { buildRoute } from "@/config/routes";
import SpinnerLoader from "@/utils/loaders/spinner-loader";

const StatusCell = ({ renderValue }) => {
  return (
    <TableTag
      variant={getTableVariant(renderValue())}
      className={"ml-auto w-max"}
    >
      {getTableStatus(renderValue())}
    </TableTag>
  );
};
StatusCell.propTypes = {
  renderValue: PropTypes.func,
};

function DocumentList() {
  const { t } = useLanguage();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState({});
  const [searchInput, setSearchText] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  useAutoBreadcrumb();

  const page = parseInt(searchParams.get("page") || "0");
  const size = searchParams.get("size") || "10";
  const searchQuery = searchParams.get("search") || "";
  const searchText = useDebounce(searchParams.get("search") || searchInput);
  const [trigger] = useLazyGetDocumentByIdQuery();
  const { data: assemblyData } = useGetAssemblyQuery();
  const { data: sessionData } = useGetSessionQuery();

  const ActionCell = ({ row }) => {
    const { id, type } = row?.original || {};
    const menuItems = [
      {
        label: (
          <div className="flex items-center gap-2">
            <Pencil size={16} /> Edit
          </div>
        ),
        onClick: (e) => {
          e.stopPropagation();
        },
      },
      {
        label: (
          <div className="flex items-center gap-2">
            <RotateCcw size={16} /> Revision
          </div>
        ),
        onClick: (e) => {
          e.stopPropagation();
        },
      },
      {
        label: (
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              const matchedDocument = documentTypes.find(
                (doc) => doc.value === type,
              );

              if (matchedDocument?.route) {
                // Use centralized buildRoute for consistent routing
                if (type === "MINISTER_DESIGNATION_GROUP") {
                  navigate(buildRoute.document(type, id, 'view'));
                } else {
                  navigate(buildRoute.document(type, id));
                }
              } else {
                toast.error("Route Not Found", {
                  description: `Invalid Route For : ${getFormattedDocumentType(
                    type,
                  )}`,
                });
              }
            }}
          >
            <Eye size={16} /> View Details
          </div>
        ),
      },
    ].filter(Boolean);

    return (
      <div onClick={(e) => e.stopPropagation()}>
        <TableDropdownMenu row={row} menuItems={menuItems} />
      </div>
    );
  };
  ActionCell.propTypes = {
    row: PropTypes.object,
  };

  const updateParams = (newParams) => {
    const updatedParams = new URLSearchParams(searchParams);
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        updatedParams.set(key, value.toString());
      } else {
        updatedParams.delete(key);
      }
    });
    setSearchParams(updatedParams);
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage - 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 0 });
  };

  const processFilters = (filters) => {
    const queryParams = {};
    Object.entries(filters).forEach(([key, filterData]) => {
      const { filter, value } = filterData;

      switch (filter) {
        case "documentType": {
          queryParams[key] = value;
          break;
        }
        case "Assembly":
          queryParams["assembly"] =
            typeof value === "object" && value !== null
              ? value.KLA || ""
              : value || "";
          break;
        case "Session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.Session || ""
              : value || "";
          break;
        case "Is":
          queryParams[key] = formatDateYYYYMMDD(value);
          break;
        case "status": {
          const statusValue =
            typeof value === "object" && value !== null ? value.Status : value;
          queryParams["status"] = statusValue;
          break;
        }
        case "between": {
          const { from, to } = value || {};
          if (key === "createdAt" && from && to) {
            queryParams["createdAtStartDate"] = `${formatDateYYYYMMDD(from)}`;
            queryParams["createdAtEndDate"] = `${formatDateYYYYMMDD(to)}`;
          } else if (from && to) {
            queryParams["lastModifiedAtStartDate"] = `${formatDateYYYYMMDD(
              from,
            )}`;
            queryParams["lastModifiedAtEndDate"] = `${formatDateYYYYMMDD(to)}`;
          }

          break;
        }
        default:
          queryParams[key] = value;
      }
    });

    return queryParams;
  };

  const {
    data: documentsListData,
    isFetching: isDocumentsListFetching,
    error: documentsListError,
  } = useGetDocumentListQuery({
    ...processFilters(filteredData),
    searchText: searchText,
    page,
    size,
  });

  const isLoading = isDocumentsListFetching;

  const handleSearch = (e) => {
    setSearchText(e.target.value);
    updateParams({ search: e.target.value, page: 0 });
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  const klaOptions =
    assemblyData?.map((kla) => ({
      label: kla.title,
      value: kla.title,
    })) || [];

  const sessionOptions =
    sessionData?.map((session) => ({
      label: session.title,
      value: session.title,
    })) || [];

  const filterOptions = [
    {
      label: t("table:Assembly"),
      submenu: [
        {
          comboboxOptions: klaOptions,
          label: "Assembly",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "KLA",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Assembly",
    },
    {
      label: t("table:Session"),
      submenu: [
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Session",
          truncateStringLength: 6,
          value: "Session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Session",
    },
    {
      label: t("table:documentType"),
      submenu: [
        {
          comboboxOptions: [
            {
              label: "Minister Designation Group",
              value: "MINISTER_DESIGNATION_GROUP",
            },
            {
              label: "Allotment Of Days",
              value: "ALLOTMENT_OF_DAYS",
            },
            {
              label: "Schedule Of Activity",
              value: "SCHEDULE_OF_ACTIVITY",
            },
            {
              label: "Delayed Answer Bulletin",
              value: "DELAYED_ANSWER_BULLETIN",
            },
            {
              label: "Delay Statement",
              value: "DELAY_STATEMENT_LIST",
            },
            {
              label: "Late Answer",
              value: "LATE_ANSWER",
            },
            {
              label: "Answer Status Report",
              value: "ANSWER_STATUS_REPORT",
            },
          ],
          maxCount: 1,
          placeholder: "Select Document Type",
          size: "lg",
          truncateStringLength: 30,
          value: "documentType",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "documentType",
    },
    {
      label: t("table:createdDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "createdAt",
    },
    {
      label: t("table:approvedDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "lastModifiedAt",
    },
    {
      label: "Status",
      submenu: [
        {
          comboboxOptions: [
            {
              label: "Submitted",
              value: "SUBMITTED",
            },
            {
              label: "Draft in Progress",
              value: "DRAFT_IN_PROGRESS",
            },
            {
              label: "Approved",
              value: "APPROVED",
            },
            {
              label: "Rejected",
              value: "REJECTED",
            },
            {
              label: "Laid",
              value: "LAID",
            },
            {
              label: "Re-Submitted",
              value: "RE_SUBMITTED",
            },
            {
              label: "Return",
              value: "RETURN",
            },
          ],
          icon: FileClock,
          label: "Status",
          maxCount: 1,
          placeholder: "Laid in Assembly",
          size: "lg",
          truncateStringLength: 18,
          value: "Status",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "status",
    },
  ];

  const columns = ({ t }) => [
    {
      accessorKey: "currentNumber",
      header: t("table:currentNo"),
    },
    {
      accessorKey: "name",
      meta: { className: "py-3" },
      header: t("table:documentName"),
    },
    {
      accessorFn: ({ createdAt }) =>
        formatDateWithFallback(createdAt, { fallback: "-" }),
      header: t("table:createdDate"),
    },
    {
      accessorFn: ({ lastModifiedAt }) =>
        formatDateWithFallback(lastModifiedAt, { fallback: "-" }),
      header: t("table:approvedDate"),
    },
    {
      header: t("table:status"),
      meta: { className: "text-right capitalize" },
      accessorKey: "status",
      cell: StatusCell,
    },
    {
      id: "action",
      header: t("table:action"),
      meta: { className: "text-center w-[100px]" },
      cell: ({ row }) => <ActionCell row={row} />,
    },
  ];

  const tableData = () => {
    return documentsListData?.content ?? [];
  };

  const getTotalPages = () => {
    return documentsListData?.totalPages ?? 0;
  };

  const getDisplayedPageInfo = () => {
    const totalElements = documentsListData?.totalElements;
    const page = (documentsListData?.page ?? 0) + 1;
    const size = documentsListData?.size ?? 10;
    if (!totalElements) return "";

    const startIndex = (page - 1) * size + 1;
    const endIndex = Math.min(page * size, totalElements);
    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const paginationOptions = [
    {
      label: "10 per page",
      value: "10",
    },
    {
      label: "50 per page",
      value: "50",
    },
    {
      label: "100 per page",
      value: "100",
    },
  ];

  const getTableEmptyLabel = () => {
    if (isLoading) return "Loading...";
    if (documentsListError) return "Error";
    return "No Data";
  };

  const handleFilterChange = (data) => {
    setFilteredData(data);
    updateParams({ page: 0 });
  };

  return (
    <div className="px-5 py-3 bg-background">
      <div className="p-5 bg-background-container">
        <div className="flex flex-col gap-4">
          <div className="flex justify-between">
            <h1 className="typography-page-heading">{t("allDocuments")}</h1>
            <Button
              variant="secondary"
              icon={PlusIcon}
              iconPosition={"left"}
              onClick={() => setIsOpen(true)}
            >
              {t("create")}
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <Input
              icon={Search}
              className="w-80"
              reserveErrorSpace={false}
              placeholder={"Search"}
              size="md"
              defaultValue={searchQuery}
              onChange={handleSearch}
            />

            <FilterDropdown
              badgeContainerClassName=""
              options={filterOptions}
              onValueChange={handleFilterChange}
            />
          </div>
          <DataTable
            className="p-2"
            columns={columns({ t })}
            data={isLoading ? [] : tableData()}
            emptyLabel={isLoading ? <SpinnerLoader /> : getTableEmptyLabel()}
            onRowClick={(row) => {
              if (!row) return;
              const { type, id } = row;
              const matchedDocument = documentTypes.find(
                (doc) => doc.value === type,
              );
              trigger({ documentId: id }).then(() => {
                if (matchedDocument) {
                  navigate(`${matchedDocument.route}${id}`);
                } else {
                  toast.error("Route Not Found", {
                    description: `Invalid Route For : ${getFormattedDocumentType(
                      type,
                    )}`,
                  });
                }
              });
            }}
          />

          <div className="flex justify-between">
            <div className="flex items-center gap-4">
              <span className="typography-body-text-s-14 text-grey-600">
                {getDisplayedPageInfo()}
              </span>
              <PaginationSelect
                onChange={handlePageSizeChange}
                defaultValue={size}
                options={paginationOptions}
                disabled={isLoading}
              />
            </div>
            <div>
              <Paginator
                currentPage={(documentsListData?.page ?? 0) + 1}
                totalPages={getTotalPages()}
                showPreviousNext={true}
                onPageChange={handlePageChange}
                disabled={isLoading}
              />
            </div>
          </div>
        </div>
      </div>
      {isOpen && <DocumentCreateModal isOpen={isOpen} onClose={closeModal} />}
    </div>
  );
}

export default DocumentList;
