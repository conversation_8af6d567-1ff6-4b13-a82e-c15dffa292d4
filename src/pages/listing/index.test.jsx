import { AppProvider } from "@/app/provider";
import { store } from "@/app/store";
import { documentsApi } from "@/services/documents";
import { render, screen, waitFor } from "@testing-library/react";
import { http, HttpResponse } from "msw";
import PropTypes from "prop-types";
import { MemoryRouter } from "react-router-dom";
import { beforeEach, describe, expect, it } from "vitest";
import DocumentList from ".";
import { server } from "../../../mock/node";

const { VITE_API_BASE_URL } = import.meta.env;

beforeEach(() => {
  store.dispatch(documentsApi.util.resetApiState());
});

function Wrapper({ children }) {
  return (
    <AppProvider>
      <MemoryRouter>{children}</MemoryRouter>
    </AppProvider>
  );
}
Wrapper.propTypes = {
  children: PropTypes.element,
};

describe("Document Listing Component", () => {
  const renderComponent = () => {
    render(<DocumentList />, { wrapper: Wrapper });

    return {
      documentHeading: screen.getByRole("heading", { name: /all documents/i }),
      createButton: screen.getByRole("button", { name: /create/i }),
    };
  };

  it("should renders document list table with heading and buttons", async () => {
    const { documentHeading, createButton } = renderComponent();

    await waitFor(() => {
      expect(documentHeading).toBeInTheDocument();
      expect(createButton).toBeInTheDocument();
    });
  });

  it("should displays table rows based on API data", async () => {
    server.use(
      http.get(VITE_API_BASE_URL + "api/documents/search", () => {
        return HttpResponse.json({
          content: [
            {
              assembly: 12,
              createdAt: "2025-03-15",
              createdBy: "user123",
              currentNumber: 123456,
              id: "8985def7-e6a8-43bb-b564-d3eefe3e29f0",
              lastModifiedAt: "2025-03-15",
              lastModifiedBy: "user123",
              name: "Schedule of activity",
              session: 12,
              status: "DRAFT",
              type: "SCHEDULE_OF_ACTIVITY",
            },
            {
              assembly: 15,
              createdAt: "2025-03-15",
              createdBy: "user123",
              currentNumber: 123457,
              id: "8985def7-e6a8-43bb-b564-8985def73456f",
              lastModifiedAt: "2025-03-15",
              lastModifiedBy: "user123",
              name: "Allotment of days",
              session: 13,
              status: "SUBMITTED",
              type: "ALLOTMENT_OF_DAYS",
            },
          ],
          totalPages: 1,
          totalElements: 1,
        });
      }),
    );

    renderComponent();
    await waitFor(() => {
      expect(screen.getByText("123456")).toBeInTheDocument();
      expect(screen.getByText("Schedule of activity")).toBeInTheDocument();
      expect(screen.getByText("Draft")).toBeInTheDocument();
    });
  });

  it("should updates pagination based on page and size", async () => {
    server.use(
      http.get(VITE_API_BASE_URL + "api/documents/search", () => {
        return HttpResponse.json({
          content: [],
          totalPages: 5,
          totalElements: 50,
        });
      }),
    );

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText("Showing 1 - 10 of 50")).toBeInTheDocument();
    });
  });

  it.todo("should displays loading state", () => {
    renderComponent();

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("should displays error state", async () => {
    server.use(
      http.get(VITE_API_BASE_URL + "api/documents/search", () => {
        return HttpResponse.json(
          {
            type: "https://api.house.kla.com/errors/generic_error",
            title: "Unexpected error occurred",
            status: 500,
            detail: "",
            instance: "uri=/question-service/api/documents",
            properties: {
              stackTrace: [],
            },
          },
          { status: 500 },
        );
      }),
    );

    renderComponent();

    await waitFor(() => {
      expect(screen.queryByText("Error")).toBeInTheDocument();
    });
  });
});
