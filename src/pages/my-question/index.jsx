import {
  DataTable,
  FilterDropdown,
  Input,
  PaginationSelect,
  Paginator,
  TableTag,
} from "@kla-v2/ui-components";
import { Users } from "lucide-react";
import { User } from "lucide-react";
import { useMemo } from "react";
import PropTypes from "prop-types";
import { Star } from "lucide-react";
import { useCallback } from "react";

import { useSearchParams } from "react-router-dom";
import { useActiveAssembly, useLanguage } from "@/hooks";
import { Search } from "lucide-react";
import { useState } from "react";
import { useGetQuestionsListQuery } from "@/services/my-question";
import { getTableStatus, getTableVariant } from "@/utils/tag-variants";
import { useNavigate } from "react-router-dom";
import { buildRoute } from "@/config/routes";

function MyQuestion() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { activeAssembly } = useActiveAssembly();
  const page = parseInt(searchParams.get("page") || "1") - 1;
  const size = parseInt(searchParams.get("size") || "10");
  const [searchInput, setSearchInput] = useState(
    searchParams.get("searchText") || "",
  );

  const [filteredData, setFilteredData] = useState({});

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchUpdate = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ searchText: "" });
    } else if (event.key === "Enter") {
      updateParams({ searchText: event.target.value });
    }
  };

  const filters = useMemo(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      return {
        Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
        Session: { filter: "Session", value: activeAssembly?.session },
      };
    }
    return {};
  }, [activeAssembly?.assembly, activeAssembly?.session]);

  const extractFilterValue = (filterData) => {
    if (!filterData) return null;
    if (Array.isArray(filterData.value)) {
      return filterData.value.length > 0 ? filterData.value : null;
    }

    return filterData.value !== undefined && filterData.value !== ""
      ? filterData.value
      : null;
  };

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      if (!filterData || !filterData.filter) return;

      const { filter } = filterData;
      const value = extractFilterValue(filterData);

      if (value === null) return;

      switch (key) {
        case "assembly":
          queryParams["assembly"] =
            typeof value === "object" && value !== null
              ? value.assembly || ""
              : value || "";
          break;
        case "session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.session || ""
              : value || "";
          break;
        case "groupName":
          if (typeof value === "object" && value !== null) {
            queryParams["groupName"] = value.undefined || "";
          } else if (Array.isArray(value)) {
            queryParams["groupName"] = value[0] || "";
          } else {
            queryParams["groupName"] = value || "";
          }
          break;
        case "ballotStatus":
          if (typeof value === "object" && value !== null) {
            queryParams["ballotStatus"] = value.undefined || "";
          } else if (Array.isArray(value)) {
            queryParams["ballotStatus"] = value[0] || "";
          } else {
            queryParams["ballotStatus"] = value || "";
          }
          break;
        case "questionDate":
          if (filter === "between" && value?.from && value?.to) {
            queryParams["questionDateStartDate"] = value.from;
            queryParams["questionDateEndDate"] = value.to;
          } else if (filter === "Is" && value) {
            queryParams["questionDate"] = value;
          }
          break;
        case "createdAt":
          if (filter === "between" && value?.from && value?.to) {
            queryParams["createdAtStartDate"] = value.from;
            queryParams["createdAtEndDate"] = value.to;
          } else if (filter === "Is" && value) {
            queryParams["createdAt"] = value;
          }
          break;
        default:
          if (Array.isArray(value)) {
            queryParams[key] = value.join(",");
          } else {
            queryParams[key] = value;
          }
      }
    });

    return queryParams;
  };

  const handleFilterChange = (data) => {
    setFilteredData(data);

    const apiParams = processFilters(data);

    const updatedParams = { ...apiParams, page: 1 };

    Object.keys(filteredData).forEach((key) => {
      if (!apiParams[key]) {
        updatedParams[key] = "";
      }
    });

    updateParams(updatedParams);
  };

  const handleClearFilters = () => {
    setFilteredData({});
    setSearchInput("");
    const paramsToReset = {
      assembly: "",
      session: "",
      searchText: "",
      page: "1",
      questionDate: "",
      questionDateStartDate: "",
      questionDateEndDate: "",
      ballotDate: "",
      ballotDateStartDate: "",
      ballotDateEndDate: "",
      createdAt: "",
      createdAtStartDate: "",
      createdAtEndDate: "",
      groupName: "",
      ballotStatus: "",
    };

    updateParams(paramsToReset);
  };
  const CategoryCell = ({ renderValue }) => {
    const value = renderValue();
    return value === "unStarred" ? (
      <Star size={17} />
    ) : (
      <Star size={17} fill="gold" color="gold" />
    );
  };

  CategoryCell.propTypes = {
    renderValue: PropTypes.func,
  };
  const ClubbedCell = ({ renderValue }) => {
    const value = renderValue();
    return value === "Yes" ? <User size={17} /> : <Users size={17} />;
  };

  ClubbedCell.propTypes = {
    renderValue: PropTypes.func,
  };

  const StatusCell = ({ renderValue }) => {
    const status = renderValue();
    return (
      <TableTag variant={getTableVariant(status)} className={"ml-auto w-max"}>
        {getTableStatus(status)}
      </TableTag>
    );
  };

  StatusCell.propTypes = {
    renderValue: PropTypes.func,
  };
  const filterOptions = [
    {
      label: t("kla"),
      submenu: [
        {
          comboboxOptions: [
            { label: "15", value: "15" },
            { label: "12", value: "12" },
            { label: "13", value: "13" },
          ],
          label: t("kla"),
          maxCount: 1,
          size: "lg",
          placeholder: "Select KLA",
          value: "assembly",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "assembly",
    },
    {
      label: t("session"),
      submenu: [
        {
          comboboxOptions: [
            { label: "1", value: "1" },
            { label: "2", value: "2" },
            { label: "3", value: "3" },
          ],
          label: t("session"),
          maxCount: 1,
          size: "lg",
          placeholder: "Select Session",
          value: "session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "session",
    },
    {
      label: t("questionDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
    {
      label: t("group"),
      submenu: [
        {
          comboboxOptions: [
            { label: "Group 1", value: "Group 1" },
            { label: "Group 2", value: "Group 2" },
            { label: "Group 3", value: "Group 3" },
            { label: "Group 4", value: "Group 4" },
            { label: "Group 5", value: "Group 5" },
          ],
          maxCount: 5,
          placeholder: "Select Group",
          size: "lg",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "groupName",
    },
    {
      label: t("ballotDate"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "createdAt",
    },

    {
      label: t("status"),
      submenu: [
        {
          comboboxOptions: [
            { label: "Cancelled", value: "CANCELLED" },
            { label: "Submitted", value: "SUBMITTED" },
          ],
          maxCount: 2,
          placeholder: "Ballot Status",
          size: "lg",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ballotStatus",
    },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const tableColumns = useMemo(
    () => [
      {
        accessorKey: "id",
        header: "Question No",
        meta: { className: "min-w-28" },
      },
      {
        accessorKey: "clubbed",
        header: "Clubbed",
        cell: ClubbedCell,
      },
      {
        accessorKey: "category",
        header: "Category",
        cell: CategoryCell,
      },
      {
        accessorKey: "heading",
        header: "Question Heading",
        meta: { className: "min-w-48" },
      },
      {
        accessorKey: "designation",
        header: "Designation",
      },
      {
        accessorKey: "portfolio",
        header: "Portfolio",
        cell: ({ row }) => {
          const value = row.getValue("portfolio");
          const localValue = row.original.portfolioInLocal;
          return localValue ? `${value} (${localValue})` : value;
        },
      },

      {
        accessorKey: "date",
        header: "Question Date",
        meta: { className: "min-w-36" },
      },

      {
        accessorKey: "status",
        meta: { className: "text-center capitalize" },
        header: "Status",
        cell: StatusCell,
      },
    ],
    [],
  );

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }
        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (Object.prototype.hasOwnProperty.call(newParams, "search")) {
        if (newParams.search === "") {
          updatedParams.delete("search");
        } else {
          updatedParams.set("search", newParams.search);
        }
      }

      Object.entries(newParams).forEach(([key, value]) => {
        if (
          key !== "search" &&
          value !== undefined &&
          value !== null &&
          value !== ""
        ) {
          updatedParams.set(key, value.toString());
        }
      });

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );
  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };
  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  const handleRowClick = (row) => {
    if (!row) return;

    const { id } = row;

    navigate(buildRoute.question(id, 'view'));
  };

  handleRowClick.propTypes = {
    row: PropTypes.object,
    children: PropTypes.node,
  };

  const { data: MyQuestionData } = useGetQuestionsListQuery();

  return (
    <>
      <div className="px-5 py-3 bg-background">
        <div className="bg-background-container relative">
          <div className="flex flex-col gap-4">
            <div className="">
              <h2 className="typography-page-heading py-4 px-2">
                {t("My Questions")}
              </h2>
              <div className="flex justify-between items-center p-2">
                <Input
                  icon={Search}
                  className="w-80"
                  reserveErrorSpace={false}
                  placeholder={"Search"}
                  size="md"
                  value={searchInput}
                  onChange={handleSearchInputChange}
                  onKeyDown={handleSearchUpdate}
                />
                <FilterDropdown
                  badgeContainerClassName=""
                  defaultValue={filters}
                  options={filterOptions}
                  onValueChange={handleFilterChange}
                  onClearAll={handleClearFilters}
                />
              </div>
              <DataTable
                columns={tableColumns}
                data={MyQuestionData ?? []}
                onRowClick={handleRowClick}
              />
              <div className="flex justify-between p-4">
                <div className="flex gap-4 items-center">
                  <span className="typography-body-text-s-14 text-grey-600">
                    TotalPages
                  </span>
                  <PaginationSelect
                    onChange={handlePageSizeChange}
                    defaultValue={size.toString()}
                    options={paginationOptions}
                  />
                </div>
                <div>
                  <Paginator
                    currentPage={page + 1}
                    totalPages="100"
                    onPageChange={(newPage) => handlePageChange(newPage - 1)}
                    showPreviousNext={true}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default MyQuestion;
