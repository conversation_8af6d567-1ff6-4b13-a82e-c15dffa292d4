import { useLanguage } from "@/hooks";
import { formatDateString } from "@/utils/date-utils";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  CalendarPopup,
} from "@kla-v2/ui-components";
import { Calendar as CalendarIcon } from "lucide-react";
import PropTypes from "prop-types";
import { useEffect } from "react";
import { useRef } from "react";
import { useState } from "react";

const ScheduleTable = ({ entries, onEntriesUpdate }) => {
  const { t } = useLanguage();

  const [openPickerId, setOpenPickerId] = useState({ rowId: null, key: null });

  const handleDateChange = (index, field, value) => {
    // Prevent clearing the date when selecting the same date
    if (
      !value ||
      value.toISOString() === new Date(entries[index]?.[field]).toISOString()
    ) {
      toggleDatePicker(null, null);
      return;
    }
    // Ensure entries is always an array
    const updatedEntries = Array.isArray(entries)
      ? entries?.map((entry, i) =>
          i === index ? { ...entry, [field]: value } : entry,
        )
      : [];

    onEntriesUpdate(updatedEntries);
    toggleDatePicker(null, null);
  };

  const toggleDatePicker = (index, field) => {
    setOpenPickerId((prev) =>
      prev.rowId === index && prev.field === field
        ? { rowId: null, field: null }
        : { rowId: index, field },
    );
  };

  const DatePickerCell = ({
    index,
    field,
    value,
    openPickerId,
    toggleDatePicker,
    handleDateChange,
  }) => {
    const totalRows = entries.length;
    const lastField = "answerDateWithoutDelayStatement";
    const isLastColumn = field === lastField;
    // Ref for the CalendarPopup
    const popupRef = useRef(null);

    // Close the date picker when clicking outside
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (popupRef.current && !popupRef.current.contains(event.target)) {
          toggleDatePicker(null, null); // Close picker if clicked outside
        }
      };

      if (openPickerId.rowId === index && openPickerId.field === field) {
        document.addEventListener("mousedown", handleClickOutside);
      }

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, [openPickerId, index, field, toggleDatePicker]);
    return (
      <TableCell className="relative text-typography-body-text-r-15 py-4 px-6">
        {formatDateString(value)}
        <CalendarIcon
          className="inline-block ml-2 text-grey-400 cursor-pointer"
          size={16}
          onClick={() => toggleDatePicker(index, field)}
        />
        {openPickerId.rowId === index && openPickerId.field === field && (
          <div
            ref={popupRef}
            className={`absolute z-[99999] bg-white rounded-md shadow-lg p-3
             ${
               index >= totalRows - 3
                 ? "bottom-full mb-2" // For last 3 rows (bottom)
                 : index < 3
                   ? "top-full mt-2" // For top rows (open below)
                   : "bottom-full mb-2" // For middle rows (open above to avoid overflow)
             }
             ${
               isLastColumn
                 ? "right-0 translate-x-[-8px]" // Fix for last column
                 : "left-0"
             }
             w-[300px] max-w-xs
           `}
          >
            <CalendarPopup
              mode="single"
              selected={value ? new Date(value) : undefined}
              onSelect={(date) => handleDateChange(index, field, date)}
              className="border border-none"
            />
          </div>
        )}
      </TableCell>
    );
  };

  DatePickerCell.propTypes = {
    index: PropTypes.number.isRequired,
    field: PropTypes.string.isRequired,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
    toggleDatePicker: PropTypes.func.isRequired,
    openPickerId: PropTypes.shape({
      rowId: PropTypes.number,
      field: PropTypes.string,
    }).isRequired,
    handleDateChange: PropTypes.func.isRequired,
    setOpenPickerId: PropTypes.func.isRequired,
  };
  return (
    <Table className="overflow-y-hidden">
      <TableHeader className="text-white">
        <TableRow>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "90px" }}
          >
            {t("table:slNo")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "170px" }}
          >
            {t("table:questionDay")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:ballotingDay")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:dateForSendingQuestionsToPress")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:printedQuestionBooklet")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:distributionOfQuestion")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:answerDate")}
          </TableHead>
          <TableHead
            className="typography-body-text-s-12 py-2 px-4"
            style={{ minWidth: "160px" }}
          >
            {t("table:withoutDelayStatement")}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {entries.length > 0 ? (
          entries?.map((entry, index) => (
            <TableRow key={index}>
              <TableCell className="text-typography-body-text-r-15 py-4 px-4">
                {index + 1}
              </TableCell>

              <TableCell className="text-base py-4 px-4">
                {formatDateString(entry.questionDate)}
              </TableCell>

              <DatePickerCell
                index={index}
                field="ballotingDate"
                value={entry.ballotingDate}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
              />
              <DatePickerCell
                index={index}
                field="pressPublishingDate"
                value={entry.pressPublishingDate}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
              />
              <DatePickerCell
                index={index}
                field="bookletReceivedDate"
                value={entry.bookletReceivedDate}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
              />

              <DatePickerCell
                index={index}
                field="bookletDistributionDate"
                value={entry.bookletDistributionDate}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
              />
              <DatePickerCell
                index={index}
                field="answerDate"
                value={entry.answerDate}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
              />
              <DatePickerCell
                index={index}
                field="answerDateWithoutDelayStatement"
                value={entry.answerDateWithoutDelayStatement}
                openPickerId={openPickerId}
                toggleDatePicker={toggleDatePicker}
                handleDateChange={handleDateChange}
                setOpenPickerId={setOpenPickerId}
                totalRows={entries.length}
                lastField="answerDateWithoutDelayStatement"
              />
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={8} className="text-center py-8 px-8">
              No schedule entries available
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

ScheduleTable.propTypes = {
  entries: PropTypes.arrayOf(
    PropTypes.shape({
      questionDate: PropTypes.string,
      ballotingDate: PropTypes.string,
      pressPublishingDate: PropTypes.string,
      bookletReceivedDate: PropTypes.string,
      bookletDistributionDate: PropTypes.string,
      answerDate: PropTypes.string,
      answerDateWithoutDelayStatement: PropTypes.string,
    }),
  ).isRequired,
  onEntriesUpdate: PropTypes.func.isRequired,
};

export default ScheduleTable;
