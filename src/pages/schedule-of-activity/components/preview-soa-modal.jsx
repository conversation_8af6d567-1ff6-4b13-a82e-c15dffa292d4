import { useLanguage } from "@/hooks";
import { AttachToFile } from "@/icons";
import {
  Button,
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { X } from "lucide-react";
import PropTypes from "prop-types";
import { DocumentMetadata } from "../../../components/document-metadata";
import DetailLayout from "@/components/detail-layout";

const PreviewSubmitSOAPopup = ({ isOpen, onClose, children, metaData }) => {
  const { t } = useLanguage();

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("preview")}
              </p>
              <div className="flex flex-row justify-between">
                <p className="typography-sub-title-heading mb-4">
                  {metaData?.name || "Schedule Of Activity"}
                </p>
              </div>
              <div className="h-12">
                <DocumentMetadata documentMetadata={metaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="p-4">
              <DetailLayout name="Details" />
            </div>
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button
                size="sm"
                variant="secondary"
                iconPosition="left"
                icon={X}
              >
                {t("close")}
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                size="sm"
                variant="primary"
                iconPosition="right"
                icon={AttachToFile}
              >
                {t("attachToFile")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

PreviewSubmitSOAPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  metaData: PropTypes.object,
};

export default PreviewSubmitSOAPopup;
