import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button, IconButton, toast } from "@kla-v2/ui-components";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Right, Loader2, Save } from "lucide-react";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import {
  useGetScheduleOfActivityQuery,
  useUpdateScheduleOfActivityMutation,
  useSubmitScheduleOfActivityMutation,
} from "@/services/schedule-of-activity";
import ScheduleTable from "./components/schedule-table";
import { formatDate } from "@/utils/date-utils";
import { DocumentMetadata } from "@/components/document-metadata";
import { File } from "lucide-react";
import { ArrowRightIcon } from "lucide-react";
import AodPopup from "./components/update-schedule-of-activity-popup";
import { UserIcon } from "lucide-react";
import { PanelBottomIcon } from "lucide-react";
import { LayoutGridIcon } from "lucide-react";
import PreviewSubmitSOAPopup from "./components/preview-soa-modal";
import { MinisterGroupCard } from "../allotment-of-days";
import {
  useGetAllotmentOfDaysQuery,
  useGetMinisterGroupsQuery,
} from "@/services/allotment-of-days";
import { useMemo } from "react";
import { useGetMinisterListQuery } from "@/services/master-data-management/minister-list";
import { Calendar } from "lucide-react";

export default function ScheduleOfActivityPage() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { documentId } = useParams();
  const [isOpen, setIsOpen] = useState(false);

  useAutoBreadcrumb();

  const [showOnlyHeader, setShowOnlyHeader] = useState(false);
  const [allotmentData, setAllotmentData] = useState({
    allotments: [],
  });
  const [scheduleData, setScheduleData] = useState({
    entries: [],
    metadata: {
      Assembly: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const onSubmit = async () => {
    try {
      setIsPreviewOpen(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };

  // RTK Query hooks for schedule data
  const {
    data: scheduleResponse,
    isLoading: scheduleLoading,
    error: scheduleError,
  } = useGetScheduleOfActivityQuery(documentId);

  const referredAllotmentOfDays = scheduleResponse?.referredAllotmentOfDaysId;

  const { data: allotmentResponse } = useGetAllotmentOfDaysQuery(
    referredAllotmentOfDays,
  );

  const [updateSchedule, { isLoading: isUpdating }] =
    useUpdateScheduleOfActivityMutation();

  const [submitSchedule, { isLoading: isSubmitting }] =
    useSubmitScheduleOfActivityMutation();
  const ministerDesignationGroupId = useMemo(() => {
    return allotmentResponse?.referredMinisterDesignationGroupId || null;
  }, [allotmentResponse]);
  // Map document metadata and schedule data to component state
  useEffect(() => {
    if (scheduleResponse) {
      setScheduleData({
        entries: scheduleResponse?.scheduleOfActivityEntries || [],
        metadata: {
          Assembly: scheduleResponse?.assembly?.toString() || "",
          session: scheduleResponse?.session?.toString() || "",
          documentType: scheduleResponse?.type || "Schedule of Activity",
          currentNo: scheduleResponse?.currentNumber || "",
          createdOn: formatDate(scheduleResponse?.createdAt) || "",
          createdBy: scheduleResponse?.createdBy || "",
          name: scheduleResponse?.name || "Schedule of Activity",
        },
      });
    } else {
      // Update just the metadata if document data is available
      setScheduleData((prev) => ({
        ...prev,
        metadata: {
          Assembly: scheduleResponse?.assembly?.toString() || "",
          session: scheduleResponse?.session?.toString() || "",
          documentType: scheduleResponse?.type || "Schedule of Activity",
          currentNo: scheduleResponse?.currentNumber || "",
          createdOn: formatDate(scheduleResponse?.createdAt) || "",
          createdBy: scheduleResponse?.createdBy || "",
          name: scheduleResponse?.name || "Schedule of Activity",
        },
      }));
    }
  }, [scheduleResponse]);
  const { data: ministerGroupsResponse } = useGetMinisterGroupsQuery(
    ministerDesignationGroupId,
    {
      skip: !ministerDesignationGroupId,
    },
  );

  const { data: ministerListData } = useGetMinisterListQuery();

  const ministerMap = useMemo(() => {
    return ministerListData
      ? ministerListData.reduce((acc, minister) => {
          acc[minister.id] = minister.displayName;
          return acc;
        }, {})
      : {};
  }, [ministerListData]);
  const groupEntriesMap = useMemo(() => {
    return ministerGroupsResponse
      ? ministerGroupsResponse.groups.reduce((acc, group) => {
          acc[group.id] = group.groupEntries || [];
          return acc;
        }, {})
      : {};
  }, [ministerGroupsResponse]);

  useEffect(() => {
    if (allotmentResponse) {
      const updatedAllotments = allotmentResponse?.allotments?.map(
        (allotment) => ({
          ...allotment,
          ministers: (groupEntriesMap[allotment.groupId] || []).map(
            (minister) => ({
              ...minister,
              displayName: ministerMap[minister.ministerId] || "Unknown",
            }),
          ),
        }),
      );
      const updatedMetadata = {
        kla: allotmentResponse.assembly?.toString() || "15",
        session: allotmentResponse.session?.toString() || "",
        documentType: "Schedule Of Activity",
        currentNo: allotmentResponse.currentNumber || "",
        createdOn: allotmentResponse.createdAt
          ? new Date(allotmentResponse.createdAt).toLocaleDateString("en-GB")
          : "",
        createdBy: allotmentResponse.createdBy || "",
        name: allotmentResponse.name || "Schedule Of Activity",
      };

      setAllotmentData((prevState) => ({
        ...prevState,
        allotments: updatedAllotments,
        metadata: updatedMetadata,
      }));
    }
  }, [allotmentResponse, groupEntriesMap, ministerMap]);

  const handleSave = async () => {
    try {
      await updateSchedule({
        id: documentId,
        data: {
          scheduleOfActivityEntries: scheduleData.entries,
        },
      }).unwrap();

      toast.success("Schedule of activity saved successfully");
    } catch (error) {
      console.error("Error saving schedule data:", error);
      toast.error("Failed to save schedule of activity");
    }
  };

  const handleSubmit = async () => {
    try {
      await submitSchedule(documentId).unwrap();
      toast.success("Schedule of activity submitted successfully");
    } catch (error) {
      console.error("Error submitting schedule:", error);
      toast.error("Failed to submit schedule of activity");
    }
  };

  const goBack = () => {
    navigate(-1);
  };

  const handleEntryUpdate = (updatedEntries) => {
    setScheduleData((prev) => ({
      ...prev,
      entries: updatedEntries,
    }));
  };

  const isLoading = scheduleLoading && !scheduleData.metadata.name;
  const hasError = scheduleError;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="typograpghy-body-text-r-14">Loading schedule data...</p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-6 bg-destructive/10 rounded-lg max-w-md text-center">
          <h2 className="text-xl font-bold text-destructive mb-2">
            Error Loading Data
          </h2>
          <p className="mb-4">
            Unable to load schedule of activity data. Please try again.
          </p>
          <Button onClick={goBack} variant="neutral" size="lg">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-background min-h-screen">
      <div className="px-5 py-3">
        <div className="p-5 rounded-md">
          <div className="flex justify-between mb-2 items-center">
            <h1 className="typography-page-heading uppercase">
              {scheduleData.metadata.name}
            </h1>
            <Button
              variant="neutral"
              icon={File}
              iconPosition="right"
              className=""
              type="button"
              size="sm"
              onClick={() => onSubmit()}
            >
              {t("preview")}
            </Button>
          </div>

          <DocumentMetadata documentMetadata={scheduleData.metadata} />
          {/* This Button will remove  after mock / API complete */}
          <div className="bg-background-container py-6 px-12 z-10">
            <Button
              size="sm"
              onClick={() => setShowOnlyHeader((prev) => !prev)}
              className="mb-3"
            >
              Toggle Header Mode
            </Button>
            <div className="space-y-4">
              <div className="flex justify-between items-center border-b pb-2">
                <div className="flex items-center gap-2">
                  <h2 className="text-base font-semibold text-grey-700">
                    Schedule of Activity
                  </h2>

                  {!showOnlyHeader && (
                    <Button
                      variant="ghost"
                      icon={Calendar}
                      iconPosition="right"
                      className="gap-1 text-info text-sm h-7 w-auto px-3 bg-primary-tint-90 border-none rounded-md hover:bg-primary-tint-90 hover:text-primary-tint-10"
                      onClick={() => setIsPreviewOpen(true)}
                    >
                      <span className="typography-body-text-s-14">
                        {t("approvedCOS")}
                      </span>
                    </Button>
                  )}
                </div>

                {showOnlyHeader ? (
                  <Button
                    variant="ghost"
                    icon={Calendar}
                    iconPosition="right"
                    className="gap-1 text-info text-sm h-7 w-auto px-3 bg-primary-tint-90 border-none rounded-md hover:bg-primary-tint-90 hover:text-primary-tint-10"
                    onClick={() => setIsPreviewOpen(true)}
                  >
                    <span className="typography-body-text-s-14">
                      {t("approvedCOS")}
                    </span>
                  </Button>
                ) : (
                  <div className="flex items-center gap-2">
                    <IconButton
                      variant="textInfo"
                      className="w-6 h-6 p-0 rounded-none text-primary"
                      aria-label="Panel Bottom"
                      icon={PanelBottomIcon}
                    />
                    <IconButton
                      variant="textInfo"
                      className="w-6 h-6 p-0 rounded-none"
                      aria-label="Layout Grid"
                      icon={LayoutGridIcon}
                    />
                  </div>
                )}
              </div>

              {!showOnlyHeader && (
                <div className="flex justify-between items-start">
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      {/* //TODO:This hard code value changes after api connection */}
                      <h3 className="typograpgh-body-text-s-18  text-grey-700">
                        Updates from Allotment Of Days
                      </h3>
                    </div>
                    <p className="typograpgh-body-text-r-12 text-grey-400">
                      Auto update on 12-10-2024, 2:00 am{" "}
                      <span className="typography-body-text-m-12 text-black">
                        Question Section Assistant
                      </span>
                      <UserIcon
                        size={12}
                        className="inline-block ml-1 text-grey-400"
                      />
                    </p>
                  </div>

                  <Button variant="secondary" onClick={() => setIsOpen(true)}>
                    {t("applyUpdate")}
                    <ArrowRightIcon className="ml-1" size={14} />
                  </Button>
                </div>
              )}

              {/* Optional Modal */}
              <AodPopup isOpen={isOpen} onClose={() => setIsOpen(false)} />
            </div>

            <div className="space-y-4">
              <ScheduleTable
                entries={scheduleData.entries}
                onEntriesUpdate={handleEntryUpdate}
              />
            </div>
          </div>
          <div className="flex justify-end mt-6">
            <div className="flex gap-2">
              <Button variant="neutral" size="lg" onClick={goBack}>
                <ArrowLeft size={24} />
                <span>{t("back")}</span>
              </Button>
              <Button
                variant="secondary"
                size="lg"
                onClick={handleSave}
                disabled={isUpdating || isSubmitting}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("saving")}
                  </>
                ) : (
                  <>
                    <Save size={24} />
                    <span>{t("save")}</span>
                  </>
                )}
              </Button>
              <Button
                variant="primary"
                size="lg"
                onClick={handleSubmit}
                disabled={isUpdating || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("submitting")}
                  </>
                ) : (
                  <>
                    <span>{t("submit")}</span>
                    <ArrowRight size={24} />
                  </>
                )}
              </Button>
            </div>
          </div>

          <PreviewSubmitSOAPopup
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            documentId={documentId}
            metaData={scheduleData.metadata}
          >
            <div className="space-y-2">
              {allotmentData?.allotments?.map((group) => (
                <MinisterGroupCard
                  key={group.groupId}
                  group={{
                    id: group.groupId,
                    name: group.groupName,
                    ministers: group.ministers || [],
                    dates: group.dates || [],
                  }}
                />
              ))}
            </div>
          </PreviewSubmitSOAPopup>
        </div>
      </div>
    </div>
  );
}
