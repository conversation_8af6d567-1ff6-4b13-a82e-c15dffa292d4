import { DocumentMetadata } from "@/components/document-metadata";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import {
  Button,
  ExpandableAccordion,
  ExpandableItem,
} from "@kla-v2/ui-components";
import { ArrowLeftIcon, File, Info, RotateCcw, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { BasicDetailsSection } from "./basic-details";
import { ReportsSection } from "./reports-details";
import { PaperclipIcon } from "lucide-react";
import { AnswerStatusProvider } from "./answer-context";

export default function AnswerStatusReport() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { documentId } = useParams();
  const [accordionValue, setAccordionValue] = useState([]);
  const [currentDocumentId, setCurrentDocumentId] = useState(documentId);

  useAutoBreadcrumb();

  useEffect(() => {
    if (documentId && documentId !== currentDocumentId) {
      setCurrentDocumentId(documentId);
    }
  }, [documentId, currentDocumentId]);

  const documentMetadata = {
    delayedAnswers: [],
    metadata: {
      Assembly: "15th",
      documentType: "Answer Status Report",
      currentNo: "1165/2024",
      createdBy: "QNA&A Section",
      name: "Answer Status Report for 12th session",
    },
  };

  const goBack = () => {
    navigate(-1);
  };

  return (
    <div className="flex flex-col bg-background min-h-screen">
      <div className="px-5 py-3 flex-1 flex flex-col">
        <div className="p-5 rounded-md">
          <div className="mb-4 flex justify-between items-center">
            <h1 className="typography-page-heading">
              {documentMetadata.metadata.name}
            </h1>
            <div className="flex gap-2">
              <Button
                icon={RotateCcw}
                iconPosition="right"
                size="icon"
                variant="neutral"
                onClick={() => {}}
                className="size-9"
              />
              <Button
                variant="neutral"
                icon={Info}
                iconPosition="right"
                className="h-9"
                type="button"
                size="sm"
              >
                Rule-47(2)
              </Button>
              <Button
                variant="neutral"
                icon={File}
                iconPosition="right"
                className="h-9"
                type="button"
                size="sm"
              >
                Preview
              </Button>
            </div>
          </div>

          <DocumentMetadata documentMetadata={documentMetadata.metadata} />
          <AnswerStatusProvider>
            {/* Accordion Section */}
            <div className="mt-2">
              <ExpandableAccordion
                type="multiple"
                value={accordionValue}
                onValueChange={setAccordionValue}
                className="w-full flex flex-col gap-4"
              >
                <ExpandableItem value="item-1">
                  <BasicDetailsSection
                    accordionOrderNo={1}
                    id={currentDocumentId}
                  />
                </ExpandableItem>
                <ExpandableItem value="item-2">
                  <ReportsSection accordionOrderNo={2} />
                </ExpandableItem>
              </ExpandableAccordion>
            </div>
          </AnswerStatusProvider>
        </div>
        {/* Footer Buttons */}
        <div className="mt-auto pt-4">
          <div className="flex justify-end">
            <div className="flex gap-2">
              <Button
                icon={ArrowLeftIcon}
                iconPosition="left"
                size="lg"
                variant="neutral"
                onClick={goBack}
              >
                {t("back")}
              </Button>
              <Button
                iconPosition="left"
                size="lg"
                variant="secondary"
                onClick={goBack}
              >
                <Save size={16} />
                <span>{t("save")}</span>
              </Button>
              <Button
                icon={PaperclipIcon}
                iconPosition="right"
                size="lg"
                variant="primary"
                onClick={() => {}}
              >
                {t("Attach to file")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
