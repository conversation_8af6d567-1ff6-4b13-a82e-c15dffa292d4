import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import PropTypes from "prop-types";
import {
  DataTable,
  TableTag,
  Input,
  FilterDropdown,
  PaginationSelect,
  Paginator,
} from "@kla-v2/ui-components";
import { Search, FileClock } from "lucide-react";

import { formatDateYYYYMMDD } from "@/utils";
import { useLanguage, useDebounce } from "@/hooks";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useGetSettingOfQuestionsUnstarredQuery } from "@/services/setting-of-question";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";
import {
  useGetAssemblyQuery,
  useGetSessionQuery,
} from "@/services/master-data-management/assembly";

const StatusCell = ({ renderValue }) => {
  const status = renderValue();

  const statusMap = {
    ACTION_TO_BE_TAKEN: (
      <TableTag variant="return" className="w-max">
        Action to be taken
      </TableTag>
    ),
    COMPLETED: (
      <TableTag variant="approved" className="w-max">
        Completed
      </TableTag>
    ),
  };

  return statusMap[status] || null;
};

StatusCell.propTypes = {
  renderValue: PropTypes.func,
};

function ActionToBeTakenUnstarred() {
  const { t } = useLanguage();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState({});
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || "",
  );
  const debouncedSearchValue = useDebounce(searchInput, 500);

  const page = parseInt(searchParams.get("page") || "1") - 1;
  const size = parseInt(searchParams.get("size") || "10");
  const searchQuery = searchParams.get("search") || "";
  const activeAssembly = useSelector(selectActiveAssemblyItems);
  const { data: assemblyData } = useGetAssemblyQuery();
  const { data: sessionData } = useGetSessionQuery();

  const filters = useMemo(() => {
    if (activeAssembly?.assembly && activeAssembly?.session) {
      return {
        Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
        Session: { filter: "Session", value: activeAssembly?.session },
      };
    }
    return {};
  }, [activeAssembly?.assembly, activeAssembly?.session]);

  const filteredDataRef = useRef(filteredData);

  useEffect(() => {
    filteredDataRef.current = filteredData;
  }, [filteredData]);

  useEffect(() => {
    if (
      Object.keys(filters).length > 0 &&
      Object.keys(filteredDataRef.current).length === 0
    ) {
      setFilteredData(filters);
    }
  }, [filters]);

  const extractFiltersFromParams = () => {
    const params = {};
    for (const [key, value] of searchParams.entries()) {
      if (!["page", "size", "search"].includes(key)) {
        params[key] = value;
      }
    }
    return params;
  };

  const filterParams = extractFiltersFromParams();

  const processFilters = (filters) => {
    const queryParams = {};

    // Always filter by ACTION_TO_BE_TAKEN
    queryParams["status"] = "ACTION_TO_BE_TAKEN";

    Object.entries(filters).forEach(([key, filterData]) => {
      const { filter, value } = filterData;

      switch (key) {
        case "category":
          queryParams["category"] =
            typeof value === "object" && value !== null
              ? value.category || ""
              : value || "";
          break;
        case "commentsPending":
          queryParams["commentsPending"] =
            typeof value === "object" && value !== null
              ? value.commentsPending || ""
              : value || "";
          break;
        case "status":
          queryParams["status"] = "ACTION_TO_BE_TAKEN";
          break;
        case "Assembly":
          queryParams["kla"] =
            typeof value === "object" && value !== null
              ? value.Assembly || ""
              : value || "";
          break;
        case "Session":
          queryParams["session"] =
            typeof value === "object" && value !== null
              ? value.Session || ""
              : value || "";
          break;
        case "questionDate":
          handleDateFilter(queryParams, filter, value, "questionDate");
          break;
        default:
          break;
      }
    });

    return queryParams;
  };

  const handleDateFilter = (queryParams, filter, value, dateType) => {
    if (filter === "Is" && value) {
      queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value);
      queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value);
    } else if (filter === "between" && value) {
      if (value.from) {
        queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value.from);
      }
      if (value.to) {
        queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value.to);
      }
    }
  };

  const { data: questionsData, isLoading: questionsLoading } =
    useGetSettingOfQuestionsUnstarredQuery({
      search: searchQuery,
      ...processFilters(filteredData),
      page,
      size,
      ...filterParams,
    });

  const mapQuestionsData = useCallback((data) => {
    return (
      data?.content?.map((item) => ({
        id: item.id,
        questionDate: item.questionDate,
        category: item.category,
        commentsSubmittedBy: item.commentsSubmittedBy,
        commentsPending: item.commentsPending,
        status: item.status,
      })) || []
    );
  }, []);

  const tableData = mapQuestionsData(questionsData);

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }
        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (Object.prototype.hasOwnProperty.call(newParams, "search")) {
        if (newParams.search === "") {
          updatedParams.delete("search");
        } else {
          updatedParams.set("search", newParams.search);
        }
      }

      Object.entries(newParams).forEach(([key, value]) => {
        if (
          key !== "search" &&
          value !== undefined &&
          value !== null &&
          value !== ""
        ) {
          updatedParams.set(key, value.toString());
        }
      });

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchKeyDown = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ search: "" });
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      updateParams({ search: debouncedSearchValue });
    }
  }, [debouncedSearchValue, searchQuery, updateParams]);

  const getDisplayedPageInfo = () => {
    const totalElements = questionsData?.totalElements;
    if (!totalElements) return "";

    const startIndex = (page + 1) * size - (size - 1);
    const endIndex = Math.min((page + 1) * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const getFilterOptions = () => {
    const categoryOptions = [{ label: "Unstarred", value: "Unstarred" }];

    const commentsPendingOptions = [
      { label: "Yes", value: "Yes" },
      { label: "No", value: "No" },
    ];

    const statusOptions = [
      { label: "Action to be taken", value: "ACTION_TO_BE_TAKEN" },
    ];

    const klaOptions =
      assemblyData?.map((kla) => ({
        label: kla.title,
        value: kla.title,
      })) || [];

    const sessionOptions =
      sessionData?.map((session) => ({
        label: session.title,
        value: session.title,
      })) || [];

    return {
      categoryOptions,
      commentsPendingOptions,
      statusOptions,
      klaOptions,
      sessionOptions,
    };
  };

  const {
    categoryOptions,
    commentsPendingOptions,
    statusOptions,
    klaOptions,
    sessionOptions,
  } = getFilterOptions();

  const tableColumns = [
    {
      accessorKey: "questionDate",
      header: "Question Date",
      meta: { className: "min-w-36" },
    },
    {
      accessorKey: "category",
      header: "Category",
    },
    {
      accessorKey: "commentsSubmittedBy",
      header: "Comments Submitted By",
      cell: ({ row }) => {
        const comments = row.original.commentsSubmittedBy;
        return (
          <div className="flex flex-col gap-3 py-4">
            {comments?.map((person, index) => (
              <div key={index}>
                <span className="font-medium">{person.displayName}</span>
                <span className="ml-3 text-gray-400">{person.role}</span>
              </div>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "commentsPending",
      header: "Comments Pending",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: StatusCell,
    },
  ];

  const questionFilterOptions = [
    {
      label: t("table:Assembly"),
      submenu: [
        {
          comboboxOptions: klaOptions,
          label: "Assembly",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "KLA",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Assembly",
    },
    {
      label: t("table:Session"),
      submenu: [
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Session",
          truncateStringLength: 6,
          value: "Session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Session",
    },
    {
      label: "Question Date",
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
    {
      label: t("Category"),
      submenu: [
        {
          comboboxOptions: categoryOptions,
          label: "Category",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Category",
          truncateStringLength: 6,
          value: "category",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "category",
    },
    {
      label: "Comments Pending",
      submenu: [
        {
          comboboxOptions: commentsPendingOptions,
          label: "Comments Pending",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Status",
          truncateStringLength: 6,
          value: "commentsPending",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "commentsPending",
    },
    {
      label: "Status",
      submenu: [
        {
          comboboxOptions: statusOptions,
          icon: FileClock,
          label: "Status",
          maxCount: 1,
          placeholder: "Select Status",
          size: "lg",
          truncateStringLength: 6,
          value: "Status",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "status",
    },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const renderSearch = () => (
    <div className="relative w-80">
      <Input
        icon={Search}
        className="w-full"
        reserveErrorSpace={false}
        placeholder="Search"
        size="md"
        value={searchInput}
        onChange={handleSearchInputChange}
        onKeyDown={handleSearchKeyDown}
      />
    </div>
  );

  const renderFilter = () => (
    <div className="flex gap-2">
      <FilterDropdown
        badgeContainerClassName=""
        options={questionFilterOptions}
        onValueChange={(data) => {
          setFilteredData(data);
        }}
        disabled={questionsLoading}
        defaultValue={filters}
      />
    </div>
  );

  const renderTableContent = () => {
    if (questionsLoading) {
      return <SpinnerLoader />;
    }

    return (
      <>
        <DataTable columns={tableColumns} data={tableData} />

        <div className="flex justify-between mt-4">
          <div className="flex items-center gap-4">
            <span className="typography-body-text-s-14 text-grey-600">
              {getDisplayedPageInfo()}
            </span>
            <PaginationSelect
              onChange={handlePageSizeChange}
              defaultValue={size.toString()}
              options={paginationOptions}
            />
          </div>
          <div>
            <Paginator
              currentPage={page + 1}
              totalPages={questionsData?.totalPages || 0}
              onPageChange={(newPage) => handlePageChange(newPage - 1)}
              showPreviousNext={true}
            />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div className="flex items-center justify-between mt-4">
        {renderSearch()}
        {renderFilter()}
      </div>

      <div className="mt-4">{renderTableContent()}</div>
    </>
  );
}

export default ActionToBeTakenUnstarred;
