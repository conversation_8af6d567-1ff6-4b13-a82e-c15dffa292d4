import { NavLink, Outlet } from "react-router-dom";
import { Tabs, TabsList, TabsTrigger } from "@kla-v2/ui-components";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { ROUTE_CONFIG } from "@/config/routes";

function SettingOfQuestionList() {
  useAutoBreadcrumb();

  return (
    <div className="px-4 py-3 m-4 space-y-3 bg-white rounded-md">
      <div className="flex items-center justify-between">
        <h2 className="typography-page-heading">Setting of Question</h2>
      </div>

      <div>
        <Tabs defaultValue="tab1">
          <TabsList variant="segmented">
            <NavLink to={ROUTE_CONFIG.SECTION.SETTING_UNSTARRED_QUESTIONS_LIST}>
              <TabsTrigger value="tab1" variant="segmented">
                Action to be taken
              </TabsTrigger>
            </NavLink>
            <NavLink to={ROUTE_CONFIG.SECTION.SETTING_UNSTARRED_QUESTIONS_ALL}>
              <TabsTrigger value="tab2" variant="segmented">
                All
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        <Outlet />
      </div>
    </div>
  );
}

export default SettingOfQuestionList;
