import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>yNote, Pencil, X, ArrowRight, User } from "lucide-react";
import { <PERSON><PERSON>, IconButton } from "@kla-v2/ui-components";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@kla-v2/ui-components";

import { useGetDocumentByIdQuery } from "@/services/documents";
import { useGetMinisterListQuery } from "@/services/master-data-management/minister-list";
import {
  useApplyUpdatesToMinisterDesignationGroupMutation,
  useCheckUpdatesAvailableQuery,
} from "@/services/minister-designation-group";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { formatDate, formatDateTimeAMPM } from "@/utils";
import { useLanguage } from "@/hooks";

import { DocumentMetadata } from "@/components/document-metadata";
import { GroupBadge } from "@/components/group-badge";

const ViewMDGDetailsPage = () => {
  const navigate = useNavigate();
  const { documentId } = useParams();
  const { t } = useLanguage();

  const {
    data: documentData,
    isLoading: isDocumentLoading,
    error: isDocumentError,
  } = useGetDocumentByIdQuery({ documentId });

  const { data: ministerListData } = useGetMinisterListQuery();

  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [documentMetaData, setDocumentMetaData] = useState({
    entries: [],
    metadata: {
      kla: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });

  useAutoBreadcrumb();

  useEffect(() => {
    if (documentData) {
      setDocumentMetaData({
        entries: documentMetaData.entries || [],
        metadata: {
          kla: documentData.assembly?.toString() || "",
          session: documentData.session?.toString() || "",
          documentType: documentData.type || "Minister Designation Group",
          currentNo: documentData.currentNumber || "",
          createdOn: formatDate(documentData.createdAt) || "",
          createdBy: documentData.createdBy || "",
          name: documentData.name || "Minister Designation Group",
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentData]);

  // Function to get minister details by ID
  const getMinisterById = (id) => {
    return ministerListData?.find((minister) => minister.id === id);
  };

  // fetch update status
  const { data: updatesStatus } = useCheckUpdatesAvailableQuery(documentId, {
    skip: !documentId,
  });

  const [applyUpdates] = useApplyUpdatesToMinisterDesignationGroupMutation();

  const handleApplyUpdate = async () => {
    try {
      const response = await applyUpdates(documentId).unwrap();

      navigate(
        `/section/documents/minister-designation-group/${response.id}?applyUpdate=true`,
        {
          replace: true,
        },
      );
    } catch (error) {
      console.error("Update apply failed", error);
    }
  };

  if (isDocumentLoading) return SpinnerLoader;

  if (isDocumentError) {
    return <div>{t("dataLoadError")}</div>;
  }

  return (
    <div className="min-h-screen p-6 mb-12">
      <div className="flex items-center justify-between my-4">
        <h1 className="typography-page-heading">
          {documentMetaData.metadata.name}
        </h1>
        <div className="flex items-center gap-2">
          <IconButton
            icon={Pencil}
            size="sm"
            variant="secondary"
            onClick={() => {}}
          />
          <Button
            icon={StickyNote}
            iconPosition="right"
            size="md"
            variant="neutral"
            onClick={() => {}}
          >
            {t("preview")}
          </Button>
        </div>
      </div>

      <DocumentMetadata documentMetadata={documentMetaData.metadata} />

      <div className="p-4 mt-2 space-y-4 bg-white rounded-xl">
        {updatesStatus?.updatesAvailable && (
          <div className="flex items-center justify-between">
            <div>
              <h2 className="mb-1 text-grey-600 typography-body-text-s-18">
                {t("ministerProfileUpdatesSectionTitle")}
              </h2>
              <p className="flex items-center gap-1 typography-body-text-r-12 text-grey-600">
                {t("autoUpdateOn")}{" "}
                {formatDateTimeAMPM(updatesStatus?.lastModifiedAt)}
                <span className="text-black typography-body-text-m-12">
                  {updatesStatus?.lastModifiedBy}
                </span>
                <User className="w-4 h-4" />
              </p>
            </div>
            <Button
              icon={ArrowRight}
              iconPosition="right"
              size="lg"
              variant="secondary"
              onClick={handleApplyUpdate}
            >
              {t("applyUpdates")}
            </Button>
          </div>
        )}

        <div className="flex items-center justify-between mb-4">
          <h3 className="text-grey-600 typography-page-heading">
            {t("details")}
          </h3>
        </div>
        <div className="space-y-4">
          {documentData?.groups?.map((group) => (
            <div key={group.id} className="p-4 bg-white border rounded-lg">
              <div className="mb-4">
                <GroupBadge
                  groupNumber={group.groupName.replace("Group ", "")}
                />
              </div>

              <div className="space-y-3">
                {group.groupEntries.map((entry) => {
                  const minister = getMinisterById(entry.ministerId);
                  if (!minister) return null;

                  return (
                    <div
                      key={entry.id}
                      className="flex items-center w-full gap-3"
                    >
                      <img
                        src={`/mdm-service/api/minister/${minister.id}/photo`}
                        alt={minister.user.displayName}
                        className="object-cover rounded-full w-9 h-9"
                      />

                      <div className="flex items-center gap-1">
                        <h3 className="typography-body-text-m-16">
                          {minister.primaryDesignation.title}
                        </h3>
                        <p className="typography-body-text-m-14 text-grey-400">
                          {minister.user.displayName}
                        </p>
                      </div>
                    </div>
                  );
                })}

                {group.groupEntries.length === 0 && (
                  <div className="p-4 text-center text-gray-500">
                    No ministers in this group
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="fixed bottom-0 left-0 right-0 flex justify-end p-3 bg-white border-t">
        <Button
          icon={X}
          iconPosition="right"
          size="lg"
          variant="primary"
          onClick={() => window.history.back()}
        >
          {t("close")}
        </Button>
      </div>
      {showUpdateDialog && (
        <Dialog open={showUpdateDialog} onOpenChange={setShowUpdateDialog}>
          <DialogContent>
            <DialogTitle>{t("updatesAvailableAlertText")}</DialogTitle>
            <DialogDescription>
              {t("ministerProfileUpdateAlert")}
            </DialogDescription>
            <DialogFooter>
              <Button
                variant="primary"
                onClick={() => setShowUpdateDialog(false)}
              >
                {t("close")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default ViewMDGDetailsPage;
