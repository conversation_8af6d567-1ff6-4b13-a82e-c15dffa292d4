import { useState } from "react";
import PropTypes from "prop-types";
import { Search } from "lucide-react";
import { Input } from "@kla-v2/ui-components";
import { MinistersAdded } from "@/icons";
import MicroMinisterCard from "./micro-minister-card";

export default function MinisterList({ ministers, onAddToGroup }) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredMinisters = ministers.filter(
    (minister) =>
      minister.user?.displayName
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      minister.primaryDesignation.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="w-[384px] flex flex-col h-full p-4 bg-white rounded-lg shadow">
      <div className="flex-shrink-0 mb-4">
        <div className="flex flex-col mb-4">
          <h2 className="typography-sub-title-heading">Ministers</h2>
          <p className="text-grey-500 typography-body-text-r-14">
            Drag and drop ministers into groups
          </p>
        </div>

        <Input
          id="search"
          name="search"
          placeholder="Search Ministers"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          icon={Search}
          reserveErrorSpace={false}
        />
      </div>

      <div className="flex-1 overflow-hidden">
        <div className="h-full pr-1 space-y-2 overflow-y-auto">
          {filteredMinisters.length > 0 ? (
            filteredMinisters.map((minister) => (
              <MicroMinisterCard
                key={minister.id}
                minister={minister}
                onAddToGroup={() => onAddToGroup(minister)}
                showAddButton
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-grey-600">
              <MinistersAdded />
              <p>All Ministers are assigned.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

MinisterList.propTypes = {
  ministers: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      displayName: PropTypes.string.isRequired,
      primaryDesignation: PropTypes.shape({
        title: PropTypes.string.isRequired,
      }).isRequired,
    }),
  ).isRequired,
  onAddToGroup: PropTypes.func.isRequired,
};
