import PropTypes from "prop-types";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { AttachToFile } from "@/icons";
import { Pencil } from "lucide-react";
import { DocumentMetadata } from "@/components/document-metadata";
import { GroupBadge } from "@/components/group-badge";

export default function PreviewDialog({
  groups,
  onClose,
  onConfirm,
  documentMetadata,
  isOpen,
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="w-[1140px] max-w-[80vw]">
          <DialogHeader>
            <DialogTitle>Preview & Attach to File</DialogTitle>
            <DialogDescription>
              <div className="flex items-center justify-between my-4">
                <h3 className="text-lg">{documentMetadata.name}</h3>
              </div>

              <DocumentMetadata documentMetadata={documentMetadata} />
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 bg-background">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium">Details</h3>
            </div>

            <div className="overflow-y-auto max-h-[50vh] space-y-4">
              {groups.map((group) => (
                <div key={group.id} className="p-4 bg-white border rounded-lg">
                  <div className="mb-4">
                    <GroupBadge
                      groupNumber={group.groupName.replace("Group ", "")}
                    />
                  </div>

                  <div className="space-y-4">
                    {group.ministers.map((minister) => (
                      <div
                        key={minister.id}
                        className="flex items-center gap-3"
                      >
                        <div className="flex-shrink-0">
                          <img
                            src={`/mdm-service/api/minister/${minister.id}/photo`}
                            alt={minister.user.displayName}
                            className="object-cover w-10 h-10 rounded-full"
                          />
                        </div>
                        <div>
                          <div className="truncate typography-body-text-m-12 text-grey-600">
                            {minister.primaryDesignation.title}
                          </div>
                          <div className="truncate typography-body-text-m-16">
                            {minister.user.displayName}
                          </div>
                        </div>
                      </div>
                    ))}

                    {group.ministers.length === 0 && (
                      <div className="p-4 text-center text-gray-500">
                        No ministers in this group
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                icon={Pencil}
                iconPosition="left"
                size="lg"
                variant="secondary"
              >
                Edit
              </Button>
            </DialogClose>
            <Button
              icon={AttachToFile}
              iconPosition="right"
              size="lg"
              variant="primary"
              onClick={onConfirm}
            >
              Attach To File
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

PreviewDialog.propTypes = {
  groups: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      groupName: PropTypes.string.isRequired,
      ministers: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          title: PropTypes.string,
          image: PropTypes.string,
        }),
      ).isRequired,
    }),
  ).isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  documentMetadata: PropTypes.shape({
    name: PropTypes.string.isRequired,
  }).isRequired,
  isOpen: PropTypes.bool.isRequired,
};
