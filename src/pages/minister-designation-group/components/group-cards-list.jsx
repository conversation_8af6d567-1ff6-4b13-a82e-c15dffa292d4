import PropTypes from "prop-types";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import GroupCard from "./group-card";

export default function GroupCardsList({
  groups,
  onRemoveMinister = () => {},
  onRemoveGroup = () => {},
  loadingGroupId,
}) {
  const groupIds = groups.map((g) => g.id);

  return (
    <SortableContext items={groupIds} strategy={verticalListSortingStrategy}>
      <div className="h-full overflow-y-auto">
        {groups.map((group) => (
          <GroupCard
            key={group.id}
            group={group}
            onRemoveMinister={onRemoveMinister}
            onRemoveGroup={onRemoveGroup}
            isSaving={loadingGroupId === group.id}
          />
        ))}
      </div>
    </SortableContext>
  );
}

GroupCardsList.propTypes = {
  groups: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
    }),
  ).isRequired,
  onRemoveMinister: PropTypes.func,
  onRemoveGroup: PropTypes.func,
  loadingGroupId: PropTypes.string,
};
