import PropTypes from "prop-types";
import { useState, useEffect } from "react";
import { GripHorizontal, Trash2, Save } from "lucide-react";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { AddMemberIcon } from "@/icons";

import { GroupBadge } from "@/components/group-badge";
import MicroMinisterCard from "./micro-minister-card";

export default function GroupCard({
  group,
  onRemoveMinister,
  onRemoveGroup,
  isSaving,
}) {
  const [showSaved, setShowSaved] = useState(false);

  useEffect(() => {
    if (isSaving) {
      setShowSaved(false);
    } else {
      setShowSaved(true);

      const timeout = setTimeout(() => setShowSaved(false), 1000);
      return () => clearTimeout(timeout);
    }
  }, [isSaving]);

  const {
    attributes,
    listeners,
    setNodeRef: setDraggableRef,
    transform,
  } = useDraggable({
    id: group.id,
  });

  const { setNodeRef: setDroppableRef } = useDroppable({
    id: group.id,
  });

  const style = transform
    ? {
        transform: CSS.Translate.toString(transform),
      }
    : undefined;

  const ministerIds = group.ministers.map((m) => m.id);

  return (
    <div
      ref={setDraggableRef}
      style={style}
      className="flex flex-col gap-4 p-3 mb-4 overflow-hidden bg-white border rounded-lg border-border-2"
    >
      <div className="flex items-center justify-between">
        <GroupBadge groupNumber={group.groupName.replace("Group ", "")} />
        <div
          {...listeners}
          {...attributes}
          className="mr-2 text-gray-400 cursor-grab"
        >
          <GripHorizontal size={16} />
        </div>
        <button onClick={() => onRemoveGroup(group.id)}>
          <Trash2 size={14} className="mr-2 text-grey-400" />
        </button>
      </div>

      <div
        ref={setDroppableRef}
        className="flex flex-col gap-2 overflow-y-auto"
      >
        {group.ministers.length > 0 ? (
          <SortableContext
            items={ministerIds}
            strategy={verticalListSortingStrategy}
          >
            {group.ministers.map((minister) => (
              <MicroMinisterCard
                key={minister.id}
                minister={{ ...minister, fromGroup: group.id }}
                onRemove={() => onRemoveMinister(group.id, minister.id)}
              />
            ))}
          </SortableContext>
        ) : (
          <div className="flex flex-col items-center justify-center gap-2 py-8 text-grey-600">
            <AddMemberIcon />
            <p className="text-sm text-center">
              Add Ministers to the group from the left side panel
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-end">
        {isSaving ? (
          <span className="inline-flex items-center gap-1.5 text-sm text-gray-500">
            <Save size={16} strokeWidth={1} />
            Saving...
          </span>
        ) : (
          showSaved && (
            <span className="inline-flex items-center gap-1.5 text-sm text-gray-500">
              <Save size={16} strokeWidth={1} />
              Saved
            </span>
          )
        )}
      </div>
    </div>
  );
}

GroupCard.propTypes = {
  group: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    groupName: PropTypes.string.isRequired,
    color: PropTypes.string,
    ministers: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
          .isRequired,
        name: PropTypes.string.isRequired,
        title: PropTypes.string,
        image: PropTypes.string,
      }),
    ).isRequired,
  }).isRequired,
  onRemoveMinister: PropTypes.func.isRequired,
  onRemoveGroup: PropTypes.func.isRequired,
  isSaving: PropTypes.bool,
};
