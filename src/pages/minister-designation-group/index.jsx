import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { buildRoute } from "@/config/routes";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { ArrowLeftIcon, ArrowRightIcon, StickyNote } from "lucide-react";
import {
  DndContext,
  DragOverlay,
  MouseSensor,
  PointerSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { Button, Dialogbox, toast } from "@kla-v2/ui-components";
import uuid4 from "uuid4";

import { AddButtonIcon } from "@/icons";
import { formatDate } from "@/utils";
import { DocumentMetadata } from "@/components/document-metadata";
import MinisterList from "./components/minister-list";
import GroupCardsList from "./components/group-cards-list";
import MicroMinisterCard from "./components/micro-minister-card";
import AddToGroupDialog from "./components/add-to-group-dialog";
import PreviewDialog from "./components/preview-dialog";

import {
  useCreateMinisterDesignationGroupMutation,
  useUpdateMinisterDesignationGroupMutation,
  useDeleteMinisterDesignationGroupMutation,
} from "@/services/minister-designation-group";
import { useGetMinistersQuery } from "@/services/master-data-management/ministers";
import {
  useGetDocumentByIdQuery,
  useSubmitDocumentMutation,
} from "@/services/documents";

export default function MinisterDesignationGroupPage() {
  const { documentId } = useParams();
  const navigate = useNavigate();

  useAutoBreadcrumb();

  const {
    data: ministersData,
    isLoading: isMinistersLoading,
    isError: isMinistersError,
  } = useGetMinistersQuery();

  const {
    data: documentData,
    isLoading: isDocumentLoading,
    error: isDocumentError,
    refetch: refetchDocumentData,
  } = useGetDocumentByIdQuery({ documentId });

  const [createMinisterDesignationGroup] =
    useCreateMinisterDesignationGroupMutation();
  const [updateMinisterDesignationGroup] =
    useUpdateMinisterDesignationGroupMutation();
  const [deleteMinisterDesignationGroup] =
    useDeleteMinisterDesignationGroupMutation();
  const [submitMinisterDesignationGroup, { isLoading: isSubmitting }] =
    useSubmitDocumentMutation();

  // Local state
  const [ministers, setMinisters] = useState([]);
  const [groups, setGroups] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [activeItem, setActiveItem] = useState(null);
  const [showAddToGroupDialog, setShowAddToGroupDialog] = useState(false);
  const [selectedMinister, setSelectedMinister] = useState(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [loadingGroupId, setLoadingGroupId] = useState(null);
  const [confirmationDialog, setConfirmationDialog] = useState({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {},
  });

  const [documentMetaData, setDocumentMetaData] = useState({
    entries: [],
    metadata: {
      kla: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });

  // Map document metadata and schedule data to component state
  useEffect(() => {
    if (documentData) {
      // Check document status and redirect if not DRAFT
      if (documentData.status !== "DRAFT" && !showPreviewDialog) {
        navigate(buildRoute.document('MINISTER_DESIGNATION_GROUP', documentId, 'view'));
        return;
      }

      setDocumentMetaData({
        entries: documentMetaData.entries || [],
        metadata: {
          kla: documentData.assembly?.toString() || "",
          session: documentData.session?.toString() || "",
          documentType: documentData.type || "Minister Designation Group",
          currentNo: documentData.currentNumber || "",
          createdOn: formatDate(documentData.createdAt) || "",
          createdBy: documentData.createdBy || "",
          name: documentData.name || "Minister Designation Group",
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentData, documentId, navigate, showPreviewDialog]);

  // Load initial data from API
  useEffect(() => {
    if (documentData && documentData.groups && ministersData) {
      // Extract all ministerIds from groupEntries
      const assignedMinisterIds = new Set(
        documentData.groups.flatMap((group) =>
          group.groupEntries.map((entry) => entry.ministerId),
        ),
      );

      // Filter ministersData to exclude assigned ministers
      const unassignedMinisters = ministersData.filter(
        (minister) => !assignedMinisterIds.has(minister.id),
      );

      setMinisters(unassignedMinisters);

      // Map groups with their assigned ministers
      const initialGroups = documentData.groups.map((group) => ({
        ...group,
        ministers: group.groupEntries.map((entry) => {
          const minister = ministersData.find((m) => m.id === entry.ministerId);
          return {
            ...entry,
            ...minister,
          };
        }),
      }));

      setGroups(initialGroups);

      // Check if groups array is empty and create a new group if it is
      if (documentData.groups.length === 0) {
        handleCreateNewGroup();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [documentData, ministersData]);

  // Initialize the sensors for drag and drop
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    }),
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10,
      },
    }),
  );

  // Drag and drop handlers
  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    // Check if the dragged item is a minister
    const ministerItem = ministers.find((m) => m.id === active.id);

    if (ministerItem) {
      setActiveItem({ ...ministerItem, type: "minister" });
      return;
    }

    // Check if the dragged item is a minister within a group
    for (const group of groups) {
      const groupMinister = group.ministers.find((m) => m.id === active.id);
      if (groupMinister) {
        setActiveItem({
          ...groupMinister,
          type: "minister",
          fromGroup: group.id,
        });
        return;
      }
    }

    // Check if the dragged item is a group
    const groupItem = groups.find((g) => g.id === active.id);
    if (groupItem) {
      setActiveItem({ ...groupItem, type: "group" });
      return;
    }
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      setActiveItem(null);
      return;
    }

    let updatedGroups = [...groups];
    let updatedMinisters = [...ministers];

    // Handle minister drag from ministers list to a group
    if (activeItem?.type === "minister" && !activeItem.fromGroup) {
      const ministerIndex = ministers.findIndex((m) => m.id === active.id);

      if (ministerIndex !== -1 && over.id !== active.id) {
        updatedGroups = groups.map((group) => {
          if (group.id === over.id) {
            const isAlreadyInGroup = group.ministers.some(
              (m) => m.id === active.id,
            );
            if (!isAlreadyInGroup) {
              return {
                ...group,
                ministers: [...group.ministers, ministers[ministerIndex]],
              };
            }
          }
          return group;
        });

        updatedMinisters = ministers.filter((m) => m.id !== active.id);
      }
    }

    // Handle minister drag from one group to another
    if (
      activeItem?.type === "minister" &&
      activeItem.fromGroup &&
      over.id !== activeItem.fromGroup
    ) {
      const sourceGroupId = activeItem.fromGroup;
      const targetGroupId = over.id;

      updatedGroups = groups.map((group) => {
        // Remove from source group
        if (group.id === sourceGroupId) {
          return {
            ...group,
            ministers: group.ministers.filter((m) => m.id !== active.id),
          };
        }
        // Add to target group
        if (group.id === targetGroupId) {
          const ministerToMove = groups
            .find((g) => g.id === sourceGroupId)
            ?.ministers.find((m) => m.id === active.id);

          if (ministerToMove) {
            return {
              ...group,
              ministers: [...group.ministers, ministerToMove],
            };
          }
        }
        return group;
      });
    }

    // Handle reordering ministers within a group
    if (
      activeItem?.type === "minister" &&
      activeItem.fromGroup &&
      over.id.startsWith(activeItem.fromGroup)
    ) {
      const groupIndex = groups.findIndex((g) => g.id === activeItem.fromGroup);

      if (groupIndex !== -1) {
        const group = groups[groupIndex];
        const activeIndex = group.ministers.findIndex(
          (m) => m.id === active.id,
        );
        const overIndex = group.ministers.findIndex((m) => m.id === over.id);

        if (activeIndex !== -1 && overIndex !== -1) {
          updatedGroups = [...groups];
          updatedGroups[groupIndex] = {
            ...group,
            ministers: arrayMove(group.ministers, activeIndex, overIndex),
          };
        }
      }
    }

    // Handle reordering groups
    if (activeItem?.type === "group" && over.id !== active.id) {
      const activeIndex = groups.findIndex((g) => g.id === active.id);
      const overIndex = groups.findIndex((g) => g.id === over.id);

      if (activeIndex !== -1 && overIndex !== -1) {
        updatedGroups = [...groups];

        // Swap only the entries/ministers between the groups
        const tempEntries = updatedGroups[activeIndex].ministers;
        updatedGroups[activeIndex] = {
          ...updatedGroups[activeIndex],
          ministers: updatedGroups[overIndex].ministers,
        };
        updatedGroups[overIndex] = {
          ...updatedGroups[overIndex],
          ministers: tempEntries,
        };

        // Explicitly keep the original order values
        updatedGroups[activeIndex].order = groups[activeIndex].order;
        updatedGroups[overIndex].order = groups[overIndex].order;
      }
    }

    // Transform the groups for the backend
    const transformedGroups = transformGroupsForBackend(updatedGroups);

    try {
      setLoadingGroupId(over.id);
      await updateMinisterDesignationGroup({
        documentId: documentId,
        data: transformedGroups,
      }).unwrap();

      // Update local state only after the API call succeeds
      setGroups(updatedGroups);
      setMinisters(updatedMinisters);
    } catch (error) {
      console.error("Failed to update groups:", error);
    } finally {
      setLoadingGroupId(null);
    }

    setActiveId(null);
    setActiveItem(null);
  };

  const handleAddMinisterToGroup = (minister) => {
    setSelectedMinister(minister);
    setShowAddToGroupDialog(true);
  };

  const transformGroupsForBackend = (groups) => {
    return {
      groups: groups.map((group) => ({
        id: group.id,
        groupName: group.groupName,
        groupNameInLocal: group.groupNameInLocal,
        order: group.order,
        entries: group.ministers.map((minister, index) => ({
          id: uuid4(),
          designationId: minister.primaryDesignation?.id || 12345,
          designation: minister.primaryDesignation?.title || "No Title",
          designationInLocal:
            minister.primaryDesignation?.titleInLocal || "No Title",
          ministerId: minister.id,
          order: index + 1,
        })),
      })),
    };
  };

  const handleConfirmAddToGroup = async (groupId) => {
    if (!selectedMinister || !groupId) return;

    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        return {
          ...group,
          ministers: [...group.ministers, selectedMinister],
        };
      }
      return group;
    });

    const updatedMinisters = ministers.filter(
      (m) => m.id !== selectedMinister.id,
    );

    // Transform the groups for the backend
    const transformedGroups = transformGroupsForBackend(updatedGroups);

    try {
      await updateMinisterDesignationGroup({
        documentId: documentId,
        data: transformedGroups,
      }).unwrap();

      setGroups(updatedGroups);
      setMinisters(updatedMinisters);
    } catch (error) {
      console.error("Failed to update groups:", error);
    }

    setShowAddToGroupDialog(false);
    setSelectedMinister(null);
  };

  const getNextGroupNumber = (groups) => {
    if (groups.length === 0) return 1;
    const maxOrder = Math.max(...groups.map((group) => group.order));
    return maxOrder + 1;
  };

  // Create new group
  const handleCreateNewGroup = async () => {
    const nextGroupNumber = getNextGroupNumber(groups);

    try {
      const response = await createMinisterDesignationGroup({
        documentId,
        data: { order: nextGroupNumber },
      }).unwrap();

      // Add the new group to the local state
      const newGroup = {
        ...response,
        ministers: [],
        order: nextGroupNumber,
      };

      setGroups([...groups, newGroup]);
    } catch (error) {
      console.error("Failed to create group:", error);
    }
  };

  // Remove minister from group
  const handleRemoveMinisterFromGroup = (groupId, ministerId) => {
    const handleConfirm = async () => {
      const ministerToRestore = groups
        .find((g) => g.id === groupId)
        ?.ministers.find((m) => m.id === ministerId);

      const updatedGroups = groups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            ministers: group.ministers.filter((m) => m.id !== ministerId),
          };
        }
        return group;
      });

      if (ministerToRestore) {
        setMinisters([...ministers, ministerToRestore]);
      }

      try {
        const transformedGroups = transformGroupsForBackend(updatedGroups);
        await updateMinisterDesignationGroup({
          documentId: documentId,
          data: transformedGroups,
        }).unwrap();

        setGroups(updatedGroups);
      } catch (error) {
        console.error("Failed to update groups:", error);
      }

      closeConfirmationDialog();
    };

    setConfirmationDialog({
      isOpen: true,
      title: "Remove Minister",
      description: "Are you sure you want to remove this minister?",
      onConfirm: handleConfirm,
    });
  };

  // Remove group
  const handleRemoveGroup = (groupId) => {
    const handleConfirm = async () => {
      const groupToRemove = groups.find((g) => g.id === groupId);

      if (groupToRemove && groupToRemove.ministers.length > 0) {
        setMinisters([...ministers, ...groupToRemove.ministers]);
      }

      try {
        await deleteMinisterDesignationGroup({
          documentId,
          groupId,
        }).unwrap();

        // Remove the group from the local state
        const updatedGroups = groups.filter((g) => g.id !== groupId);

        const reorderedGroups = updatedGroups
          .sort((a, b) => a.order - b.order)
          .map((group, index) => ({
            ...group,
            order: index + 1,
          }));

        setGroups(reorderedGroups);
      } catch (error) {
        console.error("Failed to delete group:", error);
      }

      refetchDocumentData();

      // Close dialog after operation completes
      closeConfirmationDialog();
    };

    setConfirmationDialog({
      isOpen: true,
      title: "Remove Group",
      description: "Are you sure you want to remove this group?",
      onConfirm: handleConfirm,
    });
  };

  // Submit groups
  const handleSubmit = async () => {
    try {
      const response = await submitMinisterDesignationGroup({
        documentId,
      }).unwrap();
      toast.success("Success", {
        description: response?.message || "Submission successful!",
      });
      setShowPreviewDialog(true);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to submit:",
        error,
      });
    }
  };

  const isSubmitDisabled = () => {
    const hasUnassignedMinisters = ministers.length > 0;
    const hasEmptyGroups = groups.some((group) => group.ministers.length === 0);

    return hasUnassignedMinisters || hasEmptyGroups;
  };

  // AttachToFile Method
  const handleAttachToFile = () => {
    //TODO: implement the attach to file method
    setShowPreviewDialog(false);
  };

  const closeConfirmationDialog = () => {
    setConfirmationDialog((prev) => ({ ...prev, isOpen: false }));
  };

  // Render
  if (isMinistersLoading || isDocumentLoading) return <div>Loading...</div>;

  if (isMinistersError || isDocumentError) {
    return <div>Error loading data. Please try again later.</div>;
  }

  if (documentData && documentData.status !== "DRAFT") {
    return null;
  }

  return (
    <div className="flex flex-col h-[calc(100vh-115px)] px-4">
      <div className="flex items-center justify-between my-2">
        <h1 className="typography-page-heading">
          {documentMetaData.metadata.name}
        </h1>
        <Button
          icon={StickyNote}
          iconPosition="right"
          size="md"
          variant="neutral"
          onClick={() => {}}
        >
          Preview
        </Button>
      </div>
      <DocumentMetadata documentMetadata={documentMetaData.metadata} />

      <div className="w-full mx-auto overflow-hidden">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          autoScroll
        >
          <div className="flex flex-row items-start w-full h-full gap-2 overflow-hidden">
            {/* Left Column - Minister List */}
            <MinisterList
              ministers={ministers}
              onAddToGroup={handleAddMinisterToGroup}
            />
            {/* Right Column - Groups */}
            <div className="flex flex-col flex-1 h-full min-h-0 p-4 bg-white rounded-lg shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex flex-col justify-center">
                  <h2 className="typography-sub-title-heading text-primary">
                    Group
                  </h2>
                  <p className="text-grey-500 typography-body-text-r-14">
                    Drag and drop ministers into groups
                  </p>
                </div>
                <Button
                  icon={AddButtonIcon}
                  iconPosition="left"
                  size="md"
                  variant="neutral"
                  onClick={handleCreateNewGroup}
                >
                  New Group
                </Button>
              </div>

              <div className="flex-1 overflow-hidden">
                <GroupCardsList
                  groups={groups}
                  onRemoveMinister={handleRemoveMinisterFromGroup}
                  onRemoveGroup={handleRemoveGroup}
                  loadingGroupId={loadingGroupId}
                />
              </div>
            </div>
          </div>

          <DragOverlay>
            {activeId && activeItem && activeItem.type === "minister" && (
              <MicroMinisterCard minister={activeItem} isOverlay />
            )}
            {activeId && activeItem && activeItem.type === "group" && (
              <div className="bg-white"></div>
            )}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Bottom Bar - Changed from fixed to sticky */}
      <div className="sticky bottom-0 left-0 z-10 flex justify-end w-full gap-4 p-4">
        <Button
          icon={ArrowLeftIcon}
          iconPosition="left"
          size="lg"
          variant="secondary"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <Button
          icon={ArrowRightIcon}
          iconPosition="right"
          size="lg"
          variant="primary"
          onClick={handleSubmit}
          disabled={isSubmitting || isSubmitDisabled()}
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </div>

      {/* Dialogs */}
      {showAddToGroupDialog && (
        <AddToGroupDialog
          groups={groups}
          isOpen={showAddToGroupDialog}
          onClose={() => setShowAddToGroupDialog(false)}
          onConfirm={handleConfirmAddToGroup}
        />
      )}

      {showPreviewDialog && (
        <PreviewDialog
          isOpen={showPreviewDialog}
          groups={groups}
          onClose={() => setShowPreviewDialog(false)}
          onConfirm={handleAttachToFile}
          documentMetadata={documentMetaData.metadata}
        />
      )}

      <Dialogbox
        open={confirmationDialog.isOpen}
        dialogTitle={confirmationDialog.title}
        dialogDescription={confirmationDialog.description}
        onSubmit={confirmationDialog.onConfirm}
        onCancel={closeConfirmationDialog}
        primaryActionText="Remove"
        secondaryActionText="Cancel"
      />
    </div>
  );
}
