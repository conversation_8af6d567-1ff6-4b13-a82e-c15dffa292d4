import ClausePopUpModal from "@/components/pop-up/clause-popup";
import ExampleModal from "@/components/pop-up/confirmation-popup/example-usage";
import DateShiftingModal from "../allotment-of-days/revision/date-shift-modal";
import ForwardDocumentDialog from "@/components/pop-up/forward-doument-dialog";
import AddAssignModal from "@/components/pop-up/assign-add-input/index";
import RulePopUpModal from "@/components/pop-up/rule-popup";
import ReturnSetupModal from "@/components/pop-up/return-settingup-modal";
import { useState } from "react";
import { Button } from "@kla-v2/ui-components";

const PopupButton = () => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isExamplePopupOpen, setIsExamplePopupOpen] = useState(false);
  const [isDateShiftOpen, setIsDateShiftOpen] = useState(false);
  const [isForwardDocOpen, setIsForwardDocOpen] = useState(false);
  const [isProceedModalOpen, setIsProceedModalOpen] = useState(false);
  const [isRuleOpen, setIsRuleOpen] = useState(false);
  const [isReturnModalOpens, setIsReturnModalOpens] = useState(false);

  return (
    <div className="p-4">
      <h2 className="text-xl px-4 font-semibold ">Clause Modals</h2>
      <div className="flex flex-wrap gap-4 p-4">
        <Button
          type="button"
          variant="primary"
          onClick={() => setIsPreviewOpen(true)}
        >
          Clause Pop Up
        </Button>
        <ClausePopUpModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
        />
        <Button
          type="button"
          variant="primary"
          onClick={() => setIsRuleOpen(true)}
        >
          Rule Pop Up
        </Button>
        <RulePopUpModal
          isOpen={isRuleOpen}
          onClose={() => setIsRuleOpen(false)}
        />
      </div>
      <h2 className="text-xl px-4 font-semibold mt-4">Other Modals</h2>
      <div className="flex flex-wrap gap-4 p-4">
        <Button
          type="button"
          variant="primary"
          onClick={() => setIsDateShiftOpen(true)}
        >
          Date Shifting
        </Button>
        <DateShiftingModal
          isOpen={isDateShiftOpen}
          onClose={() => setIsDateShiftOpen(false)}
        />

        <Button
          type="button"
          variant="primary"
          onClick={() => setIsForwardDocOpen(true)}
        >
          Forward Document
        </Button>
        <ForwardDocumentDialog
          isOpen={isForwardDocOpen}
          onClose={() => setIsForwardDocOpen(false)}
        />
      </div>
      <div className="flex items-center">
        <ExampleModal
          isOpen={isExamplePopupOpen}
          onClose={() => setIsExamplePopupOpen(false)}
        />
      </div>
      <div className="flex items-center p-4 gap-4">
        <Button variant="primary" onClick={() => setIsProceedModalOpen(true)}>
          Save Modal
        </Button>
        <Button variant="primary" onClick={() => setIsReturnModalOpens(true)}>
          Return Modal
        </Button>
        <AddAssignModal
          isOpen={isProceedModalOpen}
          onClose={() => setIsProceedModalOpen(false)}
          title="Save"
          subtitle="Choose workflow actions"
          reasonLabel="Add Reason"
          addButtonText="Proceed"
          cancelButtonText="Cancel"
          warningText={false}
          showReasonInput={true}
        />
        <ReturnSetupModal
          isOpen={isReturnModalOpens}
          onClose={() => setIsReturnModalOpens(false)}
          title="Return Setting Up"
          subtitle="Are you sure you want to cancel the Setting Up?"
          reasonLabel="Add Reason"
          addButtonText="Confirm"
          cancelButtonText="Cancel"
          warningText={false}
          showReasonInput={true}
        />
      </div>
    </div>
  );
};

export default PopupButton;
