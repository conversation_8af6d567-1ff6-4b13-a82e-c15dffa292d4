import AnswerBulletinHeader from "@/components/answer-bulletin/answer-bulletin-header";
import { DocumentMetadata } from "@/components/document-metadata";
import MultiHeaderDataTable from "@/components/multi-header-data-table";
import { useLanguage } from "@/hooks";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useGetDelayedAnswerBulletinQuery } from "@/services/delayed-answer-bulletin";
import { formatDate } from "@/utils";
import { Button, IconButton } from "@kla-v2/ui-components";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  File,
  Info,
  Loader2,
  RotateCcw,
  Save,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

export default function DelayedAnswerBulletinPage() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { documentId } = useParams();

  useAutoBreadcrumb();

  const [dabData, setDabData] = useState({
    delayedAnswers: [],
    metadata: {
      Assembly: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });

  const {
    data: DelayedAnswerBulletinResponse,
    isLoading: DelayedAnswerBulletinLoading,
    error: DelayedAnswerBulletinError,
  } = useGetDelayedAnswerBulletinQuery(documentId);

  // Map document metadata and dab data to component state
  useEffect(() => {
    if (DelayedAnswerBulletinResponse) {
      setDabData({
        delayedAnswers: DelayedAnswerBulletinResponse?.delayedAnswers || [],
        metadata: {
          Assembly: DelayedAnswerBulletinResponse?.assembly?.toString() || "",
          session: DelayedAnswerBulletinResponse?.session?.toString() || "",
          documentType:
            DelayedAnswerBulletinResponse?.type || "Delayed Answer Bulletin",
          currentNo: DelayedAnswerBulletinResponse?.currentNumber || "",
          createdOn: formatDate(DelayedAnswerBulletinResponse?.createdAt) || "",
          createdBy: DelayedAnswerBulletinResponse?.createdBy || "",
          name:
            DelayedAnswerBulletinResponse?.name || "Delayed Answer Bulletin",
        },
      });
    } else {
      // Update just the metadata if document data is available
      setDabData((prev) => ({
        ...prev,
        metadata: {
          Assembly: DelayedAnswerBulletinResponse?.assembly?.toString() || "",
          session: DelayedAnswerBulletinResponse?.session?.toString() || "",
          documentType:
            DelayedAnswerBulletinResponse?.type || "Delayed Answer Bulletin",
          currentNo: DelayedAnswerBulletinResponse?.currentNumber || "",
          createdOn: formatDate(DelayedAnswerBulletinResponse?.createdAt) || "",
          createdBy: DelayedAnswerBulletinResponse?.createdBy || "",
          name:
            DelayedAnswerBulletinResponse?.name || "Delayed Answer Bulletin",
        },
      }));
    }
  }, [DelayedAnswerBulletinResponse]);

  const headers = [
    {
      id: "row1",
      cells: [
        {
          id: "assembly",
          title: "നിയമസഭ",
          rowSpan: 2,
        },
        {
          id: "session",
          title: "സമ്മേളനം",
          rowSpan: 2,
        },
        { id: "designation", title: "വകുപ്പ് മന്ത്രി", rowSpan: 2 },
        { id: "date", title: "തീയതി", rowSpan: 2 },
        {
          id: "questionNumbers",
          title: "ചോദ്യ നമ്പറുകൾ",
          className: "text-white text-center border border-white h-[98px]",
          colSpan: 2,
        },
      ],
    },
    {
      id: "row2",
      cells: [
        {
          id: "questionsWithInterimAndDelayedFinalAnswers",
          title: "ഇടക്കാല മറുപടിയായി ലഭിച്ചതിന്മേൽ",
          className:
            "text-white text-center border border-white h-[189px] w-[217px]",
        },
        {
          id: "questionsWithFinalDelayedAnswers",
          title: "ആദ്യമായി ലഭിച്ച മറുപടിയിന്മേൽ",
          className:
            "text-white text-center border border-white h-[189px] w-[217px]",
        },
      ],
    },
  ];

  const columns = [
    { id: "assembly", accessor: "assembly" },
    { id: "session", accessor: "session" },
    {
      id: "designation",
      accessor: "designationInLocal",
      cell: (value) => value || "-",
    },
    {
      id: "date",
      accessor: "questionDate",
      cell: (value) => value || "-",
    },

    {
      id: "questionsWithInterimAndDelayedFinalAnswers",
      accessor: "questionsWithInterimAndDelayedFinalAnswers",
      cell: (value) =>
        value && value.length > 0
          ? value.map((q) => q.questionNumber).join(", ")
          : "-",
    },
    {
      id: "questionsWithFinalDelayedAnswers",
      accessor: "questionsWithFinalDelayedAnswers",
      cell: (value) =>
        value && value.length > 0
          ? value.map((q) => q.questionNumber).join(", ")
          : "-",
    },
  ];

  const goBack = () => {
    navigate(-1);
  };

  if (DelayedAnswerBulletinLoading && !dabData.metadata.name) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-medium">
            Loading delayed answer bulletin...
          </p>
        </div>
      </div>
    );
  }

  if (DelayedAnswerBulletinError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="p-6 bg-destructive/10 rounded-lg max-w-md text-center">
          <h2 className="text-xl font-bold text-destructive mb-2">
            Error Loading Data
          </h2>
          <p className="mb-4">
            Unable to load delayed answer bulletin. Please try again.
          </p>
          <Button onClick={goBack} variant="neutral">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-background min-h-screen">
      <div className="px-5 py-3">
        <div className="p-5 rounded-md">
          <div className="mb-4 flex justify-between">
            <h1 className="typography-page-heading">{dabData.metadata.name}</h1>
            <div className="flex gap-2">
              <IconButton
                icon={RotateCcw}
                iconPosition="right"
                size="icon"
                variant="neutral"
                onClick={() => {}}
                className="size-9"
              />
              <Button
                variant="neutral"
                icon={Info}
                iconPosition="right"
                className="h-9"
                type="button"
                size="sm"
              >
                Rule-47(2)
              </Button>
              <Button
                variant="neutral"
                icon={File}
                iconPosition="right"
                className="h-9"
                type="button"
                size="sm"
              >
                Preview
              </Button>
            </div>
          </div>
          <DocumentMetadata documentMetadata={dabData.metadata} />
          <div className="bg-background-container py-6 px-12">
            <AnswerBulletinHeader document={DelayedAnswerBulletinResponse} />
            <MultiHeaderDataTable
              headers={headers}
              columns={columns}
              data={dabData?.delayedAnswers}
              showNumberedHeader
            />
          </div>
        </div>
        <div className="flex justify-end">
          <div className="flex gap-2">
            <Button
              icon={ArrowLeftIcon}
              iconPosition="left"
              size="lg"
              variant="neutral"
              onClick={goBack}
            >
              {t("back")}
            </Button>
            <Button
              iconPosition="left"
              size="lg"
              variant="secondary"
              onClick={() => {}}
            >
              <Save size={16} />
              <span>{t("save")}</span>
            </Button>
            <Button
              icon={ArrowRightIcon}
              iconPosition="right"
              size="lg"
              variant="primary"
              onClick={() => {}}
            >
              {t("submit")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
