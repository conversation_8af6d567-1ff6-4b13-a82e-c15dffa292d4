import {
  Input,
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@kla-v2/ui-components";
import { Search } from "lucide-react";
import ConsentModal from "./consent-request-modal";
import ConsentCard from "@/components/consent-card";
import {
  useFetchAcceptedConsentsQuery,
  useFetchPendingConsentsQuery,
  useFetchUserConsentsQuery,
} from "@/services/consent";
import { useState } from "react";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useLanguage } from "@/hooks";

function ConsentList() {
  const { t } = useLanguage();

  useAutoBreadcrumb();

  const { data: pendingConsentsData, refetch: refetchPendingConsents } =
    useFetchPendingConsentsQuery({});

  const { data: acceptedConsentsData, refetch: refetchAcceptedConsents } =
    useFetchAcceptedConsentsQuery({});

  const { data: sendedConsentsData, refetch: refetchSendedConsents } =
    useFetchUserConsentsQuery({});

  const handleConsentRequestSuccess = () => {
    refetchPendingConsents();
    refetchAcceptedConsents();
    refetchSendedConsents();
  };

  const [searchQuery, setSearchQuery] = useState("");

  const filterConsents = (consents) => {
    if (!searchQuery) return consents;
    return consents.filter(
      (consent) =>
        consent.memberDisplayName
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        consent.constituencyName
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        consent.politicalPartyName
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()),
    );
  };

  return (
    <div className="px-5 py-3 bg-background">
      <div className="p-5 bg-background-container h-screen">
        <div className="flex flex-col gap-4">
          <div className="flex justify-between">
            <h2 className="typography-page-heading">{t("consentList")}</h2>
            <ConsentModal
              onConsentRequestSuccess={handleConsentRequestSuccess}
            />
          </div>
          <div className="flex justify-between items-center">
            <Input
              icon={Search}
              className="w-80"
              reserveErrorSpace={false}
              placeholder={"Search"}
              size="md"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div>
            <Tabs defaultValue="REQUEST_RECEIVED">
              <TabsList variant="segmented" className="block">
                <TabsTrigger value="REQUEST_RECEIVED" variant="segmented">
                  {t("requestReceived")}
                </TabsTrigger>
                <TabsTrigger value="REQUEST_ACCEPTED" variant="segmented">
                  {t("requestAccepted")}
                </TabsTrigger>
                <TabsTrigger value="REQUEST_SENT" variant="segmented">
                  {t("requestSent")}
                </TabsTrigger>
                <hr className="w-full" />
              </TabsList>
              <TabsContent value="REQUEST_RECEIVED">
                <div className="grid grid-cols-3 gap-4">
                  {filterConsents(pendingConsentsData || []).map((consent) => (
                    <ConsentCard key={consent.id} data={consent} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="REQUEST_ACCEPTED">
                <div className="grid grid-cols-3 gap-4">
                  {filterConsents(acceptedConsentsData || []).map((consent) => (
                    <ConsentCard key={consent.id} data={consent} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="REQUEST_SENT">
                <div className="grid grid-cols-3 gap-4">
                  {filterConsents(sendedConsentsData || []).map((consent) => (
                    <ConsentCard key={consent.id} data={consent} hideActions />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConsentList;
