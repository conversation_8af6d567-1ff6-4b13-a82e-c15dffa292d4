import { useState } from "react";
import { Search } from "lucide-react";
import { Checkbox, Badge } from "@kla-v2/ui-components";
import AddButtonIcon from "@/icons/add-icon";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";

export default function MultiSelect({ members, onSelectionChange }) {
  const { t } = useLanguage();
  const [selected, setSelected] = useState([]);
  const [search, setSearch] = useState("");
  const toggleSelect = (member, isChecked) => {
    const newSelection = isChecked
      ? [...selected, member]
      : selected.filter((item) => item.id !== member.id);
    setSelected(newSelection);
    onSelectionChange(newSelection);
  };
  return (
    <div>
      <div className="flex flex-wrap items-center gap-2 mb-3">
        {selected.length === 0 ? (
          <Badge
            icon={
              <AddButtonIcon className="!w-[22px] !h-[22px] text-grey-400" />
            }
            size="sm"
            className="h-8 px-2 flex items-center justify-center gap-2  text-grey-400"
          >
            {t("form:addMembers")}
          </Badge>
        ) : (
          selected.map((member) => (
            <Badge
              key={member.id}
              onClose={() => toggleSelect(member, false)}
              size="sm"
              className="flex items-center justify-center gap-1 px-2 py-2 h-8 "
            >
              {member.displayName}
            </Badge>
          ))
        )}
      </div>

      <div className=" border border-gray-300 rounded-md bg-white shadow-sm">
        <div className="flex items-center gap-2 px-4 py-3 bg-white border-b border-[#D0D5DD] rounded-t-[4px]">
          <Search className="h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder={t("placeholder:search")}
            className="w-full outline-none border-none bg-transparent text-gray-900"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>

        <ul className="max-h-[100px] overflow-y-auto">
          {(Array.isArray(members) ? members : [])
            .filter((member) =>
              member?.user?.displayName
                ?.toLowerCase()
                .includes(search.toLowerCase()),
            )
            .map((member) => {
              const memberData = {
                // TODO
                id: member.user?.id || Math.random().toString(),
                displayName: member.user?.displayName || "Unknown",
                constituencyName: member.constituency?.name || "N/A",
              };

              return (
                <li
                  key={memberData.id}
                  className="flex justify-between items-center px-4 py-2 cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selected.some(
                        (item) => item.id === memberData.id,
                      )}
                      onCheckedChange={(value) =>
                        toggleSelect(memberData, value)
                      }
                    />
                    <span
                      className={`${
                        selected.some((item) => item.id === memberData.id)
                          ? "font-semibold"
                          : ""
                      }`}
                    >
                      {memberData.displayName}
                    </span>
                  </div>
                  <span className="text-gray-500">
                    {memberData.constituencyName}
                  </span>
                </li>
              );
            })}
        </ul>
      </div>
    </div>
  );
}
MultiSelect.propTypes = {
  members: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      location: PropTypes.string,
    }),
  ),
  onSelectionChange: PropTypes.func.isRequired,
};
