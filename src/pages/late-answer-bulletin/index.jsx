import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import {
  StickyNote,
  RotateCcw,
  ArrowLeftIcon,
  ArrowRightIcon,
  Save,
} from "lucide-react";
import { Button } from "@kla-v2/ui-components";

import { formatDate } from "@/utils";
import { useLanguage } from "@/hooks";
import { useGetLateAnswerBulletinQuery } from "@/services/late-answer-bulletin";
import AnswerBulletinHeader from "@/components/answer-bulletin/answer-bulletin-header";
import MultiHeaderDataTable from "@/components/multi-header-data-table";
import { DocumentMetadata } from "@/components/document-metadata";

const LateAnswerBulletinPage = () => {
  const { documentId } = useParams();
  const { t } = useLanguage();

  useAutoBreadcrumb();

  const [tableData, setTableData] = useState([]);
  const [documentMetaData, setDocumentMetaData] = useState({
    entries: [],
    metadata: {
      kla: "",
      session: "",
      documentType: "",
      currentNo: "",
      createdOn: "",
      createdBy: "",
      name: "",
    },
  });

  const { data: labDocument, isLoading } =
    useGetLateAnswerBulletinQuery(documentId);

  useEffect(() => {
    if (labDocument) {
      setDocumentMetaData({
        entries: documentMetaData.entries || [],
        metadata: {
          kla: labDocument.assembly?.toString() || "",
          session: labDocument.session?.toString() || "",
          documentType: labDocument.type || "Late Answer Bulletin",
          currentNo: labDocument.currentNumber || "",
          createdOn: formatDate(labDocument.createdAt) || "",
          createdBy: labDocument.createdBy || "",
          name: labDocument.name || "Late Answer Bulletin",
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [labDocument]);

  useEffect(() => {
    if (labDocument) {
      setTableData(labDocument.lateAnswerStats || []);
    }
  }, [labDocument]);

  // Column definitions
  const columns = [
    {
      id: "date",
      accessor: "questionDate",
      cell: (value) => {
        const date = new Date(value);
        return `${date.getDate().toString().padStart(2, "0")}/${(
          date.getMonth() + 1
        )
          .toString()
          .padStart(2, "0")}/${date.getFullYear()}`;
      },
    },
    {
      id: "starredQuestionCount",
      accessor: "starredQuestionCount",
    },
    {
      id: "unstarredQuestionCount",
      accessor: "unstarredQuestionCount",
    },
    {
      id: "totalQuestionCount",
      accessor: "totalQuestionCount",
    },
    {
      id: "starredWithInterimAnswerCount",
      accessor: "questionsAnsweredOnTime.starredWithInterimAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "starredWithFinalAnswerCount",
      accessor: "questionsAnsweredOnTime.starredWithFinalAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "unstarredWithInterimAnswerCount",
      accessor: "questionsAnsweredOnTime.unstarredWithInterimAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "unstarredWithFinalAnswerCount",
      accessor: "questionsAnsweredOnTime.unstarredWithFinalAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredOnTimeTotal",
      accessor: "questionsAnsweredOnTime.totalQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsNotAnsweredOnTimeStarred",
      accessor: "questionsNotAnsweredOnTime.starredQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsNotAnsweredOnTimeUnstarred",
      accessor: "questionsNotAnsweredOnTime.unstarredQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsNotAnsweredOnTimeTotal",
      accessor: "questionsNotAnsweredOnTime.totalQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateStarredInterim",
      accessor: "questionsAnsweredLate.starredWithInterimAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateStarredFinal",
      accessor: "questionsAnsweredLate.starredWithFinalAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateUnstarredInterim",
      accessor: "questionsAnsweredLate.unstarredWithInterimAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateUnstarredInterimFinal",
      accessor: "questionsAnsweredLate.unstarredWithInterimAndFinalAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateUnstarredFinal",
      accessor: "questionsAnsweredLate.unstarredWithFinalAnswerCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "questionsAnsweredLateTotal",
      accessor: "questionsAnsweredLate.totalQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "starredQuestionsNotAnswered",
      accessor: "questionsNotAnswered.starredQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "unstarredQuestionsNotAnswered",
      accessor: "questionsNotAnswered.unstarredQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
    {
      id: "totalQuestionsNotAnswered",
      accessor: "questionsNotAnswered.totalQuestionCount",
      cell: (value) => (value === 0 ? "-" : value.toString()),
    },
  ];

  // Multi-level header structure
  const headers = [
    {
      id: "row1",
      cells: [
        { id: "date", title: "തീയതി", rowSpan: 3 },
        {
          id: "totalQuestions",
          title: "പട്ടികയിലുള്ള ചോദ്യങ്ങളുടെ എണ്ണം",
          colSpan: 3,
        },
        {
          id: "answeredOnTime",
          title: "ചട്ടം 47(1) പ്രകാരം ലഭിച്ച ഉത്തരങ്ങളുടെ എണ്ണം",
          colSpan: 5,
        },
        {
          id: "notAnsweredOnTime",
          title: "ഇനിയും ഉത്തരം ലഭിക്കാനുള്ള ചോദ്യങ്ങളുടെ എണ്ണം",
          colSpan: 3,
        },
        {
          id: "answeredLate",
          title:
            "യഥാസമയം ഉത്തരം ലഭിക്കാത്തതും എന്നാൽ ഇനിയും ഉത്തരം ലഭിച്ചുവരായ ചോദ്യങ്ങളുടെ സംഖ്യകൾ എന്നിവ",
          colSpan: 6,
        },
        {
          id: "notAnswered",
          title: "ഇനിയും ഉത്തരം ലഭിക്കാനുള്ള ചോദ്യങ്ങളുടെ എണ്ണം",
          colSpan: 3,
        },
      ],
    },
    {
      id: "row2",
      cells: [
        { id: "starredTotal", title: "നക്ഷത്ര ചിഹ്നം ഉള്ളത്", rowSpan: 2 },
        {
          id: "unstarredTotal",
          title: "നക്ഷത്ര ചിഹ്നം ഇല്ലാത്തത്",
          rowSpan: 2,
        },
        { id: "totalTotal", title: "ആകെ", rowSpan: 2 },
        { id: "starredOnTime", title: "നക്ഷത്ര ചിഹ്നം ഉള്ളത്", colSpan: 2 },
        {
          id: "unstarredOnTime",
          title: "നക്ഷത്ര ചിഹ്നം ഇല്ലാത്തത്",
          colSpan: 2,
        },
        { id: "totalOnTime", title: "ആകെ", rowSpan: 2 },
        { id: "starredNotOnTime", title: "നക്ഷത്ര ചിഹ്നം ഉള്ളത്", rowSpan: 2 },
        {
          id: "unstarredNotOnTime",
          title: "നക്ഷത്ര ചിഹ്നം ഇല്ലാത്തത്",
          rowSpan: 2,
        },
        { id: "totalNotOnTime", title: "ആകെ", rowSpan: 2 },
        { id: "starredLate", title: "നക്ഷത്ര ചിഹ്നം ഉള്ളത്", colSpan: 2 },
        { id: "unstarredLate", title: "നക്ഷത്ര ചിഹ്നം ഇല്ലാത്തത്", colSpan: 3 },
        { id: "totalLate", title: "ആകെ", rowSpan: 2 },
        {
          id: "starredNotAnswered",
          title: "നക്ഷത്ര ചിഹ്നം ഉള്ളത്",
          rowSpan: 2,
        },
        {
          id: "unstarredNotAnswered",
          title: "നക്ഷത്ര ചിഹ്നം ഇല്ലാത്തത്",
          rowSpan: 2,
        },
        { id: "totalNotAnswered", title: "ആകെ", rowSpan: 2 },
      ],
    },
    {
      id: "row3",
      cells: [
        { id: "starredOnTimeInterim", title: "ഇടക്കാല മറുപടി" },
        { id: "starredOnTimeFinal", title: "അന്തിമ മറുപടി" },
        { id: "unstarredOnTimeInterim", title: "ഇടക്കാല മറുപടി" },
        { id: "unstarredOnTimeFinal", title: "അന്തിമ മറുപടി" },
        { id: "starredLateInterim", title: "ഇടക്കാല മറുപടി" },
        { id: "starredLateFinal", title: "അന്തിമ മറുപടി" },
        { id: "unstarredLateInterim", title: "ഇടക്കാല മറുപടി" },
        {
          id: "unstarredLateInterimFinal",
          title: "ഇടക്കാല മറുപടി അന്തിമ മറുപടിയായത്",
        },
        { id: "unstarredLateFinal", title: "അന്തിമ മറുപടി" },
      ],
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-115px)]">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-[calc(100vh-115px)] px-4">
      <div className="flex items-center justify-between my-4">
        <h1 className="typography-page-heading">
          {labDocument?.name || "Late Answer Bulletin"}
        </h1>
        <div className="flex items-center gap-2">
          <Button
            icon={RotateCcw}
            iconPosition="right"
            size="icon"
            variant="neutral"
            onClick={() => {}}
          ></Button>
          <Button
            icon={StickyNote}
            iconPosition="right"
            size="md"
            variant="neutral"
            onClick={() => {}}
          >
            Preview
          </Button>
        </div>
      </div>
      <DocumentMetadata documentMetadata={documentMetaData.metadata} />

      <div className="px-12 py-6 bg-white rounded-md shadow-md">
        <AnswerBulletinHeader document={labDocument} />

        <MultiHeaderDataTable
          headers={headers}
          columns={columns}
          data={tableData}
          showNumberedHeader
        />
      </div>

      <div className="flex justify-end w-full gap-4 p-4">
        <Button
          icon={ArrowLeftIcon}
          iconPosition="left"
          size="lg"
          variant="neutral"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <Button
          iconPosition="left"
          size="lg"
          variant="secondary"
          onClick={() => {}}
        >
          <Save size={24} />
          <span>{t("save")}</span>
        </Button>
        <Button
          icon={ArrowRightIcon}
          iconPosition="right"
          size="lg"
          variant="primary"
          onClick={() => {}}
        >
          Submit
        </Button>
      </div>
    </div>
  );
};

export default LateAnswerBulletinPage;
