import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
  AccordionItem,
  ExpandableTrigger,
  ExpandableContent,
  ExpandableAccordion,
  Button,
  MultiSelectDropdown,
} from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import { useState } from "react";
import BasicDetails from "./basic-details/basic-details";
import NoticeDetailsEdit from "./notice-details/notice-details";
import { DocumentMetadata } from "@/components/document-metadata";

import {
  useGetQuestionBasicDetailsQuery,
  useGetQuestionNoticeDetailsQuery,
} from "@/services/notice-question-edit";
import { useParams } from "react-router-dom";
import { File } from "lucide-react";
import Preview from "./preview";
import { useMemo } from "react";
import { formatDate } from "@/utils";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

const QuestionEditPage = () => {
  const [expandedAccordions, setExpandedAccordions] = useState([
    "item-1",
    "item-2",
  ]);
  const [selectedVersions, setSelectedVersions] = useState([]);
  const { documentId } = useParams();
  const { data: basicDetails } = useGetQuestionBasicDetailsQuery(documentId);
  const { data: noticeDetails } = useGetQuestionNoticeDetailsQuery(documentId);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [basicDetailsFormValues, setBasicDetailsFormValues] = useState(null);
  const [noticeDetailsFormValues, setNoticeDetailsFormValues] = useState(null);
  const handleAccordionValueChange = (value) => {
    setExpandedAccordions(value);
  };

  const versionOptions = useMemo(() => {
    if (!basicDetails) return [];

    const options = [];

    if (basicDetails.primaryMember) {
      options.push({
        group: "Primary Member",
        imageUrl:
          basicDetails.primaryMember.imageUrl || "https://placehold.co/400",
        label: basicDetails.primaryMember.memberDisplayName,
        value: basicDetails.primaryMember.memberId.toString(),
        subLabel: basicDetails.primaryMember.constituencyName,
        constituency: basicDetails.primaryMember.constituencyName,
      });
    }

    if (basicDetails.secondaryMembers) {
      const secondaryMembersArray = Array.isArray(basicDetails.secondaryMembers)
        ? basicDetails.secondaryMembers
        : [basicDetails.secondaryMembers];

      secondaryMembersArray.forEach((member) => {
        if (member && member.memberDisplayName) {
          options.push({
            label: member.memberDisplayName,
            value: member.memberId?.toString() || member.memberDisplayName,
            group: "Secondary Members",
            subLabel: member.constituencyName,
            imageUrl: member.imageUrl || "https://placehold.co/400",
            constituency: member.constituencyName,
          });
        }
      });
    }

    return options;
  }, [basicDetails]);

  const handleVersionSelectionChange = (selected) => {
    setSelectedVersions(
      selected.map((option) => ({
        memberName: option.label,
        constituency: option.constituency,
        imageUrl: option.imageUrl,
      })),
    );
  };

  const handlePreview = () => {
    setIsPreviewOpen(true);
  };

  useAutoBreadcrumb();

  return (
    <div>
      <div className="flex items-center justify-between p-4">
        <h1 className="typography-sub-heading">
          1224 വടകര നിയോജകമണ്ഡലത്തില്‍ പാലം നിര്‍മ്മിക്കുന്ന പദ്ധതി
        </h1>
        <Button
          variant="neutral"
          icon={File}
          iconPosition="right"
          type="button"
          size="sm"
          onClick={handlePreview}
        >
          Preview
        </Button>
      </div>
      <Preview
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        basicDetailsFormValues={basicDetailsFormValues}
        noticeDetailsFormValues={noticeDetailsFormValues}
        apiBasicDetails={basicDetails}
        apiNoticeDetails={noticeDetails}
      />
      <div className="ml-3">
        <DocumentMetadata
          documentMetadata={{
            documentType: "Notice For Question",
            createdOn: formatDate(basicDetails?.createdAt),
            createdBy: basicDetails?.createdBy || "-",
          }}
        />
      </div>
      <Tabs defaultValue="tab2" className="p-2">
        <TabsList variant="solid" className="flex w-full">
          <TabsTrigger value="tab2" variant="solid" className="w-1/2">
            Version
          </TabsTrigger>
          <TabsTrigger value="tab3" variant="solid" className="w-1/2">
            Notes
          </TabsTrigger>
        </TabsList>
        <TabsContent value="tab2" className="m-0">
          <div className="flex items-center justify-between">
            <MultiSelectDropdown
              options={versionOptions}
              onSelectionChange={handleVersionSelectionChange}
              selectedValues={versionOptions.filter((opt) =>
                selectedVersions.some(
                  (version) => version.memberName === opt.label,
                ),
              )}
              addLabel="Select Version"
              maxCount={3}
            />

            <div className="flex items-center gap-2 text-gray-600">
              <span>Key</span>
              <div className="flex items-center gap-2 typography-body-text-r-14">
                <h3 className="bg-[#DCFFF1] underline">This Line was Added</h3>
                <h3 className="bg-[#FFECEB] line-through">
                  This Line was Removed
                </h3>
              </div>
            </div>
          </div>
          <ExpandableAccordion
            type="multiple"
            value={expandedAccordions}
            onValueChange={handleAccordionValueChange}
          >
            <AccordionItem value="item-1">
              <ExpandableTrigger
                className="text-base font-semibold text-primary"
                variant="secondary"
              >
                <AccordionTitle
                  accordionOrderNo="1"
                  accordionTitle="Basic Details"
                  isColored={true}
                />
              </ExpandableTrigger>
              <ExpandableContent>
                <hr className="mb-5 border border-stone-300 opacity-30" />
                <BasicDetails onFormChange={setBasicDetailsFormValues} />
              </ExpandableContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <ExpandableTrigger
                className="text-base font-semibold text-primary"
                variant="secondary"
              >
                <AccordionTitle
                  accordionOrderNo="2"
                  accordionTitle="Notice Details"
                  isColored={true}
                />
              </ExpandableTrigger>
              <ExpandableContent>
                <hr className="mb-5 border border-stone-300 opacity-30" />
                <NoticeDetailsEdit onFormChange={setNoticeDetailsFormValues} />
              </ExpandableContent>
            </AccordionItem>
          </ExpandableAccordion>
        </TabsContent>

        <TabsContent value="tab3">Notice Content</TabsContent>
      </Tabs>
    </div>
  );
};

export default QuestionEditPage;
