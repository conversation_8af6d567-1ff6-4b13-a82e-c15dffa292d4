import { useState, useEffect, useRef, useCallback } from "react";
import { Textarea } from "@kla-v2/ui-components";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useFieldArray, useForm } from "react-hook-form";
import { Save, Plus, GripVertical, Trash2 } from "lucide-react";
import CornerIcon from "@/icons/corner-icon";
import CompareIcon from "@/icons/compare-icon";
import { StarredIcon, UnStarredIcon } from "@/icons/star-icon";
import { useParams } from "react-router-dom";
import {
  useGetQuestionNoticeDetailsQuery,
  useUpdateQuestionNoticeDetailsMutation,
} from "@/services/notice-question-edit";
import AddFillIcon from "@/icons/add-fill-icon";
import { useGetQuestionBasicDetailsQuery } from "@/services/notice-question-edit";
import { Badge } from "@kla-v2/ui-components";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import PropTypes from "prop-types";
import { noticeDetailsFormFields } from "./form-fields";
import { useLanguage } from "@/hooks";

const NoticeDetailsEdit = ({ onFormChange }) => {
  const { t } = useLanguage();
  const { documentId } = useParams();
  const isInitialMount = useRef(true);
  const previousFormValues = useRef({});
  const dataLoaded = useRef(false);
  const formFields = noticeDetailsFormFields(t);

  const [originalData, setOriginalData] = useState({
    noticeHeading: "",
    noticePriority: "",
    starred: false,
    clauses: [{ content: "", tags: [] }],
    constituencies: [],
  });

  const { data: apiNoticeDetails, isLoading: isLoadingNoticeDetails } =
    useGetQuestionNoticeDetailsQuery(documentId, {
      skip: dataLoaded.current,
    });

  const { data: apiBasicDetails, isLoading: isLoadingBasicDetails } =
    useGetQuestionBasicDetailsQuery(documentId);

  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const [constituencies, setConstituencies] = useState([]);
  const [showSecondaryConstituency, setShowSecondaryConstituency] =
    useState(false);
  const [showSaving, setShowSaving] = useState(false);

  const [updateNoticeDetails, { isLoading: isSaving }] =
    useUpdateQuestionNoticeDetailsMutation();

  const form = useForm({
    mode: "onChange",
    defaultValues: {
      noticeHeading: "",
      noticePriority: "",
      starred: false,
      clauses: [{ content: "", tags: [] }],
      constituencies: [],
    },
  });

  const { control, watch, setValue, getValues } = form;
  const { fields, append, remove, move } = useFieldArray({
    control,
    name: formFields.CLAUSES.name,
  });

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex((item) => item.id === active.id);
      const newIndex = fields.findIndex((item) => item.id === over.id);
      move(oldIndex, newIndex);
    }
  };

  const getBadgeStyle = (priority) => {
    switch (priority) {
      case "P1":
        return "border border-blue-200 bg-blue-100 text-blue-500";
      default:
        return "border border-gray-300 text-gray-700";
    }
  };

  useEffect(() => {
    if (apiBasicDetails && apiBasicDetails.secondaryMembers) {
      const secondaryMember = Array.isArray(apiBasicDetails.secondaryMembers)
        ? apiBasicDetails.secondaryMembers[0]
        : apiBasicDetails.secondaryMembers;

      const secondaryConstituency = secondaryMember?.constituencyName || "";

      if (
        secondaryConstituency &&
        !constituencies.includes(secondaryConstituency)
      ) {
        setShowSecondaryConstituency(true);
      }
    }
  }, [apiBasicDetails, constituencies]);

  useEffect(() => {
    const subscription = form.watch((values) => {
      onFormChange(values);
    });
    return () => subscription.unsubscribe();
  }, [form, onFormChange]);

  const autoSave = useCallback(async () => {
    try {
      const currentValues = getValues();

      const apiData = {
        id: documentId,
        noticeHeading: currentValues.noticeHeading,
        noticePriority: currentValues.noticePriority,
        starred: currentValues.starred,
        clauses: currentValues.clauses
          .filter((clause) => clause.content.trim())
          .map((clause, index) => ({
            id: clause.id || `temp-${index}`,
            content: clause.content,
            order: index + 1,
            tags: clause.tags || [],
          })),
        constituencies: currentValues.constituencies || [],
      };

      await updateNoticeDetails(apiData).unwrap();
      previousFormValues.current = currentValues;
    } catch (error) {
      console.error("Error saving data:", error);
    } finally {
      setShowSaving(false);
    }
  }, [getValues, documentId, updateNoticeDetails]);

  useEffect(() => {
    if (apiNoticeDetails && !dataLoaded.current) {
      const formattedData = {
        noticeHeading: apiNoticeDetails.noticeHeading || "",
        noticePriority: apiNoticeDetails.noticePriority || "P1",
        starred: apiNoticeDetails.starred || false,
        clauses:
          apiNoticeDetails.clauses?.length > 0
            ? apiNoticeDetails.clauses.map((clause) => ({
                id: clause.id,
                content: clause.content,
                order: clause.order,
                tags: clause.tags || [],
              }))
            : [{ content: "", tags: [] }],
        constituencies: [],
      };

      form.reset(formattedData);
      previousFormValues.current = { ...formattedData };

      setOriginalData(formattedData);

      const primaryConstituency =
        apiNoticeDetails.primaryMember?.constituencyName || "";
      if (primaryConstituency) {
        setConstituencies([primaryConstituency]);
        setValue("constituencies", [primaryConstituency]);
      }

      dataLoaded.current = true;
    }
  }, [apiNoticeDetails, form, setValue]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const subscription = form.watch((value, { name }) => {
      if (!name) return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      setShowSaving(true);

      const timeoutId = setTimeout(() => {
        autoSave();
      }, 1000);

      setAutoSaveTimeout(timeoutId);
    });

    return () => {
      subscription.unsubscribe();
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [form, autoSaveTimeout, autoSave, getValues]);

  useEffect(() => {
    let timer;
    if (isSaving) {
      setShowSaving(true);
    } else {
      timer = setTimeout(() => {
        setShowSaving(false);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [isSaving]);

  const handleAddClause = () => {
    append({ content: "", tags: [] });
  };

  const handleRemoveConstituency = (index) => {
    const updatedConstituencies = [...constituencies];
    updatedConstituencies.splice(index, 1);
    setConstituencies(updatedConstituencies);
    setValue("constituencies", updatedConstituencies);
  };

  const handleAddSecondaryConstituency = () => {
    if (apiBasicDetails && apiBasicDetails.secondaryMembers) {
      const secondaryMember = Array.isArray(apiBasicDetails.secondaryMembers)
        ? apiBasicDetails.secondaryMembers[0]
        : apiBasicDetails.secondaryMembers;

      const secondaryConstituency = secondaryMember?.constituencyName || "";

      if (
        secondaryConstituency &&
        !constituencies.includes(secondaryConstituency)
      ) {
        const updatedConstituencies = [
          ...constituencies,
          secondaryConstituency,
        ];
        setConstituencies(updatedConstituencies);
        setValue("constituencies", updatedConstituencies);
        setShowSecondaryConstituency(false);
      }
    }
  };

  const handleRemoveTag = (clauseIndex, tagIndex) => {
    const currentTags = [
      ...watch(`${formFields.CLAUSES.name}.${clauseIndex}.tags`),
    ];
    currentTags.splice(tagIndex, 1);
    setValue(`${formFields.CLAUSES.name}.${clauseIndex}.tags`, currentTags);
  };

  const isLoading = isLoadingNoticeDetails || isLoadingBasicDetails;

  if (isLoading) {
    return <div className="p-4">Loading notice details...</div>;
  }

  return (
    <div className="p-2 ">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Read-only view (left side) */}
        <div className="w-full sm:w-1/2 p-4 rounded-md">
          <div className="mb-4 flex justify-between">
            <div className="w-40">
              <label className="block text-sm font-medium text-gray-600 mb-2">
                Priority
              </label>
              <div className="flex gap-2">
                <Badge
                  className={`px-4 py-2 w-32 rounded-full ${getBadgeStyle(
                    originalData.noticePriority,
                  )}`}
                >
                  {originalData.noticePriority}
                </Badge>
              </div>
            </div>
            <div className="flex items-center cursor-pointer mt-6">
              <span className="text-sm mr-2">
                {originalData.starred ? "Starred" : "Unstarred"}
              </span>
              {originalData.starred ? <StarredIcon /> : <UnStarredIcon />}
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {formFields.NOTICE_HEADING.label}
            </label>
            <div className="w-full border border-gray-300 rounded-lg p-3 text-sm bg-white min-h-44 mt-2 relative">
              {originalData.noticeHeading}
              <div className="absolute top-3 right-4 flex items-center">
                <CompareIcon />
              </div>
              <div className="absolute bottom-3 right-3 flex items-center gap-2">
                <CornerIcon />
                <div className="text-gray-500 text-sm">
                  <span className="text-foreground">
                    {originalData.noticeHeading?.length || 0}
                  </span>
                  <span className="text-gray-500">/500</span>
                </div>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label className="text-base font-semibold text-black leading-6 tracking-[-0.02em] font-inter mb-2 mt-10 block">
              {formFields.CLAUSES.label}
            </label>
            {originalData.clauses.map((clause, index) => (
              <div
                key={clause.id || index}
                className="mb-4 bg-white p-3 rounded-md border border-gray-200 min-h-48 relative"
              >
                <div className="relative">
                  <span className="absolute left-0 top-1 font-medium text-sm">
                    ({String.fromCharCode(65 + index)})
                  </span>
                  <p className=" ml-5 p-1 text-left text-gray-950 text-sm font-medium leading-relaxed whitespace-pre-wrap break-words">
                    {clause.content}
                  </p>
                </div>
                <div className="absolute top-3 right-2 flex items-center">
                  <CompareIcon />
                </div>
                <div className="absolute bottom-3 right-3 flex items-center gap-2">
                  <CornerIcon />
                  <div className="text-sm">
                    <span className="typography-body-text-r-12 text-foreground">
                      {clause.content?.length || 0}
                    </span>
                    <span className="typography-body-text-r-12 text-gray-500">
                      /500
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr className="hidden sm:block absolute  right-1/2 mr-2 h-[calc(160%-1rem)] border border-stone-300 opacity-30" />
        {/* Editable form (right side) */}
        <div className="w-full sm:w-1/2 relative">
          <Form {...form}>
            <form className="w-full">
              <div className="mb-4 flex justify-between">
                <FormField
                  control={control}
                  name="noticePriority"
                  render={({ field }) => (
                    <FormItem className="w-40">
                      <label className="block text-sm font-medium text-gray-600 mb-1 mt-4">
                        Priority
                      </label>
                      <div className="flex gap-2">
                        {["P1"].map((priority) => (
                          <Badge
                            key={priority}
                            className={`px-4 py-2 w-32 rounded-full cursor-pointer text-center ${
                              field.value === priority
                                ? getBadgeStyle(priority)
                                : "border border-gray-300 text-gray-700"
                            }`}
                            onClick={() => field.onChange(priority)}
                          >
                            {priority}
                          </Badge>
                        ))}
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name="starred"
                  render={({ field }) => (
                    <div
                      className="flex items-center cursor-pointer mt-6"
                      onClick={() => field.onChange(!field.value)}
                    >
                      <span className="text-sm mr-2">
                        {field.value ? "Starred" : "Unstarred"}
                      </span>
                      {field.value ? <StarredIcon /> : <UnStarredIcon />}
                    </div>
                  )}
                />
              </div>
              <div className="mb-4">
                <FormField
                  control={control}
                  name={formFields.NOTICE_HEADING.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {formFields.NOTICE_HEADING.label}
                          </label>
                          <Textarea
                            className="w-full border border-gray-300 rounded-lg p-3 text-black font-medium text-sm"
                            rows="5"
                            maxLength={500}
                            placeholder={formFields.NOTICE_HEADING.placeholder}
                            value={field.value}
                            onChange={field.onChange}
                          />
                          <div className="absolute bottom-3 right-3 flex items-center gap-2">
                            <CornerIcon />
                            <div className="text-gray-500 text-sm">
                              <span className="text-foreground">
                                {field.value?.length || 0}
                              </span>
                              <span className="text-gray-500">/500</span>
                            </div>
                          </div>
                          <div className="absolute top-8 right-3 flex items-center">
                            <CompareIcon />
                          </div>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="mt-5">
                  <span className="text-sm font-medium text-gray-700">Tag</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {constituencies.map((constituency, index) => (
                      <Badge
                        key={index}
                        className="h-8 px-3 text-gray-700"
                        onClose={() => handleRemoveConstituency(index)}
                      >
                        <div className="flex items-center">
                          <span>{constituency}</span>
                        </div>
                      </Badge>
                    ))}
                    {showSecondaryConstituency && (
                      <div
                        className="cursor-pointer"
                        onClick={handleAddSecondaryConstituency}
                      >
                        <AddFillIcon />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="mb-4">
                <div className="flex items-center justify-between mb-1">
                  <label className="text-base font-semibold text-black leading-6 tracking-[-0.02em] font-inter">
                    {formFields.CLAUSES.label}
                  </label>
                  <button
                    type="button"
                    className="flex items-center justify-center w-8 h-8 p-1 rounded-md border border-gray-300 bg-white"
                    onClick={handleAddClause}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={fields.map((field) => field.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="flex flex-col gap-2">
                      {fields.map((field, index) => (
                        <SortableClauseCard
                          key={field.id}
                          id={field.id}
                          index={index}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          onDelete={() => fields.length > 1 && remove(index)}
                          handleRemoveTag={handleRemoveTag}
                          formFields={formFields}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              </div>
              <div className="right-2 top-0 flex text-end justify-end">
                <div className="flex items-center space-x-2 rounded-md text-gray-600 p-2">
                  <Save className="h-5 w-4" />
                  <span className="text-base">
                    {showSaving ? "Saving..." : "Saved"}
                  </span>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

const SortableClauseCard = ({
  id,
  index,
  control,
  watch,
  setValue,
  onDelete,
  handleRemoveTag,
  formFields,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const letterIndex = String.fromCharCode(65 + index);
  const maxCount = 500;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative border border-gray-300 rounded-lg text-sm w-full flex justify-center p-3 bg-white mb-4"
    >
      <div
        className="absolute left-[-6px] top-1/2 -translate-y-1/2 flex items-center cursor-grab"
        {...attributes}
        {...listeners}
      >
        <div className="text-gray-700 flex w-4 h-[34px] justify-center items-center border border-gray-300 bg-white rounded-lg">
          <GripVertical size={16} className="text-border-1" />
        </div>
      </div>

      <FormField
        control={control}
        name={`${formFields.CLAUSES.name}.${index}.content`}
        render={({ field }) => (
          <div className="w-full">
            <div className="absolute top-2 left-5 right-16 flex flex-wrap gap-1 z-10">
              {watch(`${formFields.CLAUSES.name}.${index}.tags`)?.map(
                (tag, tagIndex) => (
                  <Badge
                    key={tagIndex}
                    className={`${
                      tag.title === "Partially Disallow"
                        ? "bg-pink-50 border border-pink-100 text-pink-500"
                        : tag.title === "Disallow"
                          ? " border border-gray-200 text-gray-300 "
                          : "bg-blue-50 border border-blue-100 text-blue-500"
                    } text-xs`}
                    onClose={
                      tag.title !== "Disallow"
                        ? () => handleRemoveTag(index, tagIndex)
                        : undefined
                    }
                  >
                    <div className="flex items-center gap-3  h-2">
                      <span>{tag.title}</span>
                      {tag.ruleRef && (
                        <span className="ml-1 text-cyan-600 text-sm font-semibold">
                          {tag.ruleRef}
                        </span>
                      )}
                      {tag.title === "Disallow" && (
                        <AddFillIcon className="!w-[17px] !h-[19px]" />
                      )}
                    </div>
                  </Badge>
                ),
              )}
            </div>
            <Textarea
              {...field}
              className="w-full border-none focus:outline-none resize-none ml-2 text-black text-sm p-2 px-10"
              rows="5"
              maxLength={500}
              placeholder={formFields.CLAUSES.placeholder}
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                setValue(
                  `${formFields.CLAUSES.name}.${index}.content`,
                  e.target.value,
                  {
                    shouldDirty: true,
                  },
                );
              }}
              style={{
                paddingTop:
                  watch(`${formFields.CLAUSES.name}.${index}.tags`)?.length > 0
                    ? "50px"
                    : "24px",
                paddingLeft: "32px",
              }}
            />

            <div className="absolute top-3 right-3 flex items-center gap-2">
              <CompareIcon />
              <button
                type="button"
                className="absolute right-6 text-gray-400"
                onClick={onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
            <span className="absolute top-16 left-5 text-black font-medium text-sm">
              ({letterIndex})
            </span>
            <div className="absolute bottom-3 right-3 flex items-center gap-2">
              <CornerIcon />
              <div className="text-sm">
                <span className="typography-body-text-r-12 text-foreground">
                  {field.value?.length || 0}
                </span>
                <span className="typography-body-text-r-12 text-gray-500">
                  /{maxCount}
                </span>
              </div>
            </div>
          </div>
        )}
      />
    </div>
  );
};

SortableClauseCard.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  index: PropTypes.number.isRequired,
  control: PropTypes.object.isRequired,
  watch: PropTypes.func.isRequired,
  setValue: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  handleRemoveTag: PropTypes.func.isRequired,
  formFields: PropTypes.object.isRequired,
};

NoticeDetailsEdit.propTypes = {
  onFormChange: PropTypes.func.isRequired,
};

export default NoticeDetailsEdit;
