import {
  Form,
  FormField,
  FormItem,
  FormControl,
} from "../../../components/ui/form";
import { useForm } from "react-hook-form";
import {
  DatePicker,
  Combobox,
  MultiSelectDropdown,
} from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import { useMemo, useState, useEffect, useRef, useCallback } from "react";
import { useParams } from "react-router-dom";
import {
  useGetDesignationQuery,
  useGetPortfolioQuery,
  useGetSubSubjectQuery,
} from "@/services/master-data-management/basic-details-notice";
import { Save } from "lucide-react";
import {
  useGetQuestionBasicDetailsQuery,
  useUpdateQuestionBasicDetailsMutation,
} from "@/services/notice-question-edit";
import PropTypes from "prop-types";
import { basicDetailsFormFields } from "./form-fields";
const BasicDetails = ({ onFormChange }) => {
  const { t } = useLanguage();
  const { documentId } = useParams();

  const isInitialMount = useRef(true);
  const previousFormValues = useRef({});

  const dataLoaded = useRef(false);

  const { data: apiBasicDetails, isLoading } = useGetQuestionBasicDetailsQuery(
    documentId,
    {
      skip: dataLoaded.current,
    },
  );

  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const [showSaving, setShowSaving] = useState(false);
  const formFields = basicDetailsFormFields(t);

  const [updateBasicDetails, { isLoading: isSaving }] =
    useUpdateQuestionBasicDetailsMutation();

  const form = useForm({
    mode: "onChange",
    defaultValues: {
      assembly: "",
      session: "",
      noticeNo: "",
      registrationDate: "",
      questionDate: "",
      [formFields.DESIGNATION.name]: null,
      [formFields.MINISTER_PORTFOLIO.name]: null,
      [formFields.MINISTER_SUB_SUBJECT.name]: null,
      members: [],
    },
  });

  const [displayData, setDisplayData] = useState({
    assembly: "",
    session: "",
    noticeNo: "",
    registrationDate: "",
    questionDate: "",
    designation: "",
    ministerPortfolio: "",
    ministerSubSubject: "",
    members: [],
  });

  const { data: designationData } = useGetDesignationQuery();
  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: subjectData } = useGetSubSubjectQuery();

  const designations = useMemo(() => {
    if (designationData) {
      return designationData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
    }
    return [{ label: "Agriculture", value: "2" }];
  }, [designationData]);

  const portfolios = useMemo(() => {
    if (portfolioData) {
      return portfolioData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
    }
    return [{ label: "Agriculture", value: "1" }];
  }, [portfolioData]);

  const subSubjects = useMemo(() => {
    if (subjectData) {
      return subjectData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
    }
    return [{ label: "Minister for Agriculture", value: "3" }];
  }, [subjectData]);

  const membersOptions = useMemo(() => {
    if (!apiBasicDetails) return [];

    const options = [];

    if (apiBasicDetails.primaryMember) {
      options.push({
        label: apiBasicDetails.primaryMember.memberDisplayName,
        value: apiBasicDetails.primaryMember.memberId.toString(),
        group: "Primary Member",
        subLabel: apiBasicDetails.primaryMember.constituencyName,
        imageUrl:
          apiBasicDetails.primaryMember.imageUrl || "https://placehold.co/400",
        constituency: apiBasicDetails.primaryMember.constituencyName,
      });
    }

    if (apiBasicDetails.secondaryMembers) {
      const secondaryMembersArray = Array.isArray(
        apiBasicDetails.secondaryMembers,
      )
        ? apiBasicDetails.secondaryMembers
        : [apiBasicDetails.secondaryMembers];

      secondaryMembersArray.forEach((member) => {
        if (member && member.memberDisplayName) {
          options.push({
            label: member.memberDisplayName,
            value: member.memberId?.toString() || member.memberDisplayName,
            group: "Secondary Members",
            subLabel: member.constituencyName,
            imageUrl: member.imageUrl || "https://placehold.co/400",
            constituency: member.constituencyName,
          });
        }
      });
    }

    return options;
  }, [apiBasicDetails]);

  const getLabelFromValue = (value, options) => {
    if (!value || !options) return "";
    const option = options.find((opt) => opt.value === value);
    return option ? option.label : "";
  };

  const updateDisplayDataFromAPI = useCallback(
    (apiData) => {
      if (apiData) {
        const displayMembers = [];

        if (apiData.primaryMember) {
          displayMembers.push({
            memberDisplayName: apiData.primaryMember.memberDisplayName,
            constituency: apiData.primaryMember.constituencyName,
          });
        }

        if (apiData.secondaryMembers) {
          const secondaryMembersArray = Array.isArray(apiData.secondaryMembers)
            ? apiData.secondaryMembers
            : [apiData.secondaryMembers];

          secondaryMembersArray.forEach((member) => {
            if (member && member.memberDisplayName) {
              displayMembers.push({
                memberDisplayName: member.memberDisplayName,
                constituency: member.constituencyName,
              });
            }
          });
        }

        setDisplayData({
          assembly: apiData.assembly?.toString() || "",
          session: apiData.session?.toString() || "",
          noticeNo: apiData.noticeNumber || "",
          registrationDate: apiData.dateOfRegistration || "",
          questionDate: apiData.questionDate || "",
          designation: getLabelFromValue(
            apiData.ministerDesignationId?.toString(),
            designations,
          ),
          ministerPortfolio: getLabelFromValue(
            apiData.portfolioId?.toString(),
            portfolios,
          ),
          ministerSubSubject: getLabelFromValue(
            apiData.subSubjectId?.toString(),
            subSubjects,
          ),
          members: displayMembers,
        });
      }
    },
    [designations, portfolios, subSubjects],
  );

  useEffect(() => {
    if (apiBasicDetails && !dataLoaded.current) {
      const memberIds = [];

      if (apiBasicDetails.primaryMember) {
        memberIds.push(apiBasicDetails.primaryMember.memberId.toString());
      }

      if (apiBasicDetails.secondaryMembers) {
        const secondaryMembersArray = Array.isArray(
          apiBasicDetails.secondaryMembers,
        )
          ? apiBasicDetails.secondaryMembers
          : [apiBasicDetails.secondaryMembers];

        secondaryMembersArray.forEach((member) => {
          if (member && member.memberId) {
            memberIds.push(member.memberId.toString());
          }
        });
      }

      const formattedData = {
        assembly: apiBasicDetails.assembly?.toString() || "",
        session: apiBasicDetails.session?.toString() || "",
        noticeNo: apiBasicDetails.noticeNumber || "",
        registrationDate: apiBasicDetails.dateOfRegistration || "",
        questionDate: apiBasicDetails.questionDate || "",
        [formFields.DESIGNATION.name]:
          apiBasicDetails.ministerDesignationId?.toString() || null,
        [formFields.MINISTER_PORTFOLIO.name]:
          apiBasicDetails.portfolioId?.toString() || null,
        [formFields.MINISTER_SUB_SUBJECT.name]:
          apiBasicDetails.subSubjectId?.toString() || null,
        members: memberIds,
      };

      previousFormValues.current = { ...formattedData };

      form.reset(formattedData);
      updateDisplayDataFromAPI(apiBasicDetails);

      dataLoaded.current = true;
    }
  }, [
    apiBasicDetails,
    form,
    designations,
    portfolios,
    subSubjects,
    formFields,
    updateDisplayDataFromAPI,
  ]);

  const autoSave = useCallback(
    async (formValues) => {
      try {
        if (!documentId) return;

        const hasChanges = Object.keys(formValues).some((key) => {
          if (Array.isArray(formValues[key])) {
            if (!Array.isArray(previousFormValues.current[key])) return true;
            if (
              formValues[key].length !== previousFormValues.current[key].length
            )
              return true;
            return formValues[key].some(
              (val, idx) => val !== previousFormValues.current[key][idx],
            );
          }
          return formValues[key] !== previousFormValues.current[key];
        });

        if (!hasChanges) return;

        const apiData = {
          id: documentId,
          assembly: parseInt(formValues.assembly) || 0,
          session: parseInt(formValues.session) || 0,
          questionDate: formValues.questionDate || "",
          dateOfRegistration: formValues.registrationDate || "",
          ministerDesignationId:
            parseInt(formValues[formFields.DESIGNATION.name]) || 0,
          portfolioId:
            parseInt(formValues[formFields.MINISTER_PORTFOLIO.name]) || 0,
          subSubjectId:
            parseInt(formValues[formFields.MINISTER_SUB_SUBJECT.name]) || 0,
          primaryMember: {
            memberId: parseInt(formValues.members[0]) || 0,
          },
          secondaryMembers: formValues.members.slice(1).map((memberId) => ({
            memberId: parseInt(memberId) || 0,
          })),
        };

        await updateBasicDetails(apiData).unwrap();

        previousFormValues.current = { ...formValues };
      } catch (error) {
        console.error("Error saving data:", error);
      }
    },
    [documentId, formFields, updateBasicDetails],
  );

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const subscription = form.watch((value, { type }) => {
      if (type === "all") return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      if (Object.keys(value).length > 0) {
        const timeoutId = setTimeout(() => {
          autoSave(value);
        }, 1000);

        setAutoSaveTimeout(timeoutId);
      }
    });

    return () => {
      subscription.unsubscribe();
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [form, autoSaveTimeout, documentId, formFields, autoSave]);

  useEffect(() => {
    const subscription = form.watch((values) => {
      onFormChange(values);
    });
    return () => subscription.unsubscribe();
  }, [form, onFormChange]);
  useEffect(() => {
    let timer;

    if (isSaving) {
      setShowSaving(true);
    } else {
      timer = setTimeout(() => {
        setShowSaving(false);
      }, 1000);
    }

    return () => clearTimeout(timer);
  }, [isSaving]);

  if (isLoading) {
    return <div className="p-4">Loading basic details...</div>;
  }

  if (!apiBasicDetails) {
    return <div className="p-4">Question not found with ID: {documentId}</div>;
  }

  return (
    <div className="p-2 ">
      <div className="relative flex flex-col sm:flex-row">
        <div className="w-full space-y-6 rounded-md sm:w-1/2">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                KLA
              </div>
              <div className="font-medium">{displayData.assembly}</div>
            </div>
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Session
              </div>
              <div className="font-medium">{displayData.session}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Notice No.:
              </div>
              <div className="font-medium">{displayData.noticeNo}</div>
            </div>
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Date of Registration
              </div>
              <div className="font-medium">{displayData.registrationDate}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Question Date
              </div>
              <div className="font-medium">{displayData.questionDate}</div>
            </div>
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Designation
              </div>
              <div className="font-medium">{displayData.designation}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Minister Portfolio
              </div>
              <div className="font-medium">{displayData.ministerPortfolio}</div>
            </div>
            <div>
              <div className="mb-1 text-gray-600 typography-body-text-r-14">
                Minister Sub Subject
              </div>
              <div className="font-medium">
                {displayData.ministerSubSubject}
              </div>
            </div>
          </div>

          <div>
            <div className="mb-2 text-gray-600 typography-body-text-r-14">
              Name Of Members
            </div>
            <div className="flex gap-2">
              <div className="flex flex-wrap gap-2 mt-3">
                {displayData.members.map((member, index) => (
                  <div
                    key={index}
                    className="flex items-center px-3 py-1 bg-white border border-gray-200 rounded-full"
                  >
                    <img
                      src={`/mdm-service/api/minister/${member.id}/photo`}
                      alt={member.memberDisplayName}
                      className="object-cover w-6 h-6 mr-2 border rounded-full"
                    />
                    <span className="text-sm">{member.memberDisplayName}</span>
                    <span className="ml-1 text-xs text-gray-500">
                      - {member.constituency}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <hr className="absolute hidden h-full ml-56 border sm:block left-1/3 border-stone-300 opacity-30" />
        <div className="w-full sm:w-1/2">
          <Form {...form}>
            <form className="w-full">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* KLA */}
                <FormField
                  control={form.control}
                  name="assembly"
                  render={({ fieldState, field }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          {...field}
                          className="w-full"
                          label={t("KLA")}
                          options={[{ label: "15", value: "15" }]}
                          placeholder={t("Select")}
                          error={fieldState?.error?.message}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Session */}
                <FormField
                  control={form.control}
                  name="session"
                  render={({ fieldState, field }) => (
                    <FormItem>
                      <Combobox
                        {...field}
                        className="w-full"
                        label={t("Session")}
                        options={[{ label: "13", value: "13" }]}
                        placeholder={t("Select")}
                        error={fieldState?.error?.message}
                      />
                    </FormItem>
                  )}
                />

                {/* Question Date */}
                <FormField
                  control={form.control}
                  name="questionDate"
                  render={({ field }) => (
                    <FormItem>
                      <DatePicker
                        {...field}
                        label={t("Question Date")}
                        placeholder={t("Select Date")}
                        onChange={field.onChange}
                        className="w-full"
                        disabled={true}
                      />
                    </FormItem>
                  )}
                />

                {/* Designation */}
                <FormField
                  control={form.control}
                  name={formFields.DESIGNATION.name}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          {...field}
                          className="w-full"
                          label={formFields.DESIGNATION.label}
                          options={designations}
                          placeholder={formFields.DESIGNATION.placeholder}
                          onChange={field.onChange}
                          disabled={true}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Minister Portfolio */}
                <FormField
                  control={form.control}
                  name={formFields.MINISTER_PORTFOLIO.name}
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          {...field}
                          className="w-full"
                          label={formFields.MINISTER_PORTFOLIO.label}
                          value={field.value}
                          onValueChange={field.onChange}
                          options={portfolios}
                          placeholder={
                            formFields.MINISTER_PORTFOLIO.placeholder
                          }
                          error={fieldState?.error?.message}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Minister Sub Subject */}
                <FormField
                  control={form.control}
                  name={formFields.MINISTER_SUB_SUBJECT.name}
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <Combobox
                          {...field}
                          className="w-full"
                          label={formFields.MINISTER_SUB_SUBJECT.label}
                          value={field.value}
                          onValueChange={field.onChange}
                          options={subSubjects}
                          placeholder={
                            formFields.MINISTER_SUB_SUBJECT.placeholder
                          }
                          error={fieldState?.error?.message}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Members */}
                <FormField
                  control={form.control}
                  name="members"
                  render={({ field }) => (
                    <FormItem className="mt-3 md:col-span-2">
                      <label className="text-gray-600 typography-body-text-m-14 ">
                        {t("Name of Member(s)")}
                      </label>
                      <MultiSelectDropdown
                        options={membersOptions}
                        onSelectionChange={(selected) => {
                          field.onChange(selected.map((m) => m.value));
                          setDisplayData((prev) => ({
                            ...prev,
                            members: selected.map((member) => ({
                              memberDisplayName: member.label,
                              constituency: member.constituency,
                              imageUrl: member.imageUrl,
                            })),
                          }));
                        }}
                        selectedValues={
                          field.value
                            ? membersOptions.filter((opt) =>
                                field.value.includes(opt.value),
                              )
                            : []
                        }
                        addLabel="Add Members"
                        maxCount={3}
                      />
                    </FormItem>
                  )}
                />
              </div>
              <div className="top-0 flex justify-end right-2 text-end">
                <div className="flex items-center p-2 space-x-2 text-gray-600 rounded-md">
                  <Save className="w-4 h-5" />
                  <span className="text-base">
                    {showSaving ? "Saving..." : "Saved"}
                  </span>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};
BasicDetails.propTypes = {
  onFormChange: PropTypes.func.isRequired,
};
export default BasicDetails;
