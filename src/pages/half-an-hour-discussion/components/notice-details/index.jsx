import { AccordionTitle } from "@/components/accordion-title";
import { Form, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import CornerIcon from "@/icons/corner-icon";
import { useSaveNoticeDetailsMutation } from "@/services/half-an-hour-discussion";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  ExpandableContent,
  ExpandableTrigger,
  RichTextEditor,
  Textarea,
  toast,
} from "@kla-v2/ui-components";
import { Expand, Save } from "lucide-react";
import PropTypes from "prop-types";
import { useImperativeHandle, useState, forwardRef } from "react";
import { useForm } from "react-hook-form";
import { noticeFormSchema } from "./form.schema";
import { useParams } from "react-router-dom";

const NoticeDetails = forwardRef(function NoticeDetailsComponent(
  { accordionOrderNo, noticeDetails, openNext },
  ref,
) {
  const { t } = useLanguage();
  const [saveNoticeDetails, { isLoading }] = useSaveNoticeDetailsMutation();
  const [noticeText, setNoticeText] = useState("");
  const { documentId } = useParams();

  const form = useForm({
    mode: "onSubmit",
    resolver: zodResolver(noticeFormSchema(t)),
    defaultValues: {
      noticeHeading: noticeDetails?.noticeHeading ?? "",
      description: noticeDetails?.description ?? "",
    },
  });
  const { getValues } = form;

  useImperativeHandle(
    ref,
    () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
    }),
    [form],
  );

  const showTick = shouldShowTick(form);

  const handleNoticeTextChange = (text) => {
    const words = text.trim().split(/\s+/);
    setNoticeText(words.length <= 100 ? text : words.slice(0, 100).join(" "));
  };

  const handleHeadingChange = (e, field) => {
    const value = e.target.value;
    handleNoticeTextChange(value);
    field.onChange(value);
    form.trigger("noticeHeading");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const values = getValues();
    const updatedValues = {
      id: documentId,
      noticeHeading: values.noticeHeading,
      description: values.description,
    };

    try {
      const response = await saveNoticeDetails({
        documentId,
        data: updatedValues,
      }).unwrap();

      // Format the response to ensure proper handling of rich text data
      const formattedResponse = {
        ...response,
        description:
          typeof response.description === "object"
            ? JSON.stringify(response.description)
            : response.description,
      };

      resetFormWithResponseData(form, noticeFormSchema(t), formattedResponse);
      toast.success(t("toast.documentSave"));
      openNext();
    } catch (error) {
      toast.error(error?.message);
    }
  };

  return (
    <>
      <ExpandableTrigger
        showTick={showTick}
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle={t("noticeDetails")}
          isColored
        />
      </ExpandableTrigger>

      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            <Form className="w-full">
              <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                <FormField
                  control={form.control}
                  name="noticeHeading"
                  render={({ field, fieldState }) => {
                    const error = fieldState.error;
                    return (
                      <FormItem className="flex flex-col">
                        <div className="relative mt-1">
                          <Textarea
                            placeholder="Enter Notice Heading"
                            label="Notice Heading"
                            aria-label="Notice Heading"
                            className="border border-gray-300 rounded-lg p-3 text-black font-bold text-sm focus:outline-none focus:border-blue-500 resize-none w-full"
                            rows={4}
                            {...field}
                            value={field.value}
                            onChange={(e) => handleHeadingChange(e, field)}
                          />
                          <div className="absolute bottom-3 right-3 flex items-center gap-2">
                            <CornerIcon />
                            <span className="text-gray-500 text-sm">
                              <span className="typography-body-text-m-14 text-gray-800">
                                {noticeText.length}
                              </span>
                              /100
                            </span>
                          </div>
                        </div>
                        {error && (
                          <p className="typography-body-text-r-14 text-error">
                            {error?.message}
                          </p>
                        )}
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => {
                    const error = fieldState.error;
                    return (
                      <FormItem className="flex flex-col gap-2">
                        <label
                          htmlFor="description"
                          className="typography-body-text-m-14 text-gray-700"
                        >
                          {t("description")}
                        </label>
                        <RichTextEditor
                          aria-label="description"
                          defaultValue={field.value}
                          fullScreenControl={
                            <div className="p-2.5 h-full">
                              <Expand className="size-4" />
                            </div>
                          }
                          onUpdate={(editor) => {
                            const html = editor.getHTML();
                            field.onChange(html);
                            form.trigger("description");
                          }}
                        />
                        {error && (
                          <p className="typography-body-text-r-14 text-error">
                            {error.message}
                          </p>
                        )}
                      </FormItem>
                    );
                  }}
                />

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    variant="secondary"
                    icon={Save}
                    iconPosition="right"
                    disabled={isLoading}
                  >
                    {t("save")}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </ExpandableContent>
    </>
  );
});

NoticeDetails.displayName = "NoticeDetailsPage";

NoticeDetails.propTypes = {
  accordionOrderNo: PropTypes.number,
  noticeDetails: PropTypes.object,
  openNext: PropTypes.func.isRequired,
  refetchDocument: PropTypes.func.isRequired,
};

export { NoticeDetails };
