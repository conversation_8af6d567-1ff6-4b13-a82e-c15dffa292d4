import { z } from "zod";

export const BasicDetailsSchema = (t) =>
  z.object({
    ministerDesignationId: z.preprocess(
      (value) => (value ? String(value) : value),
      z
        .string({
          invalid_type_error: t("validation:ministerDesignationRequired2"),
        })
        .min(1, { message: t("validation:ministerDesignationRequired2") }),
    ),
    questionCategory: z
      .string({ message: t("validation:questionCategoryRequired") })
      .min(1, { message: t("validation:questionCategoryRequired") }),
    discussionDate: z
      .string()
      .min(1, { message: t("validation:discussionDateRequired") }),
    questionDate: z
      .string()
      .min(1, { message: t("validation:questionDateRequired") }),
    questionId: z
      .string({ message: t("validation:questionNumberRequired") })
      .min(1, { message: t("validation:questionNumberRequired") }),
    place: z.string().min(1, { message: t("validation:placeRequired") }),
    placeInLocal: z.string().optional(),
  });
