const getHandlerHalfAnHourTemplate = ({
  templateValues,
  explanatoryDetails,
}) => {
  return `

  <div style="font-family: 'Noto Sans Malayalam', sans-serif; line-height: 1.5; padding: 20px;  #ddd; width: 100%;">
<div class="flex flex-col gap-y-2">
        <h2 class="text-center typography-body-text-s-16 text-foreground leading-snug">
          കേരള നിയമസഭ <br /> അടിയന്തര ചോദ്യം
        </h2>
      </div>
      <div class="flex flex-col gap-y-2 items-end mt-7">
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">തീയതി:</span>
          <span class="typography-body-text-s-14 text-foreground">
            ${explanatoryDetails?.questionDate || ""}
          </span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">സ്ഥലം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.place || ""}
          </span>
        </div>
      </div>

      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-y-4">
          <div class="flex gap-2 mt-2">
            <span class="typography-body-text-s-14">
                     ${
                       explanatoryDetails?.member?.memberDisplayNameInLocal ||
                       ""
                     }
</span>
            <span class="typography-body-text-r-14 text-foreground">
              എം എൽ എ
            </span>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="typography-body-text-r-14">
            കേരള നിയമസഭാ സെക്രട്ടറിക്ക്,
          </span>
          <span class="typography-body-text-r-14">തിരുവനതപുരം.</span>
        </div>



          <div class="flex flex-col gap-4 mt-6">

        <div class="typography-body-text-r-14">
          <span>
         ${explanatoryDetails?.noticeHeading || ""}
          </span>
        </div>
        <div class="typography-body-text-r-14 flex gap-1">
          <span>
          ${explanatoryDetails?.description || ""}
          </span>
        </div>
         <div class="typography-body-text-r-14 flex gap-1">
         <span>${
           explanatoryDetails.explanatoryNote ?? templateValues ?? ""
         }</span>
      </div>

      </div>
    </div>
      </div>

      <div class="flex flex-col gap-y-2 items-end">
        <div class="flex">
          <span class="typography-body-text-r-14">വിശ്വസ്ഥതയോടെ</span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">അംഗം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.member?.memberDisplayNameInLocal || ""}
          </span>
        </div>
        <div class="flex gap-2">
          <span class="typography-body-text-r-14">നിയോജകമണ്ഡലം:</span>
          <span class="typography-body-text-s-14 text-foreground">
          ${explanatoryDetails?.member?.constituencyNameInLocal || ""}
          </span>
        </div>
      </div>




  </div>
`;
};

export default getHandlerHalfAnHourTemplate;
