import { AccordionTitle } from "@/components/accordion-title";
import { Form, FormField, FormItem } from "@/components/ui/form";
import { useDebounce, useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  ExpandableContent,
  ExpandableTrigger,
  RichTextEditor,
  toast,
} from "@kla-v2/ui-components";
import { Expand, Save } from "lucide-react";
import PropTypes from "prop-types";
import { useForm } from "react-hook-form";
import { useParams } from "react-router-dom";
import { useState } from "react";
import { useMemo } from "react";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";
import { ExplanatoryFormSchema } from "./form-schema";
import { HalfAnHourRichTextPage } from "./half-an-hour-rich-text";
import {
  useGetHalfAnHourDiscussionQuery,
  useSaveExplanatoryNoteMutation,
} from "@/services/half-an-hour-discussion";
import { forwardRef } from "react";
import { useImperativeHandle } from "react";
const ExplanatoryNote = forwardRef(
  ({ accordionOrderNo, explanatoryDetails, openNext }, ref) => {
    const { t } = useLanguage();
    const [postExplanatoryDetails, { isLoading }] =
      useSaveExplanatoryNoteMutation();
    const [content, setContent] = useState("");
    const textDebounced = useDebounce(content);
    const { documentId } = useParams();
    const { data: halfAnHourNoticeData } =
      useGetHalfAnHourDiscussionQuery(documentId);

    const form = useForm({
      mode: "onSubmit",
      resolver: zodResolver(ExplanatoryFormSchema(t)),
      defaultValues: {
        explanatoryNote: explanatoryDetails?.explanatoryNote ?? "",
        hasDigitalSignature: false,
      },
    });
    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
    }));
    const {
      control,
      formState: { errors },
      getValues,
    } = form;

    useMemo(async () => {
      if (textDebounced) {
        const updatedData = {
          explanatoryNote: textDebounced,
          id: documentId,
        };

        await postExplanatoryDetails({
          documentId,
          data: updatedData,
        }).unwrap();
      }
    }, [textDebounced, documentId]);

    const showTick = shouldShowTick(form);
    const handlePartialSave = async (e) => {
      e.preventDefault();

      const data = getValues();
      const updatedData = {
        explanatoryNote: data?.explanatoryNote,
        hasDigitalSignature: data?.hasDigitalSignature,
        id: documentId,
      };
      try {
        const response = await postExplanatoryDetails({
          documentId,
          data: updatedData,
        }).unwrap();
        resetFormWithResponseData(form, ExplanatoryFormSchema(t), response);
        toast.success(t("toast.documentSave"));
        openNext();
      } catch (error) {
        toast.error(error?.message);
      }
    };
    const htmlString = form?.watch("explanatoryNote");

    return (
      <>
        <ExpandableTrigger
          className="text-primary font-semibold text-base"
          variant="secondary"
          showTick={showTick}
        >
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle="Explanatory Note"
            isColored={true}
          />
        </ExpandableTrigger>
        <ExpandableContent>
          <div className="w-full flex flex-col">
            <div className="flex flex-col gap-4 p-6">
              <Form className="w-full">
                <form onSubmit={(e) => handlePartialSave(e)}>
                  <div className="lg:grid lg:grid-cols-5 gap-4 min-h-[500px]">
                    <div className="flex flex-col col-span-3 h-full">
                      <FormField
                        control={control}
                        name="explanatoryNote"
                        render={({ field }) => (
                          <FormItem className="mb-2 h-full [&>div]:h-full relative">
                            <RichTextEditor
                              fullScreenControl={
                                <div className="p-2.5 h-full">
                                  <Expand className="size-4" />
                                </div>
                              }
                              {...field}
                              defaultValue={field.value}
                              onChange={() => {}}
                              onUpdate={(editor) => {
                                const html = editor.getHTML();
                                field.onChange(html);
                                setContent(html);
                              }}
                            />
                            {isLoading && (
                              <div className="absolute  !h-auto bottom-2 right-2 typography-body-text-r-12 text-gray-500">
                                Saving...
                              </div>
                            )}
                          </FormItem>
                        )}
                      />

                      {errors.explanatoryNote && (
                        <p className="h-4 typography-body-text-r-14 text-error my-2">
                          {errors.explanatoryNote.message}
                        </p>
                      )}
                    </div>
                    <div className="col-span-2 w-full mb-2">
                      <HalfAnHourRichTextPage
                        templateValues={htmlString}
                        explanatoryDetails={halfAnHourNoticeData}
                      />
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name="digitalSignature"
                    render={({ field, fieldState }) => (
                      <FormItem className="md:col-span-1">
                        <Checkbox
                          {...field}
                          label={t("form:digitalSignature")}
                          error={fieldState.error?.message}
                          placeholder={t("placeholder:Place")}
                        />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      variant="secondary"
                      className="flex items-center"
                    >
                      {t("save")} <Save size={16} />
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </ExpandableContent>
      </>
    );
  },
);
ExplanatoryNote.displayName = "ExplanatoryNote";

ExplanatoryNote.propTypes = {
  accordionOrderNo: PropTypes.number,
  explanatoryDetails: PropTypes.object,
  openNext: PropTypes.func,
};

export { ExplanatoryNote };
