import { renderApp } from "@/testing/utils";
import { ExpandableAccordion, ExpandableItem } from "@kla-v2/ui-components";
import { screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { ExplanatoryNote } from ".";

describe("Explanatory Note Accordion", () => {
  const openNext = vi.fn();

  const renderComponent = (data) =>
    renderApp(
      <ExpandableAccordion type="multiple">
        <ExpandableItem value="explanatory-details">
          <ExplanatoryNote
            accordionOrderNo={2}
            explanatoryDetails={data}
            openNext={openNext}
          />
        </ExpandableItem>
      </ExpandableAccordion>,
    );

  it("renders the accordion with the correct title", async () => {
    await renderComponent();
    expect(screen.getByText("Explanatory Note")).toBeInTheDocument();
  });
});
