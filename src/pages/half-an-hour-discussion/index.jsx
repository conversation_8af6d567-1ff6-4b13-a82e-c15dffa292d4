import { DocumentMetadata } from "@/components/document-metadata";
import { useAccordion, useLanguage } from "@/hooks";
import { useSubmitHalfanHourDiscussionMutation } from "@/services/discussion";
import {
  Button,
  ExpandableAccordion,
  ExpandableItem,
  toast,
} from "@kla-v2/ui-components";
import { ArrowLeft, ArrowRightIcon } from "lucide-react";
import { useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { useContextualNavigation } from "@/utils/navigation";
import { ROUTE_CONFIG } from "@/config/routes";
import { BasicDetailsPage } from "./components/basic-details";
import { ExplanatoryNote } from "./components/explanatory-note";
import { NoticeDetails } from "./components/notice-details";
import PreviewDialog from "./components/preview";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useLoaderData } from "react-router-dom";

function CreateDiscussion() {
  const { t } = useLanguage();
  const documentName = t("Create Half an Hour Discussion");
  const { documentId } = useParams();
  const { navigate } = useContextualNavigation();
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const { documentData } = useLoaderData();
  const [submitHalfanHourDiscussion] = useSubmitHalfanHourDiscussionMutation();
  const accordions = ["item-1", "item-2", "item-3"];
  const initialAccordion = { accordions, openAccordions: [accordions[0]] };
  const { accordionValue, setAccordionValue, openNextAccordion } =
    useAccordion(initialAccordion);

  const basicDetailsRef = useRef();
  const noticeDetailsRef = useRef();
  const explanatoryNoteRef = useRef();

  useAutoBreadcrumb();

  const formatCreatedOn = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    const options = { year: "numeric", month: "numeric", day: "numeric" };
    return date.toLocaleDateString(undefined, options);
  };

  const amendedMetadata = {
    ...documentData,
    createdOn: formatCreatedOn(documentData?.createdAt),
    createdBy: documentData?.createdBy || "",
    isDocTypeAdded: true,
    documentType: "Half an Hour Discussion (Rule 49)",
    noticeType: "Half an Hour Discussion",
  };

  const goBack = () => {
    navigate(ROUTE_CONFIG.MEMBER.MY_NOTICES);
  };

  const handleFinalSubmit = async () => {
    try {
      await submitHalfanHourDiscussion({
        documentId,
      }).unwrap();
      toast.success(t("Submitted Successfully"));
      navigate(ROUTE_CONFIG.MEMBER.MY_NOTICES);
    } catch (error) {
      toast.error(error?.message);
    }
  };

  const handleValidation = async () => {
    const invalidAccordions = [];

    const [isBasicDetailsValid, isNoticeDetailsValid, isExplanatoryNoteValid] =
      await Promise.all([
        await basicDetailsRef.current.validateAndSubmit(true),
        await noticeDetailsRef.current.validateAndSubmit(true),
        await explanatoryNoteRef.current.validateAndSubmit(true),
      ]);
    if (!isBasicDetailsValid) invalidAccordions.push("item-1");
    if (!isNoticeDetailsValid) invalidAccordions.push("item-2");
    if (!isExplanatoryNoteValid) invalidAccordions.push("item-3");
    if (invalidAccordions.length > 0) {
      setAccordionValue([...invalidAccordions]);
      return;
    }
    setShowPreviewDialog(true);
  };

  return (
    <div className="px-5 py-3 bg-background flex flex-col min-h-screen">
      <div className="flex justify-between">
        <h1 className="typography-page-heading">{documentName}</h1>
      </div>
      <div className="w-full py-5">
        <DocumentMetadata documentMetadata={amendedMetadata} />
      </div>
      <ExpandableAccordion
        type="multiple"
        value={accordionValue}
        onValueChange={setAccordionValue}
        className="w-full flex flex-col gap-4 flex-grow"
      >
        <ExpandableItem value={`item-1`}>
          <BasicDetailsPage
            accordionOrderNo={1}
            Metadata={amendedMetadata}
            ref={basicDetailsRef}
            basicDetailsData={documentData}
            openNext={() => openNextAccordion(accordions[0])}
          />
        </ExpandableItem>
        <ExpandableItem value={`item-2`}>
          <NoticeDetails
            accordionOrderNo={2}
            noticeDetails={documentData}
            ref={noticeDetailsRef}
            openNext={() => openNextAccordion(accordions[1])}
          />
        </ExpandableItem>
        <ExpandableItem value={`item-3`}>
          <ExplanatoryNote
            accordionOrderNo={3}
            explanatoryDetails={documentData}
            ref={explanatoryNoteRef}
            openNext={() => openNextAccordion(accordions[2])}
          />
        </ExpandableItem>
      </ExpandableAccordion>

      <div className="flex justify-end items-center gap-4 py-4 bg-background">
        <Button
          variant="neutral"
          className="flex items-center gap-3 text-black border border-gray-200 rounded-md"
          onClick={goBack}
          icon={ArrowLeft}
          iconPosition="left"
        >
          {t("back")}
        </Button>
        <Button
          icon={ArrowRightIcon}
          iconPosition="right"
          size="lg"
          variant="primary"
          type="submit"
          onClick={handleValidation}
        >
          {t("submit")}
        </Button>

        {showPreviewDialog && (
          <PreviewDialog
            isOpen={showPreviewDialog}
            onClose={() => setShowPreviewDialog(false)}
            onConfirm={handleFinalSubmit}
            documentMetadata={amendedMetadata}
            documentId={documentId}
          />
        )}
      </div>
    </div>
  );
}

export { CreateDiscussion };
