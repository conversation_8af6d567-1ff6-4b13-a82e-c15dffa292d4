/**
 * Standardized tag types for RTK Query caching
 * Using PascalCase for consistency
 */
export const TAG_TYPES = {
  // Document types
  DOCUMENTS: "Documents",
  NOTICE_FOR_QUESTION: "NoticeForQuestion",
  QUESTION_NOTICE: "QuestionNotice",
  NOTICE_BANK: "NoticeBank",
  BALLOT: "Ballot",
  SCHEDULE_OF_ACTIVITY: "ScheduleOfActivity",
  ALLOTMENT_OF_DAYS: "AllotmentOfDays",
  LATE_ANSWER_BULLETIN: "LateAnswerBulletin",
  DELAYED_ANSWER_BULLETIN: "DelayedAnswerBulletin",
  SHORT_NOTICE: "ShortNotice",
  STARRED_QUESTIONS: "StarredQuestions",
  UNSTARRED_QUESTIONS: "UnstarredQuestions",
  ANSWER_STATUS_REPORT: "AnswerStatusReport",
  EXPLANATORY_NOTE: "ExplanatoryNote",
  DELAY_STATEMENT_LIST: "DelayStatementList",

  // MDM data types
  ASSEMBLY: "Assembly",
  MINISTERS: "Ministers",
  BASIC_DETAILS: "BasicDetails",
  DESIGNATION: "Designation",
  PORTFOLIOS: "Portfolios",
  SUB_SUBJECTS: "SubSubjects",
  MEMBERS: "Members",
  PARTIES: "Parties",
  PLACES_LIST: "PlacesList",
  CALENDAR_OF_SITTINGS: "CalendarOfSittings",
  USER_PROFILE: "UserProfile",
  CONSENT: "Consent",
};

/**
 * Base URL environment variable names
 */
export const BASE_URLS = {
  API: "VITE_API_BASE_URL",
  MDM: "VITE_MDM_SERVICE_BASE_URL",
  MDMS: "VITE_MDMS_API_BASE_URL",
  BASE: "BASE_URL",
};

/**
 * Standardized reducer path prefixes
 */
export const REDUCER_PREFIXES = {
  API: "api",
  MDM: "mdm",
  DOCUMENTS: "documents",
};
