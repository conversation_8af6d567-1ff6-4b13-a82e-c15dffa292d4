/**
 * Centralized route configuration - Single source of truth for all routes
 * Uses factory patterns to derive configurations from base constants
 */

// Context definitions
export const CONTEXTS = {
  SECTION: 'section',
  MEMBER: 'member',
  PPO: 'ppo',
};

// Standard actions that can be performed on resources
export const ACTIONS = {
  VIEW: 'view',
  EDIT: 'edit',
  CREATE: 'create',
};

// Base document type constants
const DOCUMENT_TYPE_KEYS = [
  'MINISTER_DESIGNATION_GROUP',
  'ALLOTMENT_OF_DAYS',
  'SCHEDULE_OF_ACTIVITY',
  'DELAYED_ANSWER_BULLETIN',
  'ANSWER_STATUS_REPORT',
  'DELAY_STATEMENT_LIST',
  'LATE_ANSWER_BULLETIN',
  'CORRECTION_OF_ANSWER',
];

// Base notice type constants
const NOTICE_TYPE_KEYS = [
  'NOTICE_FOR_SHORT_NOTICE',
  'NOTICE_FOR_HALF_AN_HOUR_DISCUSSION',
  'NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS',
  'NOTICE_FOR_QUESTION',
];

/**
 * Factory functions to generate configurations from base constants
 */

// Convert SNAKE_CASE to kebab-case
const toKebabCase = (str) => str.toLowerCase().replace(/_/g, '-');

// Convert SNAKE_CASE to Title Case
const toTitleCase = (str) => {
  return str
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Factory to create type objects with key, slug, and label
const createTypeObject = (key) => ({
  key,
  slug: toKebabCase(key),
  label: toTitleCase(key),
});

// Generate document types from base constants
export const DOCUMENT_TYPES = DOCUMENT_TYPE_KEYS.reduce((acc, key) => {
  acc[key] = createTypeObject(key);
  return acc;
}, {});

// Generate notice types from base constants
export const NOTICE_TYPES = NOTICE_TYPE_KEYS.reduce((acc, key) => {
  acc[key] = createTypeObject(key);
  return acc;
}, {});

// Base route structure using factory pattern
const createContextRoutes = (context) => ({
  BASE: `/${context}`,
  CONSENT: `/${context}/consent`,
  MY_NOTICES: `/${context}/my-notices`,
  MY_QUESTION_NOTICES: `/${context}/my-question-notices`,
  ...(context === 'member' && {
    MY_QUESTION: `/${context}/my-question`,
    MY_QUESTION_NOTICES_NOTICE_BANK: `/${context}/my-question-notices/notice-bank`,
  }),
  ...(context === 'section' && {
    DOCUMENTS: `/${context}/documents`,
    QUESTION_EDIT_LIST: `/${context}/question-edit-list`,
    QUESTION_EDIT_LIST_ALL: `/${context}/question-edit-list/all`,
    QUESTION_EDIT_LIST_ACTION_REQUIRED: `/${context}/question-edit-list/action-required`,
    BALLOT: {
      BASE: `/${context}/ballot`,
      LIST: `/${context}/ballot/list`,
      PERFORM: `/${context}/ballot/perform`,
    },
    SETTING_STARRED_QUESTIONS_LIST: `/${context}/setting-starred-questions`,
    SETTING_UNSTARRED_QUESTIONS_LIST: `/${context}/setting-of-unstarred-questions`,
    SETTING_UNSTARRED_QUESTIONS_ALL: `/${context}/setting-of-unstarred-questions/all`,
    NOTICES_FOR_QUESTION: `/${context}/notices-for-question`,
  }),
});

export const ROUTE_CONFIG = {
  HOME: "/",
  SECTION: createContextRoutes(CONTEXTS.SECTION),
  MEMBER: createContextRoutes(CONTEXTS.MEMBER),
  PPO: createContextRoutes(CONTEXTS.PPO),
  DEMO_POPUP: "/demo-pop-up",
};

/**
 * Route builder factory - generates actual routes using consistent patterns
 * Pattern: /{context}/{resourceType}/:id/{action}
 */
const createRouteBuilder = (context, resourceType) => {
  return (id, action = null) => {
    let route = `/${context}/${resourceType}`;
    if (id) route += `/${id}`;
    if (action) route += `/${action}`;
    return route;
  };
};

const getCurrentContext = () => {
  if (typeof window === 'undefined') return CONTEXTS.MEMBER;
  const pathname = window.location.pathname;
  return Object.values(CONTEXTS).find(context =>
    pathname.includes(`/${context}/`)
  ) || CONTEXTS.SECTION;
};

/**
 * Generic route builder - Clean factory-based approach
 * Pattern: /{context}/{resourceType}/:id/{action}
 */
export const buildRoute = {
  // Document routes - Pattern: /section/documents/{documentType}/:id/{action}
  document: (documentType, documentId, action = null) => {
    const docType = typeof documentType === 'string'
      ? Object.values(DOCUMENT_TYPES).find(dt => dt.key === documentType || dt.slug === documentType)
      : documentType;

    if (!docType) throw new Error(`Unknown document type: ${documentType}`);

    return createRouteBuilder(CONTEXTS.SECTION, `documents/${docType.slug}`)(documentId, action);
  },

  // Notice routes - Pattern: /{context}/my-notices/{noticeType}/:id/{action}
  notice: (noticeType, documentId, action = null, context = null) => {
    const actualContext = context || getCurrentContext();
    const noticeTypeObj = typeof noticeType === 'string'
      ? Object.values(NOTICE_TYPES).find(nt => nt.key === noticeType || nt.slug === noticeType)
      : noticeType;

    if (!noticeTypeObj) throw new Error(`Unknown notice type: ${noticeType}`);

    return createRouteBuilder(actualContext, `my-notices/${noticeTypeObj.slug}`)(documentId, action);
  },

  // Question notice routes - Pattern: /{context}/my-question-notices/notice-for-question/:id/{action}
  questionNotice: (documentId, action = null, context = null) => {
    const actualContext = context || getCurrentContext();
    return createRouteBuilder(actualContext, 'my-question-notices/notice-for-question')(documentId, action);
  },

  // Question routes - Pattern: /{context}/my-question/:id/{action}
  question: (documentId, action = null, context = null) => {
    const actualContext = context || getCurrentContext();
    return createRouteBuilder(actualContext, 'my-question')(documentId, action);
  },

  // Question edit routes - Pattern: /section/question-edit-list/:id/edit
  questionEdit: (documentId) => createRouteBuilder(CONTEXTS.SECTION, 'question-edit-list')(documentId, ACTIONS.EDIT),

  // Setting routes - Pattern: /section/setting-of-unstarred-questions/:id
  settingUnstarredQuestions: (documentId) => createRouteBuilder(CONTEXTS.SECTION, 'setting-of-unstarred-questions')(documentId),

  // Balloting routes - Pattern: /section/ballot/perform/:id/{action}
  balloting: (id, action = null) => {
    return createRouteBuilder(CONTEXTS.SECTION, 'ballot/perform')(id, action);
  },
};

/**
 * Helper functions for route building
 */
export const routeHelpers = {
  // Get document type by key or slug
  getDocumentType: (identifier) => {
    return Object.values(DOCUMENT_TYPES).find(dt =>
      dt.key === identifier || dt.slug === identifier
    );
  },

  // Get notice type by key or slug
  getNoticeType: (identifier) => {
    return Object.values(NOTICE_TYPES).find(nt =>
      nt.key === identifier || nt.slug === identifier
    );
  },

  // Get formatted document type label (replaces getFormattedDocumentType)
  getFormattedDocumentType: (documentType = "") => {
    const docType = Object.values(DOCUMENT_TYPES).find(dt => dt.key === documentType);
    if (docType) {
      return docType.label;
    }

    // Fallback: convert SNAKE_CASE to Title Case
    return toTitleCase(documentType);
  },

  // Get all document types as array
  getAllDocumentTypes: () => Object.values(DOCUMENT_TYPES),

  // Get all notice types as array
  getAllNoticeTypes: () => Object.values(NOTICE_TYPES),

  // Get current context from pathname
  getCurrentContext,
};