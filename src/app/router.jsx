import { BaseLayout } from "@/layouts/base-layout";
import MinisterDesignationGroupPage from "@/pages/minister-designation-group";
import DocumentList from "@/pages/listing";
import { QuestionCreatePage } from "@/pages/listing/questions-page/question-notice";
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RouterProvider,
  Outlet,
  Navigate,
} from "react-router-dom";
import BallotList from "../pages/listing/ballot";
import { AllotmentOfDaysPage } from "@/pages/allotment-of-days/index.js";
import ScheduleOfActivityPage from "@/pages/schedule-of-activity/index.js";
import PerformBalloting from "@/pages/listing/components/perform-balloting";
import ConsentList from "@/pages/consent";
import ConsentPpoList from "@/pages/consent/parliamentary-party-office-consent";
import NoticeForQuestion from "@/pages/notice-for-question";
import { CreateShortNotice } from "@/pages/short-notice";
import { CreateDiscussion } from "@/pages/half-an-hour-discussion";
import LateAnswerBulletinPage from "@/pages/late-answer-bulletin";
import DelayedAnswerBulletinPage from "@/pages/delayed-answer-bulletin";
import OtherNotices from "@/pages/other-notices-listing";
import QuestionToPrivateMember from "@/pages/notice-for-question/question-to-private-member";
import { documentByIdLoader } from "@/utils/loaders";
import DelayStatementList from "@/pages/listing/delay-statement-list";
import SettingOfUnstarredQuestionsPage from "@/pages/setting-of-unstarred-questions";
import SettingOfUnstarredQuestionList from "@/pages/setting-of-unstarred-questions/listing-page";
import ActionToBeTakenUnstarred from "@/pages/setting-of-unstarred-questions/listing-page/action-to-be-taken";
import UnstarredAllTab from "@/pages/setting-of-unstarred-questions/listing-page/all-tab";
import StarredQuestionPage from "@/pages/listing/components/starred/index";
import AnswerStatusReport from "@/pages/answer-status-report";
import CorrectionOfAnswerPage from "@/pages/correction-of-answer";
import SectionStaffList from "@/pages/notice-for-question/section-staff-list";
import ActionToBeTaken from "@/pages/notice-for-question/section-staff-list/action-to-be-taken";
import AllTab from "@/pages/notice-for-question/section-staff-list/all-tab";
import StaffNoticeList from "@/pages/notice-for-question/staff-notice-list";
import AllNotices from "@/pages/notice-for-question/staff-notice-list/all-notices";
import ActionToBeTakenTab from "@/pages/notice-for-question/staff-notice-list/action-to-be-taken";
import QuestionEditPage from "@/pages/question-edit";
import ViewMDGDetailsPage from "@/pages/minister-designation-group/view-mdg-details";
import HomePage from "@/pages/home";
import ShortNoticeReadOnlyView from "@/pages/short-notice/read-only-view";
import HalfAnHourReadOnlyView from "@/pages/half-an-hour-discussion/read-only-view";
import MyQuestion from "@/pages/my-question";
import { QuestionDocumentView } from "@/pages/my-question/component";
import PopupButton from "@/pages/demo-popup-btn";
import QuestionToPrivateMemberNoticeReadOnlyView from "@/pages/notice-for-question/question-to-private-member/read-only-view";

// Layout wrappers for each user role
const SectionLayout = () => <Outlet />;
const MemberLayout = () => <Outlet />;
const PpoLayout = () => <Outlet />;

const AppRouter = () => {
  const router = createBrowserRouter(
    [
      {
        path: "/",
        element: <BaseLayout />,
        children: [
          {
            index: true,
            element: <HomePage />,
          },

          // Section Staff Routes
          {
            path: "section",
            element: <SectionLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/section/documents" replace />,
              },
              {
                path: "documents",
                children: [
                  {
                    index: true,
                    element: <DocumentList />,
                  },
                  {
                    path: "minister-designation-group",
                    children: [
                      {
                        path: ":documentId",
                        element: <MinisterDesignationGroupPage />,
                      },
                      {
                        path: ":documentId/view",
                        element: <ViewMDGDetailsPage />,
                      },
                    ],
                  },
                  {
                    path: "allotment-of-days/:documentId",
                    element: <AllotmentOfDaysPage />,
                  },
                  {
                    path: "schedule-of-activity/:documentId",
                    element: <ScheduleOfActivityPage />,
                  },
                  {
                    path: "delayed-answer-bulletin/:documentId",
                    element: <DelayedAnswerBulletinPage />,
                  },
                  {
                    path: "answer-status-report/:documentId",
                    element: <AnswerStatusReport />,
                  },
                  {
                    path: "delay-statement-list/:documentId",
                    element: <DelayStatementList />,
                  },
                  {
                    path: "late-answer-bulletin/:documentId",
                    element: <LateAnswerBulletinPage />,
                  },
                  {
                    path: "correction-of-answer/:documentId",
                    element: <CorrectionOfAnswerPage />,
                  },
                ],
              },
              {
                path: "question-edit-list",
                children: [
                  {
                    index: true,
                    element: <SectionStaffList />,
                  },
                  {
                    path: "all",
                    element: <AllTab />,
                  },
                  {
                    path: "action-required",
                    element: <ActionToBeTaken />,
                  },
                  {
                    path: "edit/:documentId",
                    element: <QuestionEditPage />,
                  },
                ],
              },
              {
                path: "ballot",
                children: [
                  {
                    index: true,
                    element: <Navigate to="/section/ballot/list" replace />,
                  },
                  {
                    path: "list",
                    element: <BallotList />,
                  },
                  {
                    path: "perform",
                    element: <PerformBalloting />,
                  },
                  {
                    path: "perform/:ballotingId/:questionDate/:status",
                    element: <PerformBalloting />,
                  },
                ],
              },
              {
                path: "setting-starred-questions",
                element: <StarredQuestionPage />,
              },
              {
                path: "setting-of-unstarred-questions",
                element: <SettingOfUnstarredQuestionList />,
                children: [
                  {
                    index: true,
                    element: <ActionToBeTakenUnstarred />,
                  },
                  {
                    path: "all",
                    element: <UnstarredAllTab />,
                  },
                  {
                    path: ":documentId",
                    element: <SettingOfUnstarredQuestionsPage />,
                  },
                ],
              },
              {
                path: "notices-for-question",
                element: <StaffNoticeList />,
                children: [
                  {
                    index: true,
                    element: <ActionToBeTakenTab />,
                  },
                  {
                    path: "all-notices",
                    element: <AllNotices />,
                  },
                ],
              },
            ],
          },

          // MLA (Member) Routes
          {
            path: "member",
            element: <MemberLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/member/consent" replace />,
              },
              {
                path: "consent",
                element: <ConsentList />,
              },
              {
                path: "my-notices",
                element: <OtherNotices />,
              },
              {
                path: "my-notices/notice-bank",
                element: <OtherNotices />,
              },
              {
                path: "my-notices/notice-for-short-notice/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <CreateShortNotice />,
                  },
                  {
                    path: "view",
                    element: <ShortNoticeReadOnlyView />,
                  },
                ],
              },
              {
                path: "my-notices/notice-for-half-an-hour-discussion/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <CreateDiscussion />,
                  },
                  {
                    path: "view",
                    element: <HalfAnHourReadOnlyView />,
                  },
                ],
              },
              {
                path: "my-notices/notice-for-question-to-private-member/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <QuestionToPrivateMember />,
                  },
                  {
                    path: "view",
                    element: <QuestionToPrivateMemberNoticeReadOnlyView />,
                  },
                ],
              },
              {
                path: "my-question-notices",
                element: <NoticeForQuestion />,
                children: [
                  {
                    index: true,
                     element: <NoticeForQuestion />,
                  },
                  {
                    path: "notice-bank",
                    element: <NoticeForQuestion />,
                  },
                ],
              },
              {
                path: "my-question-notices/notice-for-question/:documentId",
                element: <QuestionCreatePage />,
              },
              {
                path: "my-question",
                element: <MyQuestion />,
              },
              {
                path: "my-question/:documentId/view",
                element: <QuestionDocumentView />,
              },
            ],
          },

          // PPO Routes
          {
            path: "ppo",
            element: <PpoLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/ppo/consent" replace />,
              },
              {
                path: "consent",
                element: <ConsentPpoList />,
              },
              {
                path: "my-notices",
                element: <OtherNotices />,
              },
              {
                path: "my-notices/notice-bank",
                element: <OtherNotices />,
              },
              {
                path: "my-notices/notice-for-short-notice/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <CreateShortNotice />,
                  },
                ],
              },
              {
                path: "my-notices/notice-for-half-an-hour-discussion/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <CreateDiscussion />,
                  },
                ],
              },
              {
                path: "my-notices/notice-for-question-to-private-member/:documentId",
                children: [
                  {
                    path: "create",
                    loader: documentByIdLoader,
                    element: <QuestionToPrivateMember />,
                  },
                ],
              },
              {
                path: "my-question-notices",
                element: <NoticeForQuestion />,
              },
              {
                path: "my-question-notices/notice-bank",
                element: <NoticeForQuestion />,
              },
              {
                path: "my-question-notices/notice-for-question/:documentId",
                element: <QuestionCreatePage />,
              },
            ],
          },
          {
            path: "/demo-pop-up",
            element: <PopupButton />,
          },
        ],
      },
    ],
    { basename: import.meta.env.BASE_URL },
  );

  return (
    <RouterProvider
      router={router}
      future={{
        v7_startTransition: true,
      }}
    />
  );
};

export { AppRouter };
