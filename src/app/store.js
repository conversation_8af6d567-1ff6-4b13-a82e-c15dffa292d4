import { configureS<PERSON> } from "@reduxjs/toolkit";
import { addApiMiddleware } from "@/utils/api-utils";

// Traditional Redux slices
import breadcrumb from "@/slices/layout/breadcrumb-slice";
import userProfile from "@/slices/layout/user-profile.slice";

// Master Data Management API services
import { assemblyApi } from "@/services/master-data-management/assembly";
import { activeAssembly<PERSON>pi } from "@/services/master-data-management/active-assembly";
import { basicDetailsApi } from "@/services/master-data-management/basic-details-notice";
import { ministerApi } from "@/services/master-data-management/ministers";
import { ministerMdmListApi } from "@/services/master-data-management/minister-list";
import { calendarOfSittingsApi } from "@/services/master-data-management/calendar-of-sittings";
import { placeListData } from "@/services/master-data-management/place-list";
import { privateMemberApi } from "@/services/master-data-management/private-member";
import { userProfile<PERSON>pi } from "@/services/master-data-management/user-profile";

// Document API services
import { documentsApi } from "@/services/documents";
import { ballotsApi } from "@/services/ballots";
import { performballotsApi } from "@/services/perform-balloting";
import { ministerDesignationGroupApi } from "@/services/minister-designation-group";
import { allotmentOfDaysApi } from "@/services/allotment-of-days";
import { scheduleOfActivityApi } from "@/services/schedule-of-activity";
import { consentApi } from "@/services/consent";
import { partyApi } from "@/services/parties";
import { questionNoticeApi } from "@/services/question-notice";
import { noticeApi } from "@/services/notice";
import { questionApi } from "@/services/question";
import { noticesDiscussionAPI } from "@/services/discussion";
import { lateAnswerApi } from "@/services/late-answer-bulletin";
import { shortNoticeApi } from "@/services/short-notice";
import { delayedAnswerBulletinApi } from "@/services/delayed-answer-bulletin";
import { starredQuestionsApi } from "@/services/starred-questions";
import { unstarredQuestionsApi } from "@/services/unstarred-questions";
import { answerStatusApi } from "@/services/answer-status-report";
import { actionToBeTakenApi } from "@/services/section-staff-actions";
import { sectionStaffAllApi } from "@/services/section-staff-all";
import { explanatoryNoteApi } from "@/services/explanatory-note";
import { delayStatementListApi } from "@/services/delay-statement";
import { questionEditApi } from "@/services/notice-question-edit";
import { soaUpdateApi } from "@/services/schedule-of-activity-updates";

// Added our new API services
import { halfAnHourDiscussionApi } from "@/services/half-an-hour-discussion";
import { noticeForQuestionToPrivateMembersApi } from "@/services/notice-for-question-to-private-members";
import { noticeForShortNoticeApi } from "@/services/notice-for-short-notice";
import { otherNoticesMyNoticeApi } from "@/services/other-notices-my-notice";
import { otherNoticesNoticeBankApi } from "@/services/other-notices-notice-bank";
import { settingOfQuestionsUnstarredApi } from "@/services/setting-of-question";
import { myQuestionApi } from "@/services/my-question";
import { noticeForQuestionApi } from "@/services/notice-for-question";

// Group API services by category
const masterDataManagementApiServices = [
  assemblyApi,
  activeAssemblyApi,
  basicDetailsApi,
  ministerApi,
  ministerMdmListApi,
  calendarOfSittingsApi,
  placeListData,
  privateMemberApi,
  userProfileApi,
];

const documentApiServices = [
  documentsApi,
  ballotsApi,
  performballotsApi,
  ministerDesignationGroupApi,
  allotmentOfDaysApi,
  scheduleOfActivityApi,
  myQuestionApi,
];

const questionApiServices = [
  questionApi,
  questionNoticeApi,
  starredQuestionsApi,
  unstarredQuestionsApi,
  questionEditApi,
  settingOfQuestionsUnstarredApi,
];

const noticeApiServices = [
  noticeApi,
  noticesDiscussionAPI,
  lateAnswerApi,
  shortNoticeApi,
  delayedAnswerBulletinApi,
  explanatoryNoteApi,
  delayStatementListApi,
  halfAnHourDiscussionApi,
  noticeForQuestionToPrivateMembersApi,
  noticeForShortNoticeApi,
  otherNoticesMyNoticeApi,
  otherNoticesNoticeBankApi,
  noticeForQuestionApi,
];

const miscApiServices = [
  consentApi,
  partyApi,
  answerStatusApi,
  actionToBeTakenApi,
  sectionStaffAllApi,
  soaUpdateApi,
];

// Combine all API services
const allApiServices = [
  ...masterDataManagementApiServices,
  ...documentApiServices,
  ...questionApiServices,
  ...noticeApiServices,
  ...miscApiServices,
];

// Create reducer object dynamically
const apiReducers = allApiServices.reduce((acc, api) => {
  acc[api.reducerPath] = api.reducer;
  return acc;
}, {});

export const store = configureStore({
  reducer: {
    // Traditional Redux slices
    breadcrumb,
    userProfile,
    // API reducers
    ...apiReducers,
  },
  middleware: (getDefaultMiddleware) => {
    // Use the helper function to add all middleware
    return addApiMiddleware(getDefaultMiddleware, allApiServices);
  },
});
