import { render } from "@testing-library/react";
import { expect, test, vi } from "vitest";
import { App } from ".";

// Mock AppRouter to avoid router issues in tests
vi.mock("./router", () => ({
  AppRouter: () => <div data-testid="mocked-router">Router Content</div>,
}));

test("renders App without errors", async () => {
  const consoleErrorSpy = vi
    .spyOn(console, "error")
    .mockImplementation(() => {});

  render(<App />);

  expect(consoleErrorSpy).not.toHaveBeenCalled();
  consoleErrorSpy.mockRestore();
});
