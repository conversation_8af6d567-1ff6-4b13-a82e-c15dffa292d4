import { App } from "@/app/index.jsx";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

async function enableMocking() {
  const { worker } = await import("../mock/browser");
  return worker.start({
    serviceWorker: {
      url: new URL(
        import.meta.env.BASE_URL + "mockServiceWorker.js",
        location.origin,
      ),
    },
  });
}

enableMocking().then(() => {
  createRoot(document.getElementById("root")).render(
    <StrictMode>
      <App />
    </StrictMode>,
  );
});
