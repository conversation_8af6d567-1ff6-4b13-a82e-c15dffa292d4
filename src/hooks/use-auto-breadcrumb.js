import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { setBreadcrumb } from "@/slices/layout/breadcrumb-slice";
import { generateSmartBreadcrumbs } from "@/utils/router-breadcrumbs";

export const useAutoBreadcrumb = () => {
  const dispatch = useDispatch();
  const location = useLocation();

  useEffect(() => {
    const breadcrumbItems = generateSmartBreadcrumbs(location.pathname);
    dispatch(setBreadcrumb(breadcrumbItems));
  }, [location.pathname, dispatch]);
};
