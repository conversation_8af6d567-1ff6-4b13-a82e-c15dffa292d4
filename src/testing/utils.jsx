import { render } from "@testing-library/react";
import { RouterProvider, createMemoryRouter } from "react-router-dom";
import { AppProvider } from "@/app/provider";

export const renderApp = async (
  uiElement,
  { url = "/", path = "/", routerOptions = {}, ...renderOptions } = {},
) => {
  const router = createMemoryRouter(
    [
      {
        path: path,
        element: uiElement,
        ...routerOptions,
      },
    ],
    {
      initialEntries: url ? ["/", url] : ["/"],
      initialIndex: url ? 1 : 0,
    },
  );

  const returnValue = {
    ...render(uiElement, {
      wrapper: () => {
        return (
          <AppProvider>
            <RouterProvider router={router} />
          </AppProvider>
        );
      },
      ...renderOptions,
    }),
  };

  return returnValue;
};
