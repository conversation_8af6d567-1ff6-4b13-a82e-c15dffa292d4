/**
 * Returns Tailwind classes for priority badges
 * @param {'P1'|'P2'|'P3'|'NIL'} priority - Priority level
 * @param {string} [current] - Optional current priority for comparison
 * @returns {string} Tailwind classes
 */
export const getPriorityClasses = (priority, current) => {
  const base = "border rounded-full px-3 py-1 text-sm";

  if (current && current !== priority)
    return `${base} border-gray-300 text-gray-700`;

  const styles = {
    P1: "border-blue-200 bg-blue-100 text-blue-500",
    P2: "border-green-200 bg-green-100 text-green-500",
    P3: "border-red-200 bg-red-100 text-red-500",
    NIL: "border-gray-300 bg-gray-100 text-gray-700",
  };

  return `${base} ${styles[priority] || styles.NIL}`;
};
