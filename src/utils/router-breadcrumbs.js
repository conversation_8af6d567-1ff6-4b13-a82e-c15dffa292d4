/**
 * Router-based breadcrumb utilities
 * Extracts breadcrumb information directly from router configuration
 */

/**
 * Convert kebab-case to Title Case for breadcrumbs
 * Handles common patterns intelligently
 */
const kebabToTitle = (str) => {
  return str
    .split("-")
    .map((word) => {
      // Handle common abbreviations that should be uppercase
      if (word.toLowerCase() === "ppo") return "PPO";
      if (word.toLowerCase() === "api") return "API";
      if (word.toLowerCase() === "id") return "ID";

      // Standard title case
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

/**
 * Generate human-readable breadcrumb label from path segment
 * Uses pure string manipulation - no special cases needed
 */
const generateBreadcrumbLabel = (segment) => {
  return kebabToTitle(segment);
};

/**
 * Build breadcrumb items from URL pathname
 * Handles dynamic parameters properly by excluding them from navigation
 */
export const buildBreadcrumbsFromPath = (pathname) => {
  if (pathname === "/") {
    return [
      {
        label: "Home",
        route: "/",
      },
    ];
  }

  // Remove leading/trailing slashes and split into segments
  const segments = pathname.replace(/^\/+|\/+$/g, "").split("/");

  // Build breadcrumbs by processing segments sequentially
  const breadcrumbs = [];

  // Always start with Home
  breadcrumbs.push({
    label: "Home",
    route: "/",
    action: "navigate",
  });

  // Process each segment and build progressive paths (excluding ID and action segments)
  let currentPath = "";
  let lastNavigablePath = "";
  
  segments.forEach((segment, index) => {
    const isDynamic =
      /^\d+$/.test(segment) ||
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        segment,
      ) ||
      /^[a-f0-9]{24}$/i.test(segment);

    const isActionWord = /^(add|edit|view|create|update|delete)$/i.test(segment);

    currentPath += "/" + segment;

    if (!isDynamic && !isActionWord) {
      // For normal segments, create breadcrumb and track navigable path
      lastNavigablePath = currentPath;
      
      breadcrumbs.push({
        label: generateBreadcrumbLabel(segment),
        route: lastNavigablePath,
        action: index < segments.length - 1 ? "navigate" : undefined,
      });
    } else if (isDynamic) {
      // For ID segments, update the last breadcrumb to be non-navigable (current page)
      if (breadcrumbs.length > 0) {
        breadcrumbs[breadcrumbs.length - 1].action = undefined;
        breadcrumbs[breadcrumbs.length - 1].isActive = true;
      }
    }
    // Action words (add, edit, view) are completely skipped from breadcrumbs
  });

  return breadcrumbs;
};

/**
 * Smart breadcrumb generation that handles special cases
 * Action words (add, edit, view) are filtered out during path processing
 */
export const generateSmartBreadcrumbs = (pathname) => {
  return buildBreadcrumbsFromPath(pathname);
};
