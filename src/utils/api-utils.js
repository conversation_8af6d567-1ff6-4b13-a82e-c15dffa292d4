import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

/**
 * Creates a standardized API service with consistent configuration
 *
 * @param {Object} options - Configuration options for the API service
 * @param {string} options.reducerPath - The reducer path for the service
 * @param {string} options.baseUrl - The base URL for the service
 * @param {string[]} options.tagTypes - Array of tag types for caching
 * @param {Object} options.endpoints - The endpoints configuration function
 * @param {Object} options.prepareHeaders - Optional function to prepare headers
 * @returns {Object} The configured API service
 */
export const createApiService = ({
  reducerPath,
  baseUrl,
  tagTypes = [],
  endpoints,
  prepareHeaders,
}) => {
  // Standardize baseUrl format
  let standardizedBaseUrl = baseUrl;

  // Handle different environment variable formats
  if (
    typeof baseUrl === "string" &&
    (baseUrl.startsWith("VITE_") || baseUrl.startsWith("BASE_URL"))
  ) {
    standardizedBaseUrl = import.meta.env[baseUrl] || "";
  }

  // Ensure baseUrl ends with '/' if it's not empty
  if (standardizedBaseUrl && !standardizedBaseUrl.endsWith("/")) {
    standardizedBaseUrl += "/";
  }

  // Create standardized fetchBaseQuery
  const baseQuery = fetchBaseQuery({
    baseUrl: standardizedBaseUrl,
    prepareHeaders: prepareHeaders || ((headers) => headers),
  });

  // Create and return the API service
  return createApi({
    reducerPath,
    baseQuery,
    tagTypes,
    endpoints,
  });
};

/**
 * Helper function to add all API middleware to the store
 *
 * @param {Function} getDefaultMiddleware - The getDefaultMiddleware function from configureStore
 * @param {Array} apis - Array of API services
 * @returns {Array} Middleware array with all API middleware added
 */
export const addApiMiddleware = (getDefaultMiddleware, apis) => {
  return getDefaultMiddleware().concat(...apis.map((api) => api.middleware));
};
