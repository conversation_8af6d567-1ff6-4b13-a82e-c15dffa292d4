{"documentType": "Document Type", "allDocuments": "All Documents", "create": "Create", "assign": "Assign", "confirm": "Confirm", "cancel": "Cancel", "return": "Return", "proceed": "Proceed", "cancelBalloting": "Cancel Balloting", "showing": "Showing", "documentName": "Document Name", "document": "Document", "selectBelowOptions": "Select below option(s) to create Document", "allotmentOfDays": "Allotment of Days", "scheduleOfActivity": "Schedule of Activity", "assembly": "Assembly", "session": "Session", "currentNo": "Current No", "createdOn": "Created on", "createdBy": "Created by", "approvedCOS": "Approved COS", "approvedMinisterGroup": "Approved Minister Group", "calendarOfSittings": "Calendar of Sittings", "approvedSittingDatesDescription": "Approved sitting dates for the assembly session", "period": "Period", "sittingDates": "Sitting Dates", "noCalendarAvailable": "No calendar of sittings available", "close": "Close", "ministerGroups": "Minister Groups", "approvedMinisterGroupsDescription": "Approved minister groups for the assembly", "noMinisterGroupsAvailable": "No minister groups available", "back": "Back", "save": "Save", "submit": "Submit", "saving": "Saving...", "submitting": "Submitting...", "approved": "Approved AOD", "slNo": "Sl No", "applyUpdates": "Apply Updates", "questionDay": "Question Day", "ballotingDay": "Balloting Day (Time 12:15 pm)", "dateForSendingQuestionsToPress": "Date for Sending Questions to Press and the Publishing Date", "printedQuestionBooklet": "Printed Question Booklet", "receivedDate": "Received Date", "distributionOfQuestion": "Distribution of Question Booklet to Members", "answerDate": "Answer Date As per Rule 47(1)", "withoutDelayStatement": "Without Delay Statement As per Rule 47(2)", "kla": "KLA", "action": "Action", "ballotheading": "Ballot Heading", "group": "Group", "questionDate": "Question Date", "ballotDate": "Ballot Date", "status": "Status", "ballotList": "Ballot List", "perform": "Perform", "requestReceived": "Request Received", "requestAccepted": "Request Accepted", "requestSent": "Request Sent", "consentList": "Consent List", "requestConsent": "Request Consent", "accept": "Accept", "decline": "Decline", "revoke": "Revoke", "sendRequestConsent": "Select below option(s) to Send Request Consent", "send": "Send", "confirmAction": "Confirm Action", "revoked": "Revoked", "pending": "Pending", "declined": "Declined", "accepted": "Accepted", "withdraw": "Withdraw", "acceptedRequest": "Accepted Request", "sentRequest": "Sent Request", "chooseWorkFlowActions": "Choose Workflow actions", "canleballoting": "Cancel Balloting", "previewAndAttachToFile": "Preview & Attach to File", "attachToFile": "Attach to File", "details": "Details", "preview": "Preview", "requiredTwoDays": "Two clear days is required for submitting this notice.", "toast": {"documentSave": "Document Saved Successfully", "documentSudbmit": "Document submitted Successfully", "basicDetailsSave": "Basic Details Saved Successfully", "noticeDetailsSave": "Notice Details Saved Successfully"}, "noticeHeading": "Notice Heading", "noticeNumber": "Notice Number", "generateBooklet": "Generate Booklet", "basicDetails": "Basic Details", "explanatoryNote": "Explanatory Note", "noticeDetails": "Notice Details", "aodUpdates": "Updates from Allotment of Days", "rule": "Rule", "delayStatementLaying": "Delay Statement Laying", "saving...": "Saving...", "list": "List", "manual": "Manual", "addItemstoDelayStatementList": "Add Items to Delay Statement List", "selectbelowitemstoaddinDelayStatementlist": "Select below items to add in Delay Statement list.", "questionNumber": "Question Number", "questionHeading": "Question Heading", "uploadAnswers&DelayStatement": "Upload Answers & Delay Statement", "addToList": "Add To List", "addSelected": "Add Selected", "questionNo": "Question No:", "dateofRegistration": "Date of Registration", "designation": "Designation", "ministerSubject/Portfolio": "Minister Subject / Portfolio", "ministerSubSubject": "Minister Sub Subject", "nameOfMembers": "Name Of Members", "questionDetails": "Question Details", "removefromList": "Remove from List", "reason": "Reason", "removing": "Removing...", "layingListDeleteConfirmationMessage": "Are you sure you want to delete this item from the laying list?", "applyUpdate": "Apply Update", "notAssigned": "Not Assigned", "nameofMember": "Name of Member", "updatesAvailableAlertText": "Updates Available", "dataLoadError": "Error loading data. Please try again later.", "ministerProfileUpdateAlert": "Ministers in this document have been changed or their profiles have been updated. Please update the document to reflect the latest changes.", "ministerProfileUpdatesSectionTitle": "Updates from Minister Profile", "autoUpdateOn": "Auto Update On", "checkDuplicate": "Check Duplicate", "headingMatches": "Heading Matches", "clauseMatches": "Clause Matches", "keyWordToSearch": "Keywords to Search :", "noKeyWords": "No keywords", "clearAll": "Clear All", "matches": "Matches :", "noMatchesFound": "No matches found", "search": "Search", "download": "Download", "description": "Description", "postpone": "Postpone", "clause": "<PERSON>e", "doYouWantToPostponeQuestion": "Do you want to Postpone question?", "postponeDate": "Postpone Date", "selectDate": "Select Date", "postponeWarningMessage": "These changes will impact the notice and can't be undone. Please review all updates carefully before confirming.", "forward": "Forward", "forwardDocument": "Forward Document", "forwardDocumentConfirmation": "Are you sure you want to return the Document?", "noticeforQuestion": "Notice For Question", "noticeForQuestionList": "Notice For Question List", "myNotices": "My Notices", "noticeBank": "Notice Bank", "priority": "Priority"}