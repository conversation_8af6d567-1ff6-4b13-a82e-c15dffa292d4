{"correspondingOrdinanceYear": "അനുബന്ധ ഓർഡിനൻസ് വർഷം", "enterCorrespondingOrdinanceNumber": "അനുബന്ധ ഓർഡിനൻസ് നമ്പർ നൽകുക", "theCorrespondingOrdinanceIsRegisteredExternally": "അനുബന്ധ ഓർഡിനൻസ് ബാഹ്യമായി രജിസ്റ്റർ ചെയ്തിട്ടുണ്ട്", "isExternallyRegistered": "റഫറന്‍സ് എസ്.ആര്‍.ഒ. എക്സ്റ്റേണലായി രജിസ്റ്റേര്‍  ചെയ്തിട്ടുണ്ട്", "referenceSroYear": "റഫറന്‍സ് എസ്.ആര്‍.ഒ. വര്‍ഷം", "referenceSroNumber": "റഫറന്‍സ് എസ്.ആര്‍.ഒ. നമ്പര്‍", "laidDate": "മേശപ്പുറത്ത് വച്ച തീയതി", "laidDateOptional": "മേശപ്പുറത്ത് വച്ച തീയതി (ഓപ്ഷണൽ)", "referenceSroFiles": "റഫറൻസ് എസ്.ആര്‍.ഒ. അപ്‌ലോഡ് ചെയ്യുക", "sroType": "എസ്.ആര്‍.ഒ. ഇനം", "isReferenceLaid": "റഫറന്‍സ് എസ്.ആര്‍.ഒ. ലെയ്ഡ്/രജിസ്റ്റേര്‍ഡ്?", "gazetteDate": "ഗസറ്റ് തീയതി", "gazetteNumber": "ഗസറ്റ് നം.", "department": " വകുപ്പ്", "correspondingActYear": "ആക്റ്റിന്റെ വര്‍ഷം", "correspondingActNumber": "ആക്റ്റിന്റെ നം.", "letterNumber": "നമ്പർ", "letterDate": "തീയതി", "gazetteNo": "ഗസറ്റ് നം.", "portfolio": " പോര്‍ട്ട്ഫോളിയോ", "designation": "സ്ഥാനപ്പേര്", "name": " പേര്", "number": "നമ്പര്‍", "date": "തീയതി", "signedBy": "ഒപ്പുവെച്ചത്", "ministerName": " പേര്", "departmentSectionName": "വിഭാഗം", "ministerDepartment": " വകുപ്പ്", "ministerDesignation": "സ്ഥാനപ്പേര്", "ministerPortfolio": " പോര്‍ട്ട്ഫോളിയോ", "sro": {"classification": "എസ്.ആര്‍.ഒ. ക്ലാസിഫിക്കേഷന്‍", "subjectInLocal": "എസ്.ആര്‍.ഒ. യുടെ വിഷയം(മലയാളം)", "subject": "എസ്.ആര്‍.ഒ. യുടെ വിഷയം(ഇംഗ്ലീഷ്)"}, "documentType": "ഡോക്യുമെന്റ് ഇനം", "documentName": "ഡോക്യുമെന്റിന്റെ പേര്", "submittedBy": "സമർപ്പിച്ചത്", "createdBy": "ക്രിയേറ്റ് ചെയ്ത തീയതി", "sroNumber": "എസ്ആർഒ നമ്പർ", "goNumber": "ജി.ഓ നമ്പർ", "goDate": "തീയതി", "correspondingActName": "ആക്റ്റിന്റെ പേര് ", "selectDate": "തീയതി", "numberOfDaysToBeLaidInAssembly": "മേശപ്പുറത്ത് വയ്ക്കേണ്ട ദിവസങ്ങളുടെ എണ്ണം", "typeOfAct": "ആക്റ്റിന്റെ തരം", "actYear": "ആക്റ്റിന്റെ വര്‍ഷം", "actNumber": "ആക്റ്റിന്റെ നം.", "section": "വിഭാഗം", "letterSignedBy": "ഒപ്പുവെച്ചത്", "actName": "ആക്റ്റിന്റെ പേര്", "layingProvisionInAct": "ആക്റ്റ് വിശദാംശങ്ങൾ", "signedByMinister": "ഒപ്പുവെച്ചത്", "delayStatement": {"signedByMinister": "മന്ത്രി ഒപ്പു വച്ചിട്ടുണ്ടോ"}, "place": "സ്ഥലം", "toSecretary": "നിയമസഭാ സെക്രട്ടറിക്ക്", "constituency": "നിയോജകമണ്ഡലം", "noticeDate": "നോട്ടീസ് തീയതി", "subject": "വിഷയം"}