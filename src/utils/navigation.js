/**
 * Navigation utilities for consistent routing across the application
 * Uses centralized route configuration with factory patterns
 */

import { useNavigate, useLocation } from "react-router-dom";
import { useCallback } from "react";
import { CONTEXTS, ROUTE_CONFIG } from "@/config/routes";

// Re-export centralized utilities
export { buildRoute } from "@/config/routes";

/**
 * Navigate with automatic context detection
 */
export const useContextualNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const getCurrentContext = useCallback(() => {
    const pathname = location.pathname;
    if (pathname.includes('/ppo/')) return CONTEXTS.PPO;
    if (pathname.includes('/member/')) return CONTEXTS.MEMBER;
    if (pathname.includes('/section/')) return CONTEXTS.SECTION;
    return CONTEXTS.MEMBER; // default
  }, [location.pathname]);

  const isPPOContext = location.pathname.includes('/ppo/');
  const isMemberContext = location.pathname.includes('/member/');
  const isSectionContext = location.pathname.includes('/section/');

  const navigateWithContext = useCallback((path) => {
    // If path is already contextual, use as-is
    if (path.startsWith('/ppo/') || path.startsWith('/member/') || path.startsWith('/section/')) {
      navigate(path);
      return;
    }

    // For base paths, convert to contextual routes
    const context = getCurrentContext();
    let contextualPath = path;

    if (path === '/my-notices') {
      contextualPath = context === CONTEXTS.PPO ? ROUTE_CONFIG.PPO.MY_NOTICES : ROUTE_CONFIG.MEMBER.MY_NOTICES;
    } else if (path === '/my-question-notices') {
      contextualPath = context === CONTEXTS.PPO ? ROUTE_CONFIG.PPO.MY_QUESTION_NOTICES : ROUTE_CONFIG.MEMBER.MY_QUESTION_NOTICES;
    } else if (path === '/consent') {
      contextualPath = context === CONTEXTS.PPO ? ROUTE_CONFIG.PPO.CONSENT : ROUTE_CONFIG.MEMBER.CONSENT;
    }

    navigate(contextualPath);
  }, [navigate, getCurrentContext]);

  return {
    navigate: navigateWithContext,
    isPPOContext,
    isMemberContext,
    isSectionContext,
    getCurrentContext,
  };
};