/**
 * Navigation utilities for consistent routing across the application
 * Uses centralized route configuration with factory patterns
 */

import { useNavigate } from "react-router-dom";
import { useCallback } from "react";
import { CONTEXTS, ROUTE_CONFIG, routeHelpers } from "@/config/routes";

// Re-export centralized utilities
export { buildRoute } from "@/config/routes";

/**
 * Navigate with automatic context detection
 */
export const useContextualNavigation = () => {
  const navigate = useNavigate();

  const getCurrentContext = useCallback(() => {
    return routeHelpers.getCurrentContext();
  }, []);

  const navigateWithContext = useCallback((path) => {
    // If path is already contextual, use as-is
    if (path.startsWith('/ppo/') || path.startsWith('/member/') || path.startsWith('/section/')) {
      navigate(path);
      return;
    }

    // For base paths, convert to contextual routes using ROUTE_CONFIG
    const context = getCurrentContext();
    const contextConfig = ROUTE_CONFIG[context.toUpperCase()];

    // Map common base paths to their contextual equivalents
    const pathMappings = {
      '/my-notices': contextConfig?.MY_NOTICES,
      '/my-question-notices': contextConfig?.MY_QUESTION_NOTICES,
      '/consent': contextConfig?.CONSENT,
      '/documents': contextConfig?.DOCUMENTS,
    };

    const contextualPath = pathMappings[path] || path;
    navigate(contextualPath);
  }, [navigate, getCurrentContext]);

  return {
    navigate: navigateWithContext,
    getCurrentContext,
  };
};