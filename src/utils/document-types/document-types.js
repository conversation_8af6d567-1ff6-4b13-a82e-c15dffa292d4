import { DOCUMENT_TYPES, NOTICE_TYPES, ACTIONS, CONTEXTS, buildRoute } from "@/config/routes";

// Clean document types array from centralized config
const documentTypes = Object.values(DOCUMENT_TYPES).map(docType => ({
  label: docType.label,
  labelInLocal: docType.label,
  value: docType.key,
  route: `/section/documents/${docType.slug}/`,
}));

// Clean notice types array from centralized config
export const noticeTypes = Object.values(NOTICE_TYPES)
  .filter(noticeType => noticeType.key !== 'NOTICE_FOR_QUESTION') // Exclude question notices
  .map(noticeType => ({
    label: noticeType.label,
    value: noticeType.key,
    slug: noticeType.slug,
  }));

// Helper function to get notice routes using new system
export const getNoticeRoute = (noticeTypeKey, action = ACTIONS.CREATE, context = CONTEXTS.MEMBER) => {
  // Handle both new noticeTypeKey format and legacy routeType format
  let noticeType;

  if (typeof noticeTypeKey === 'string') {
    // First try to find by key
    noticeType = Object.values(NOTICE_TYPES).find(nt => nt.key === noticeTypeKey);

    // If not found, try legacy routeType mapping
    if (!noticeType) {
      switch (noticeTypeKey) {
        case "halfHour":
          noticeType = NOTICE_TYPES.NOTICE_FOR_HALF_AN_HOUR_DISCUSSION;
          break;
        case "privateMember":
          noticeType = NOTICE_TYPES.NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS;
          break;
        case "shortNotice":
          noticeType = NOTICE_TYPES.NOTICE_FOR_SHORT_NOTICE;
          break;
      }
    }
  }

  if (!noticeType) {
    return `/${context}/my-notices/`;
  }

  return buildRoute.notice(noticeType, '', action, context);
};

export const ballotStatus = {
  CANCELLED: "CANCELLED",
  SUBMITTED: "SUBMITTED",
};

export const ballotTypes = [
  {
    label: "Notice For Short Notice",
    value: ballotStatus.CANCELLED,
  },
  {
    label: "Notice For Short Notice",
    value: ballotStatus.SUBMITTED,
  },
];



export default documentTypes;
