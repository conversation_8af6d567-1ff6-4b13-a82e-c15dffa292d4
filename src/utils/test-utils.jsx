import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { referenceSroApi } from "@/services/reference-sro-list";
import PropTypes from "prop-types";

export function renderWithProviders(
  ui,
  {
    preloadedState = {},
    store = configureStore({
      reducer: {
        [referenceSroApi.reducerPath]: referenceSroApi.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(referenceSroApi.middleware),
      preloadedState,
    }),
    ...renderOptions
  } = {},
) {
  function Wrapper({ children }) {
    return <Provider store={store}>{children}</Provider>;
  }

  Wrapper.propTypes = {
    children: PropTypes.node,
  };

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}
