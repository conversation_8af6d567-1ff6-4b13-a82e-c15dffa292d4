import PropTypes from "prop-types";
import DocumentMetadataItem from "./document-metadata-item";
import { User } from "lucide-react";
import { cn } from "@/utils";
import { getLabels } from "@/utils/document-metadata";
import { useLanguage } from "@/hooks";
import { routeHelpers } from "@/config/routes";

const DocumentMetadata = ({ documentMetadata }) => {
  const { t } = useLanguage();
  const metaLabels = getLabels(t);
  const {
    documentType,
    documentSubType,
    reportType,
    createdOn,
    createdBy,
    currentNo,
    isTitle,
    title,
    assembly,
    session,
  } = documentMetadata;

  return (
    <div className="p-2 mb-2 rounded-t-xl bg-primary-tint-90">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className={cn("flex gap-4")}>
          {isTitle ? (
            <span className={cn("typography-body-text-m-16")}>
              {title} {documentType}
            </span>
          ) : (
            <>
              {assembly && (
                <DocumentMetadataItem
                  label={metaLabels.assembly}
                  value={assembly}
                  section="left"
                  variant="small"
                />
              )}
              {session && (
                <DocumentMetadataItem
                  label={metaLabels.SESSION}
                  value={session}
                  section="left"
                  variant="small"
                />
              )}
              {documentType && (
                <DocumentMetadataItem
                  label={metaLabels.DOCUMENT_TYPE}
                  value={routeHelpers.getFormattedDocumentType(documentType)}
                  variant="small"
                />
              )}
              {documentSubType && (
                <DocumentMetadataItem
                  label={metaLabels.SUB_TYPE}
                  value={documentSubType}
                  section="left"
                  variant="small"
                />
              )}
              {reportType && (
                <DocumentMetadataItem
                  label={metaLabels.REPORT_TYPE}
                  value={reportType}
                  section="left"
                  variant="small"
                />
              )}
            </>
          )}
        </div>

        {/* Right Section */}
        <div className="flex gap-2 text-right">
          {!isTitle && (
            <>
              {currentNo && (
                <DocumentMetadataItem
                  label={metaLabels.CURRENT_NO}
                  value={currentNo}
                  variant="small"
                />
              )}
              {createdOn && (
                <DocumentMetadataItem
                  label={metaLabels.CREATED_ON}
                  value={createdOn}
                  variant="small"
                />
              )}

              {createdBy && (
                <>
                  <DocumentMetadataItem
                    label={metaLabels.CREATED_BY}
                    value={createdBy}
                    variant="small"
                  />
                  <User className="p-1 text-gray-600 bg-white rounded-full w-7 h-7" />
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

DocumentMetadata.propTypes = {
  documentMetadata: PropTypes.shape({
    documentType: PropTypes.string,
    documentSubType: PropTypes.string,
    reportType: PropTypes.string,
    createdOn: PropTypes.string,
    currentNo: PropTypes.string,
    createdBy: PropTypes.string,
    type: PropTypes.string,
    isTitle: PropTypes.bool,
    title: PropTypes.string,
    assembly: PropTypes.number,
    session: PropTypes.number,
  }),
};

export { DocumentMetadata };
