import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { DocumentMetadata } from "./index";

describe("DocumentMetadata Component", () => {
  const mockMetadata = {
    documentType: "SRO",
    documentSubType: "Parent Rule/Order",
    createdOn: "16-08-2024",
    createdBy: "Health and Family Welfare",
    isTitle: false,
    title: "Registering",
  };

  it("renders title and type when isTitle is true", () => {
    const metadata = { ...mockMetadata, isTitle: true };
    render(<DocumentMetadata documentMetadata={metadata} />);

    expect(
      screen.getByText(`${metadata.title} ${metadata.documentType}`),
    ).toBeInTheDocument();

    // Ensure other elements are not rendered
    expect(screen.queryByText("Document Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Sub Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Created On")).not.toBeInTheDocument();
    expect(screen.queryByText("Created By")).not.toBeInTheDocument();
  });

  it("does not render 'Document Type' or 'Sub Type' if values are missing", () => {
    const metadata = { ...mockMetadata, documentSubType: null };
    render(<DocumentMetadata documentMetadata={metadata} />);

    expect(screen.queryByText("Document Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Sub Type")).not.toBeInTheDocument();
  });

  it("does not render 'Created On' or 'Created By' if values are missing", () => {
    const metadata = { ...mockMetadata, createdOn: null, createdBy: null };
    render(<DocumentMetadata documentMetadata={metadata} />);

    expect(screen.queryByText("Created On")).not.toBeInTheDocument();
    expect(screen.queryByText("Created By")).not.toBeInTheDocument();
  });

  it("renders without crashing when metadata is empty", () => {
    const metadata = {};
    render(<DocumentMetadata documentMetadata={metadata} />);

    // Ensure that key elements or labels are not rendered
    expect(screen.queryByText(/Registering SRO/)).not.toBeInTheDocument();
    expect(screen.queryByText("Document Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Sub Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Created On")).not.toBeInTheDocument();
    expect(screen.queryByText("Created By")).not.toBeInTheDocument();
  });
});
