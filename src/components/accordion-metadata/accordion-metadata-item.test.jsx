import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import DocumentMetadataItem from "./accordion-metadata-item";

describe("DocumentMetadataItem Component", () => {
  it("renders the label and value correctly for the left section", () => {
    render(
      <DocumentMetadataItem label="Document Type" value="SRO" section="left" />,
    );

    // Assert label is rendered with the correct style
    const label = screen.getByText("Document Type:");
    expect(label).toBeInTheDocument();
    expect(label).toHaveClass("text-gray-600 typography-body-text-r-12");

    // Assert value is rendered with the correct style
    const value = screen.getByText("SRO");
    expect(value).toBeInTheDocument();
    expect(value).toHaveClass("typography-body-text-m-14");
  });

  it("renders the label and value correctly for the right section", () => {
    render(
      <DocumentMetadataItem
        label="Created By"
        value="Health and Family Welfare"
        section="right"
        variant="small"
      />,
    );

    // Assert label is rendered with the correct style
    const label = screen.getByText("Created By:");
    expect(label).toBeInTheDocument();
    expect(label).toHaveClass("text-gray-600 typography-body-text-r-12");

    // Assert value is rendered with the correct style
    const value = screen.getByText("Health and Family Welfare");
    expect(value).toBeInTheDocument();
    expect(value).toHaveClass("typography-body-text-m-12");
  });

  it("applies default medium variant when no variant is specified", () => {
    render(<DocumentMetadataItem label="Created On" value="16-08-2024" />);

    // Check if the medium variant is applied by default
    const value = screen.getByText("16-08-2024");
    expect(value).toHaveClass("typography-body-text-m-14"); // Correct default class
  });

  it("renders without crashing when props are missing", () => {
    render(<DocumentMetadataItem label={null} value={null} />);
    expect(screen.queryByText(/:/)).not.toBeInTheDocument();
  });
});
