import { useLanguage } from "@/hooks";
import { useGetExplanatoryNoteQuery } from "@/services/explanatory-note";
import { renderProseMirrorJSON } from "@/utils/richTestTopdf";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  ScrollArea,
} from "@kla-v2/ui-components";
import html2pdf from "html2pdf.js";
import { Download, Expand, Minus, Plus } from "lucide-react";
import PropTypes from "prop-types";
import { useRef, useState } from "react";

const RichTextToPDF = ({ jsonString }) => {
  const contentRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const htmlContent = renderProseMirrorJSON(JSON.parse(jsonString || "{}"));
  const { data: explanatory } = useGetExplanatoryNoteQuery();
  const handleZoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const handleZoomOut = () => setScale((prev) => Math.max(prev - 0.1, 0.5));
  const toggleFullScreen = () => setIsFullScreen(!isFullScreen);
  const { t } = useLanguage();

  const downloadPDF = () => {
    const element = contentRef.current;
    if (!element) return;

    const options = {
      margin: 0.5,
      filename: "Document.pdf",
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: "in", format: "a4", orientation: "portrait" },
    };

    html2pdf().set(options).from(element).save();
  };

  const renderContent = () => (
    <div
      ref={contentRef}
      style={{
        transform: `scale(${scale})`,
        transformOrigin: "top left",
        transition: "transform 0.2s ease-in-out",
        display: "inline-block",
      }}
      className="gap-4"
    >
      <div className="flex flex-col gap-y-2">
        <h2 className="text-center typography-body-text-s-16 text-foreground leading-snug">
          {explanatory?.data?.title} <br /> {explanatory?.data?.subTitle}
        </h2>
      </div>
      <div className="flex flex-col gap-y-2 items-end mt-8">
        <div className="flex gap-2">
          <span className="typography-body-text-r-14">{t("form:place")}:</span>
          <span className="typography-body-text-s-14 text-foreground">
            {explanatory?.data?.place}
          </span>
        </div>
        <div className="flex gap-2">
          <span className="typography-body-text-r-14">{t("form:date")}:</span>
          <span className="typography-body-text-s-14 text-foreground">
            {explanatory?.data?.date}
          </span>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-y-4">
          <div className="flex gap-2">
            <span className="typography-body-text-s-14">
              {explanatory?.data?.mla}
            </span>
            <span className="typography-body-text-r-14 text-foreground">
              {t("mla")}
            </span>
          </div>
        </div>
        <div className="flex flex-col">
          <span className="typography-body-text-r-14">
            {t("form:toSecretary")},
          </span>
          <span className="typography-body-text-r-14">
            {t("thiruvanathapuram")}.
          </span>
        </div>
      </div>

      <div className="mt-2">
        <div
          id="pdf-content"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>

      <div className="flex flex-col gap-y-2 items-end">
        <div className="flex">
          <span className="typography-body-text-r-14">{t("faithFully")}</span>
        </div>
        <div className="flex gap-2">
          <span className="typography-body-text-r-14">{t("member")}:</span>
          <span className="typography-body-text-s-14 text-foreground">
            {explanatory?.data?.memberDisplayNameInLocal}
          </span>
        </div>
        <div className="flex gap-2">
          <span className="typography-body-text-r-14">
            {t("form:constituency")}:
          </span>
          <span className="typography-body-text-s-14 text-foreground">
            {explanatory?.data?.constituencyNameInLocal}
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-auto h-full pt-[36px] pr-[48px] pb-[16px] pl-[48px] rounded-[11px] border border-gray-300 bg-white shadow-sm text-sm text-gray-800 relative overflow-auto">
      <div className="absolute right-4 top-2 z-10">
        <div className="flex flex-col items-center gap-4">
          <button
            className="text-gray-600 hover:text-gray-900 bg-white p-2 rounded-md border border-gray-300"
            type="button"
            onClick={toggleFullScreen}
          >
            <Expand size={16} />
          </button>
          <div className="flex flex-col border border-gray-300 rounded-md">
            <button
              className="p-2"
              type="button"
              disabled={scale > 1.5}
              onClick={handleZoomIn}
            >
              <Plus size={16} />
            </button>
            <hr />
            <button
              className="p-2"
              type="button"
              disabled={scale === 1}
              onClick={handleZoomOut}
            >
              <Minus size={16} />
            </button>
          </div>
        </div>
      </div>

      {renderContent()}

      <Dialog open={isFullScreen} onOpenChange={toggleFullScreen}>
        <DialogPortal>
          <DialogContent className="max-w-[593px] max-h-[90vh] h-full p-8 rounded-[8px] border border-gray-300 bg-white shadow-md  ">
            <DialogHeader>
              <DialogTitle>
                <div className="typography-page-heading">Document.pdf</div>
              </DialogTitle>
            </DialogHeader>
            <ScrollArea className="max-h-full">
              <div className="p-1">
                <div className="border border-gray-300 rounded-lg">
                  <div
                    className="w-full leading-8 px-8 py-12 "
                    ref={contentRef}
                  >
                    {renderContent()}
                  </div>
                </div>
              </div>
            </ScrollArea>
            <DialogFooter>
              <Button
                variant="neutral"
                className="flex items-center"
                onClick={downloadPDF}
              >
                {t("download")} <Download size={16} />
              </Button>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
};

RichTextToPDF.propTypes = {
  jsonString: PropTypes.string,
};

RichTextToPDF.defaultProps = {
  jsonString: "{}",
};

export default RichTextToPDF;
