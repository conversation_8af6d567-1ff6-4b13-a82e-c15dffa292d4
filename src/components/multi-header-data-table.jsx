import PropTypes from "prop-types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kla-v2/ui-components";

export function MultiHeaderDataTable({
  headers,
  columns,
  data,
  className = "border rounded-md",
  showNumberedHeader = false,
}) {
  // Function to access nested properties safely
  const getNestedValue = (obj, path) => {
    const keys = path.split(".");
    return keys.reduce(
      (acc, key) => (acc && acc[key] !== undefined ? acc[key] : "-"),
      obj,
    );
  };

  // Generate the header rows based on the provided headers
  const finalHeaders = showNumberedHeader
    ? [
        ...headers,
        {
          id: "numbered-row",
          cells: columns.map((col, index) => ({
            id: `col-number-${index + 1}`,
            title: (
              <span className="w-5 h-5 px-1.5 py-0.5 text-sm text-center rounded-full bg-tags-draft-bg text-primary-tint-10">
                {index + 1}
              </span>
            ),
            className: "border bg-white",
          })),
        },
      ]
    : headers;

  return (
    <div className={className}>
      <div className="overflow-x-auto">
        <div className="min-w-max">
          <Table className="border-collapse">
            <TableHeader>
              {/* Render header rows */}
              {finalHeaders.map((row) => (
                <TableRow key={row.id}>
                  {row.cells.map((cell) => (
                    <TableHead
                      key={cell.id}
                      className={
                        cell.className ||
                        "text-white text-center border border-white"
                      }
                      rowSpan={cell.rowSpan}
                      colSpan={cell.colSpan}
                    >
                      <div className="px-2 py-3 typography-body-text-s-16 text-center">
                        {cell.title}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>

            <TableBody>
              {data.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column) => {
                    const value = getNestedValue(row, column.accessor);
                    return (
                      <TableCell
                        key={`${rowIndex}-${column.id}`}
                        className="text-center border border-gray-200"
                      >
                        {column.cell ? column.cell(value, row) : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

MultiHeaderDataTable.propTypes = {
  headers: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      cells: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          title: PropTypes.oneOfType([PropTypes.string, PropTypes.node])
            .isRequired,
          rowSpan: PropTypes.number,
          colSpan: PropTypes.number,
          className: PropTypes.string,
        }),
      ).isRequired,
    }),
  ).isRequired,
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      accessor: PropTypes.string.isRequired,
      className: PropTypes.string,
      cell: PropTypes.func,
    }),
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  className: PropTypes.string,
  showNumberedHeader: PropTypes.bool,
};

export default MultiHeaderDataTable;
