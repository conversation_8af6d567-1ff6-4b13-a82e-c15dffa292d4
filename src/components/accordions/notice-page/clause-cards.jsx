import PropTypes from "prop-types";
import { GripVertical } from "lucide-react";
import { Textarea } from "@kla-v2/ui-components";
import CornerIcon from "@/icons/corner-icon";
import CompareIcon from "@/icons/compare-icon";
import DeleteClauseIcon from "@/icons/delete-icon";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Controller } from "react-hook-form";
import { forwardRef } from "react";
import { useState } from "react";
import CheckDuplicateModal from "@/components/pop-up/check-duplicate";

const ClauseCards = forwardRef(function ClauseCards(
  { fields, remove, move, control, isReadOnly, trigger, formFields, error },
  // eslint-disable-next-line no-unused-vars
  ref,
) {
  const sensors = useSensors(useSensor(PointerSensor));

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClauseContent, setSelectedClauseContent] = useState("");

  const handleCheckDuplicate = (content) => {
    setSelectedClauseContent(content);
    setIsModalOpen(true);
  };

  const handleDelete = (index) => {
    if (fields.length > 1) {
      remove(index);
    }
  };
  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex((item) => item.id === active.id);
      const newIndex = fields.findIndex((item) => item.id === over.id);
      move(oldIndex, newIndex);
    }
  };

  return (
    <div>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={fields.map((field) => field.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="flex flex-col gap-2 overflow-y-auto max-h-[500px] items-center">
            {fields.map((field, index) => (
              <SortableClauseCard
                key={field.id}
                id={field.id}
                index={index}
                control={control}
                isReadOnly={isReadOnly}
                onDelete={() => handleDelete(index)}
                value={field.value}
                trigger={trigger}
                formFields={formFields}
                error={error}
                onCheckDuplicate={handleCheckDuplicate}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      <CheckDuplicateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        content={selectedClauseContent}
        variant="CLAUSE"
      />
    </div>
  );
});

ClauseCards.displayName = "ClauseCards";

const SortableClauseCard = forwardRef(function SortableClauseCard(
  {
    id,
    index,
    control,
    isReadOnly,
    onDelete,
    value,
    error,
    trigger,
    formFields,
    onCheckDuplicate,
  },
  // eslint-disable-next-line no-unused-vars
  ref,
) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const letterIndex = String.fromCharCode(65 + index);
  const maxCount = 500;
  return (
    <Controller
      name={`clauses.${index}.content`}
      control={control}
      defaultValue=""
      render={({ field }) => {
        const charCount = field.value ? field.value.length : 0;
        return (
          <>
            <div
              ref={setNodeRef}
              style={style}
              className="relative border border-gray-300 rounded-lg text-gray-500 text-sm w-[99%] flex justify-center overflow-visible p-3 bg-white"
            >
              <div
                className="absolute left-[-6px] top-1/2 -translate-y-1/2 flex items-center cursor-grab"
                {...attributes}
                {...listeners}
              >
                <div className="text-gray-700 flex w-4 h-[34px] justify-center items-center border border-gray-300 bg-white rounded-lg">
                  <GripVertical size={16} className="text-border-1" />
                </div>
              </div>

              <span className="absolute top-5 left-5 text-black font-bold">
                ({letterIndex})
              </span>
              <Textarea
                value={value}
                {...field}
                disabled={isReadOnly}
                maxLength={500}
                onChange={(val) => {
                  field.onChange(val);
                  trigger?.(`${formFields.CLAUSES.name}.${index}.content`);
                }}
                className="w-full border-none focus:outline-none resize-none text-black font-bold text-sm p-2 px-10"
                rows="4"
              />
              <div className="absolute bottom-3 right-3 flex items-center gap-2">
                <CornerIcon />
                <div className=" text-sm">
                  <span className="typography-body-text-r-12 text-foreground">
                    {charCount}
                  </span>
                  <span className="typography-body-text-r-12 text-gray-500">
                    /{maxCount}
                  </span>
                </div>
              </div>
              <div className="absolute top-3 right-3 flex items-center gap-2">
                <div
                  onClick={() => onCheckDuplicate(field.value)}
                  className="cursor-pointer"
                >
                  <CompareIcon />
                </div>

                {!isReadOnly && (
                  <button onClick={onDelete} className="cursor-pointer">
                    <DeleteClauseIcon />
                  </button>
                )}
              </div>
            </div>
            {error && (
              <p className="typography-body-text-r-14 text-error self-start">
                {error ? error[0].content.message : ""}
              </p>
            )}
          </>
        );
      }}
    />
  );
});

SortableClauseCard.displayName = "SortableClauseCard";

ClauseCards.propTypes = {
  isReadOnly: PropTypes.bool,
  fields: PropTypes.array.isRequired,
  control: PropTypes.object.isRequired,
  remove: PropTypes.func.isRequired,
  move: PropTypes.func.isRequired,
  trigger: PropTypes.func,
  formFields: PropTypes.object,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  onCheckDuplicate: PropTypes.func,
};

SortableClauseCard.propTypes = {
  id: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  isReadOnly: PropTypes.bool,
  onDelete: PropTypes.func.isRequired,
  control: PropTypes.object.isRequired,
  value: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  onChange: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  trigger: PropTypes.func,
  formFields: PropTypes.object,
  onCheckDuplicate: PropTypes.func,
};

export default ClauseCards;
