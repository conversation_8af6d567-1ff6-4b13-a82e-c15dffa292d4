import { z } from "zod";
import { noticeDetailsFormFields } from "./form-fields";

export const getNoticeDetailsSchema = (t) => {
  const { NOTICE_HEADING, CLAUSES } = noticeDetailsFormFields(t);

  return z.object({
    [NOTICE_HEADING.name]: z.string().optional(),
    [CLAUSES.name]: z.array(
      z
        .object({
          content: z.string().nullish(),
        })
        .optional(),
    ),
  });
};

export const getNoticeDetailsValidationSchema = (t) => {
  const { NOTICE_HEADING, CLAUSES } = noticeDetailsFormFields(t);

  return z.object({
    [NOTICE_HEADING.name]: z.preprocess(
      (value) => (value === "" ? null : value),
      z.string({ invalid_type_error: "Notice Heading is required" }),
    ),
    [CLAUSES.name]: z
      .array(
        z.object({
          content: z.preprocess(
            (value) => (value === "" ? null : value),
            z.string({ invalid_type_error: "Clause content is required" }),
          ),
        }),
      )
      .min(1, "At least one clause is required"),
  });
};
