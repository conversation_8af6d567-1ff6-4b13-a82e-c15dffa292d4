import { useLanguage } from "@/hooks";
import {
  <PERSON>alog,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  Button,
  Badge,
  ExpandableAccordion,
  ExpandableItem,
  ExpandableTrigger,
  PaginationSelect,
  Paginator,
  Pagination,
} from "@kla-v2/ui-components";
import { ArrowRight, XIcon, ExternalLink, Star } from "lucide-react";
import PropTypes from "prop-types";
import { useMemo, useState, useEffect } from "react";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useCheckDuplicateQuery } from "@/services/question";
import { useSearchParams } from "react-router-dom";

export default function CheckDuplicateModal({
  onClose,
  content,
  isOpen,
  variant,
}) {
  const { t } = useLanguage();
  const [matchType, setMatchType] = useState(variant);
  const [keywords, setKeywords] = useState([]);
  const [savedContent, setSavedContent] = useState("");
  const [searchParams, setSearchParams] = useSearchParams();
  const [pageByType, setPageByType] = useState({
    HEADING: 0,
    CLAUSE: 0,
  });
  const page = parseInt(searchParams.get("page") || "0");
  const size = searchParams.get("size") || "5";

  useEffect(() => {
    if (isOpen && content && content.trim()) {
      setKeywords([content]);
      setSavedContent(content);
    }
  }, [isOpen, content]);

  useEffect(() => {
    if (isOpen) {
      setMatchType(variant);
    }
  }, [isOpen, variant]);

  const { data: noticeList, isLoading } = useCheckDuplicateQuery({
    searchKeywords: keywords,
    matchType,
    page,
    size,
  });

  const updateParams = (newParams) => {
    const updatedParams = new URLSearchParams(searchParams);
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        updatedParams.set(key, value.toString());
      } else {
        updatedParams.delete(key);
      }
    });
    setSearchParams(updatedParams);
  };

  const handleClearAll = () => {
    setKeywords([]);
    setPageByType({
      HEADING: 0,
      CLAUSE: 0,
    });
  };

  const handleRemoveKeyword = (keywordToRemove) => {
    setKeywords(keywords.filter((keyword) => keyword !== keywordToRemove));
    setPageByType((prev) => ({ ...prev, [matchType]: 0 }));
  };

  const handleSearch = () => {
    setPageByType((prev) => ({ ...prev, [matchType]: 0 }));
  };

  const handleRecheckWithOriginal = () => {
    if (savedContent && savedContent.trim()) {
      setKeywords([savedContent]);
      setPageByType((prev) => ({ ...prev, [matchType]: 0 }));
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage - 1 });
  };

  const getTotalPages = () => {
    return noticeList?.totalPages ?? 0;
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 0 });
  };

  const getDisplayedPageInfo = () => {
    const totalElements = noticeList?.totalElements;
    const page = (noticeList?.page ?? 0) + 1;
    const size = noticeList?.size ?? 5;
    if (!totalElements) return "";

    const startIndex = (page - 1) * size + 1;
    const endIndex = Math.min(page * size, totalElements);
    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const paginationOptions = [
    {
      label: "5 per page",
      value: "5",
    },
    {
      label: "50 per page",
      value: "50",
    },
    {
      label: "100 per page",
      value: "100",
    },
  ];

  const highlightText = (text, searchTerms) => {
    if (!searchTerms?.length || !text) return text;
    try {
      let highlightedText = text;
      searchTerms.forEach((term) => {
        if (!term) return;
        const regex = new RegExp(`(${term})`, "gi");
        highlightedText = highlightedText
          .split(regex)
          .map((part, index) =>
            regex.test(part) ? (
              <span key={`highlight-${index}`} className="text-orange-600">
                {part}
              </span>
            ) : (
              part
            ),
          )
          .flat();
      });
      return highlightedText;
    } catch (error) {
      console.error("Highlight error:", error);
      return text;
    }
  };

  const highlightedNotices = useMemo(() => {
    if (!noticeList?.content || !keywords.length) return [];
    return noticeList.content.map((notice) => ({
      ...notice,
      highlightedContent: highlightText(notice.matchContent, keywords),
    }));
  }, [noticeList, keywords]);

  const handleTabChange = (value) => {
    setMatchType(value);
  };

  const totalPages = noticeList?.totalPages || 1;
  const currentPageNumber = pageByType[matchType] + 1;

  const renderNotices = (type) => {
    const filteredNotices = highlightedNotices.filter(
      (notice) => notice.matchType === type,
    );

    return (
      <div className="w-full mt-2">
        <ExpandableAccordion
          type="multiple"
          className="w-full flex flex-col gap-4"
        >
          {filteredNotices.map((notice, index) => (
            <ExpandableItem
              key={notice.noticeId || index}
              className="w-full"
              value={`item-${index}`}
            >
              <ExpandableTrigger hideChevron>
                <div className="flex items-center justify-between w-full rounded-lg transition group cursor-pointer">
                  <div className="flex w-full items-center gap-3 overflow-hidden">
                    <span className="font-semibold text-gray-800">
                      {notice.questionNumber}
                    </span>
                    {notice.starred && (
                      <Star
                        className="text-yellow-500 size-4"
                        fill="currentColor"
                      />
                    )}
                    <span className="text-gray-700 truncate">
                      {notice.highlightedContent || notice.matchContent}
                    </span>
                  </div>
                  <ExternalLink className="w-4 h-4" />
                </div>
              </ExpandableTrigger>
            </ExpandableItem>
          ))}
        </ExpandableAccordion>

        {totalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination
              currentPage={currentPageNumber}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="max-h-[90vh] max-w-[80vw] w-12rem flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>
              <div className="typography-page-heading">Check Duplicate</div>
            </DialogTitle>
          </DialogHeader>

          {/* Main content area using flex layout */}
          <div className="flex flex-col h-full">
            <Tabs
              value={matchType}
              onValueChange={handleTabChange}
              className="flex flex-col h-full"
            >
              {/* Tab list - fixed at top */}
              <TabsList
                variant="segmented"
                className="block w-full flex-shrink-0"
              >
                <TabsTrigger
                  value="HEADING"
                  variant="segmented"
                  className="w-1/4"
                >
                  {t("headingMatches")}
                </TabsTrigger>
                <TabsTrigger
                  value="CLAUSE"
                  variant="segmented"
                  className="w-1/4"
                >
                  {t("clauseMatches")}
                </TabsTrigger>
                <hr className="w-full" />
              </TabsList>

              <div className="flex p-3 flex-col items-start gap-3 flex-shrink-0 self-stretch bg-background rounded-md">
                <div className="flex w-full items-center">
                  <p className="text-grey-600">Keywords to Search :</p>
                </div>
                <div className="flex w-full items-center gap-2 flex-wrap">
                  {keywords.length > 0 ? (
                    keywords.map((keyword, index) => (
                      <Badge
                        key={`keyword-${index}`}
                        onClose={() => handleRemoveKeyword(keyword)}
                      >
                        {keyword}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-gray-400">No keywords</span>
                  )}
                </div>
                <div className="flex w-full justify-end items-center gap-4">
                  <div
                    className="cursor-pointer flex items-center gap-2 text-secondary"
                    onClick={handleClearAll}
                  >
                    <span className="typography-body-text-m-16">Clear All</span>
                    <XIcon className="size-4" />
                  </div>
                  {keywords.length === 0 && savedContent && (
                    <div
                      className="cursor-pointer flex items-center gap-2 text-primary"
                      onClick={handleRecheckWithOriginal}
                    >
                      <span className="typography-body-text-m-16">
                        Recheck Original
                      </span>
                    </div>
                  )}
                  <Button
                    size="md"
                    variant="primary"
                    iconPosition="right"
                    icon={ArrowRight}
                    type="button"
                    onClick={handleSearch}
                    disabled={keywords.length === 0}
                  >
                    {t("search")}
                  </Button>
                </div>
              </div>

              <div className="overflow-auto ">
                <TabsContent value="HEADING" className="flex flex-col">
                  <div className="p-3 bg-background rounded-md gap-4 h-[30vh] flex flex-col overflow-hidden">
                    <div className="flex w-full items-center flex-shrink-0">
                      <p className="text-grey-600">Matches :</p>
                    </div>

                    <div className="overflow-y-auto flex-grow pr-1">
                      {isLoading ? (
                        <SpinnerLoader />
                      ) : highlightedNotices.length ? (
                        renderNotices("HEADING")
                      ) : (
                        <div className="w-full text-center">
                          No matches found
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="CLAUSE" className="flex flex-col">
                  <div className="p-3 bg-background rounded-md gap-4 h-[30vh] flex flex-col overflow-hidden">
                    <div className="flex w-full items-center flex-shrink-0">
                      <p className="text-grey-600">Matches :</p>
                    </div>

                    <div className="overflow-y-auto flex-grow pr-1">
                      {isLoading ? (
                        <SpinnerLoader />
                      ) : highlightedNotices.length ? (
                        renderNotices("CLAUSE")
                      ) : (
                        <div className="w-full text-center">
                          No matches found
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
              </div>

              <div className="flex justify-between mt-2 p-3 border-t border-gray-200 bg-white flex-shrink-0">
                <div className="flex items-center gap-4">
                  <span className="typography-body-text-s-14 text-grey-600">
                    {getDisplayedPageInfo()}
                  </span>
                  <PaginationSelect
                    onChange={handlePageSizeChange}
                    defaultValue={size}
                    options={paginationOptions}
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Paginator
                    currentPage={(noticeList?.page ?? 0) + 1}
                    totalPages={getTotalPages()}
                    showPreviousNext={true}
                    onPageChange={handlePageChange}
                    disabled={isLoading}
                  />
                </div>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

CheckDuplicateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  content: PropTypes.string,
  variant: PropTypes.oneOf(["HEADING", "CLAUSE"]),
};
