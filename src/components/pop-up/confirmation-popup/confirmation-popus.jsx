import PropTypes from "prop-types";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  RadioGroup,
  RadioGroupItem,
} from "@kla-v2/ui-components";
import { FormProvider, useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { useLanguage } from "@/hooks";

const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Do you want to Proceed?",
  subtitle = "",
  showRadioOptions = false,
  radioOptions = [],
  warningText = "These changes will impact the notice and can't be undone. Please review all updates carefully before confirming.",
  confirmButtonText = "Confirm",
  cancelButtonText = "Cancel",
  defaultRadioValue = "",
  cancelButtonSize = "sm",
  confirmButtonSize = "sm",
}) => {
  const form = useForm({
    defaultValues: {
      selectedOption:
        defaultRadioValue ||
        (radioOptions.length > 0 ? radioOptions[0].value : ""),
    },
  });

  const { handleSubmit } = form;
  const { t } = useLanguage();

  const handleConfirm = (data) => {
    if (onConfirm) {
      onConfirm(data.selectedOption);
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="w-[500px] max-w-[90vw] p-0">
          <div className="p-6">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-xl font-semibold">
                {title}
              </DialogTitle>
              {subtitle && (
                <DialogDescription className="text-gray-500 mt-1">
                  {subtitle}
                </DialogDescription>
              )}
            </DialogHeader>

            <FormProvider {...form}>
              <form
                onSubmit={handleSubmit(handleConfirm)}
                className="space-y-6"
                role="form"
                data-testid="confirmation-form"
              >
                {showRadioOptions && radioOptions.length > 0 && (
                  <FormField
                    name="selectedOption"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex gap-8"
                          >
                            {radioOptions.map((option) => (
                              <div
                                key={option.value}
                                className="flex items-center"
                              >
                                <RadioGroupItem
                                  value={option.value}
                                  id={option.value}
                                  className="text-pink-500 border-pink-500"
                                />
                                <FormLabel
                                  htmlFor={option.value}
                                  className="cursor-pointer"
                                >
                                  {option.label}
                                </FormLabel>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                )}

                {warningText && (
                  <div className="text-grey-600 text-md">{warningText}</div>
                )}

                <div className="flex justify-end gap-3 pt-2">
                  <DialogClose asChild>
                    <Button size={cancelButtonSize} variant="neutral">
                      {t(cancelButtonText)}
                    </Button>
                  </DialogClose>
                  <Button
                    type="submit"
                    size={confirmButtonSize}
                    variant="primary"
                  >
                    {t(confirmButtonText)}
                  </Button>
                </div>
              </form>
            </FormProvider>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ConfirmationModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  showRadioOptions: PropTypes.bool,
  radioOptions: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  warningText: PropTypes.string,
  confirmButtonText: PropTypes.string,
  cancelButtonText: PropTypes.string,
  defaultRadioValue: PropTypes.string,
  cancelButtonSize: PropTypes.oneOf(["xs", "sm", "md", "lg", "xl"]),
  confirmButtonSize: PropTypes.oneOf(["xs", "sm", "md", "lg", "xl"]),
};

export default ConfirmationModal;
