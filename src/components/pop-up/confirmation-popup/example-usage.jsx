import { useState } from "react";
import { Button } from "@kla-v2/ui-components";
import ConfirmationModal from "./confirmation-popus";
import { MoveRightIcon } from "lucide-react";

const ModalExamples = () => {
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false);
  const [isPartialDisallowModalOpen, setIsPartialDisallowModalOpen] =
    useState(false);
  const [isAdmitModalOpen, setIsAdmitModalOpen] = useState(false);
  const [isDisallowModalOpen, setIsDisallowModalOpen] = useState(false);
  const [isPrivateMemberWithdrawOpen, setIsPrivateMemberWithdrawOpen] =
    useState(false);

  const handleWithdrawConfirm = () => {};
  const handlePartialDisallowConfirm = () => {};
  const handleAdmitConfirm = () => {};
  const handleDisallowConfirm = () => {};
  const handlePrivateMemberWithdrawConfirm = () => {};

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Confirmation Modals</h2>
      <div className="flex flex-wrap gap-4">
        <Button
          variant="primary"
          icon={MoveRightIcon}
          iconPosition="right"
          onClick={() => setIsWithdrawModalOpen(true)}
        >
          Withdraw
        </Button>

        <Button
          variant="primary"
          onClick={() => setIsPartialDisallowModalOpen(true)}
        >
          Partial-Disallow Modal
        </Button>

        <Button variant="primary" onClick={() => setIsAdmitModalOpen(true)}>
          Admit Modal
        </Button>

        <Button variant="primary" onClick={() => setIsDisallowModalOpen(true)}>
          Open Disallow Modal
        </Button>
        <Button
          variant="primary"
          icon={MoveRightIcon}
          iconPosition="right"
          onClick={() => setIsPrivateMemberWithdrawOpen(true)}
        >
          PM Withdraw
        </Button>
      </div>

      {/* Withdraw Modal (with radio options) */}
      <ConfirmationModal
        isOpen={isWithdrawModalOpen}
        onClose={() => setIsWithdrawModalOpen(false)}
        onConfirm={handleWithdrawConfirm}
        title="Do you want to Withdraw?"
        subtitle="Withdraw from"
        showRadioOptions={true}
        radioOptions={[
          { value: "notice", label: "Notice" },
          { value: "clubbedMembers", label: "Clubbed Members" },
        ]}
        defaultRadioValue="notice"
      />

      {/* Partial-Disallow Modal */}
      <ConfirmationModal
        isOpen={isPartialDisallowModalOpen}
        onClose={() => setIsPartialDisallowModalOpen(false)}
        onConfirm={handlePartialDisallowConfirm}
        title="Do you want to Proceed Partial-Disallow?"
        showRadioOptions={false}
      />

      {/* Admit Modal */}
      <ConfirmationModal
        isOpen={isAdmitModalOpen}
        onClose={() => setIsAdmitModalOpen(false)}
        onConfirm={handleAdmitConfirm}
        title="Do you want to Proceed Admit?"
        showRadioOptions={false}
      />

      {/* Disallow Modal */}
      <ConfirmationModal
        isOpen={isDisallowModalOpen}
        onClose={() => setIsDisallowModalOpen(false)}
        onConfirm={handleDisallowConfirm}
        title="Do you want to Proceed Disallow?"
        showRadioOptions={false}
      />

      {/* Withdraw Modal (PM Notice) */}
      <ConfirmationModal
        isOpen={isPrivateMemberWithdrawOpen}
        onClose={() => setIsPrivateMemberWithdrawOpen(false)}
        onConfirm={handlePrivateMemberWithdrawConfirm}
        title="Do you want to Withdraw Notice?"
        cancelButtonSize="md"
        confirmButtonSize="md"
      />
    </div>
  );
};

export default ModalExamples;
