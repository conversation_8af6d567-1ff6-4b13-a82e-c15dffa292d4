import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  toast,
  Checkbox,
  DialogPortal,
} from "@kla-v2/ui-components";
import { useState } from "react";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";

const RULES = [
  ["a", "Questions asking for information of statistical nature ;"],
  [
    "b",
    "Questions going into too many details and where it is obvious that replay will be a long one, though not necessary statistical nature;",
  ],
  ["c", "Questions generally..."],
];

const DISALLOW_RULES = [
  ["a", "Questions asking for information of statistical nature ;"],
  [
    "b",
    "Questions going into too many details and where it is obvious that replay will be a long one, though not necessary statistical nature;",
  ],
  ["c", "Questions generally..."],
];

const ClausePopUpModal = ({ isOpen, onClose, isPreview = false }) => {
  const { t } = useLanguage();
  const [mode] = useState("list");
  const [selectedRules, setSelectedRules] = useState({
    list: {},
    manual: {},
  });

  const mapRules = (data) =>
    data.map(([letter, description]) => ({
      id: `rule-${letter}`,
      label: `(${letter})`,
      description,
      key: letter,
    }));

  const rules = mode === "manual" ? mapRules(DISALLOW_RULES) : mapRules(RULES);

  const handleCheckboxChange = (id) => {
    setSelectedRules((prev) => {
      const updatedRules = { ...prev[mode] };
      updatedRules[id] = {
        ...updatedRules[id],
        selected: !updatedRules[id]?.selected,
      };
      return { ...prev, [mode]: updatedRules };
    });
  };

  const handleSubmit = () => {
    try {
      onClose();
      toast.success("Success", { description: t("Submitted") });
    } catch (err) {
      toast.error("Error submitting document", {
        description: err?.message,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[900px] min-h-[500px] h-auto flex flex-col">
          <DialogHeader>
            <DialogTitle>As Per Direction No. 1</DialogTitle>
            <div className="text-sm text-gray-600 mt-2">
              Fill the field(s) below to add As Per Direction No. 1
            </div>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>

          <div className="flex-1 overflow-y-auto mr-4 py-4">
            <div className="mb-4 text-md">
              In accordance with established practice the following types of
              questions which are given notice of as starred will be transferred
              to the unstarred list:-
            </div>
            {rules.map(({ id, label, description }) => {
              const { selected = false } = selectedRules[mode][id] || {};
              return (
                <div key={id} className="px-3 py-3">
                  <div className="flex items-start">
                    <Checkbox
                      className="mr-3 mt-1"
                      checked={selected}
                      onCheckedChange={() => handleCheckboxChange(id)}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium mb-1">{label}</div>
                      <div className="text-sm text-gray-500">{description}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <DialogFooter className="flex justify-center items-center gap-4 pt-6 px-6">
            <div className="flex justify-center gap-4 w-full">
              {isPreview ? (
                <DialogClose asChild>
                  <Button size="sm" variant="primary">
                    {t("close")}
                  </Button>
                </DialogClose>
              ) : (
                <>
                  <DialogClose asChild>
                    <Button variant="neutral" size="lg">
                      Cancel
                    </Button>
                  </DialogClose>
                  <DialogClose asChild>
                    <Button size="lg" variant="primary" onClick={handleSubmit}>
                      Proceed
                    </Button>
                  </DialogClose>
                </>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ClausePopUpModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  isPreview: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
};

export default ClausePopUpModal;
