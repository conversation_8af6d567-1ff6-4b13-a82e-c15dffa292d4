import PropTypes from "prop-types";
import { useEffect } from "react";
import {
  <PERSON>ton,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Combobox,
} from "@kla-v2/ui-components";
import { FormProvider, useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { InfoIcon } from "lucide-react";

const ReturnSetupModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "",
  subtitle = "",
  showReasonInput = false,
  reasonLabel = "",
  addButtonText = "",
  cancelButtonText = "Cancel",
}) => {
  //   const [reasonValue, setReasonValue] = useState("");

  const form = useForm({
    defaultValues: {
      reason: "",
    },
  });

  const { handleSubmit, reset } = form;
  const { t } = useLanguage();

  const handleConfirm = (data) => {
    if (onConfirm) {
      onConfirm(showReasonInput ? data : {});
    }
    onClose();
  };
  const handleClose = () => {
    reset({
      reason: "",
    });
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      reset({
        reason: "",
      });
    }
  }, [isOpen, reset]);
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPortal>
        <DialogContent className="w-[500px] max-w-[90vw] p-0">
          <div className="p-6">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-xl font-semibold">
                {title}
              </DialogTitle>
              {subtitle && (
                <DialogDescription className="text-gray-500 mt-1">
                  {subtitle}
                </DialogDescription>
              )}
              <hr className="border border-border-1 opacity-30" />
            </DialogHeader>
            <FormProvider {...form}>
              <form
                onSubmit={handleSubmit(handleConfirm)}
                className=""
                role="form"
                data-testid="confirmation-form"
              >
                <Combobox
                  className="w-full"
                  label="Section"
                  placeholder="Question Section"
                  // onValueChange={handleValueChange}
                  options={[]}
                  isMulti={false}
                  modalPopover={true}
                  showAvatar
                  disabled
                />
                {showReasonInput && (
                  <FormField
                    name="reason"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <div className="flex items-center">
                          <FormLabel className="text-sm font-medium text-slate-600">
                            {reasonLabel}
                          </FormLabel>

                          <span className="ml-1 text-slate-600">
                            <InfoIcon size={16} />
                          </span>
                        </div>
                        <FormControl>
                          <textarea
                            {...field}
                            className="w-full border border-gray-300 rounded-md p-3 min-h-[100px] focus:outline-none focus:ring-2 focus:ring-pink-500"
                            placeholder="Type your reason here"
                            // onChange={(e) => {
                            //   field.onChange(e);
                            //   setReasonValue(e.target.value);
                            // }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                )}
                <div className="flex justify-end gap-3 pt-2">
                  <DialogClose asChild>
                    <Button size="lg" variant="neutral" onClick={handleClose}>
                      {t(cancelButtonText)}
                    </Button>
                  </DialogClose>
                  <Button type="submit" size="lg" variant="primary">
                    {t(addButtonText)}
                  </Button>
                </div>
              </form>
            </FormProvider>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ReturnSetupModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  showReasonInput: PropTypes.bool,
  reasonLabel: PropTypes.string,
  addButtonText: PropTypes.string,
  cancelButtonText: PropTypes.string,
};

export default ReturnSetupModal;
