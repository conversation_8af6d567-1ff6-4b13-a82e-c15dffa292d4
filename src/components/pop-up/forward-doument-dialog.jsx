import { useState, useEffect } from "react";
import PropTypes from "prop-types";

import { useLanguage } from "@/hooks";

import {
  Button,
  Combobox,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Textarea,
} from "@kla-v2/ui-components";

const ForwardDocumentDialog = ({ isOpen, onClose }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [reason, setReason] = useState("");

  const { t } = useLanguage();

  const options = [
    {
      avatar: "https://placehold.co/100x100",
      description: "<PERSON><PERSON>",
      label: "Section Officer",
      value: "section-officer",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Under Secretary",
      description: "<PERSON><PERSON>",
      value: "under-secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Deputy Secretary",
      description: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      value: "deputy-secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Joint Secretary",
      description: "<PERSON>",
      value: "joint-secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Additional Secretary",
      description: "Lakshmi Kutty",
      value: "additional-secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Special Secretary",
      description: "K J Joseph",
      value: "special-secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Secretary",
      description: "Abdul Rahman",
      value: "secretary",
    },
    {
      avatar: "https://placehold.co/100x100",
      label: "Speaker",
      description: "A N Samseer",
      value: "speaker",
    },
  ];

  useEffect(() => {
    if (!isOpen) {
      setSelectedOption(null);
      setReason("");
    }
  }, [isOpen]);

  // Determine if reason is required (if first option is not selected)
  const isReasonRequired =
    selectedOption && selectedOption.value !== options[0].value;

  const isForwardDisabled =
    !selectedOption || (isReasonRequired && !reason.trim());

  const handleValueChange = (value) => {
    const selected = options.find((option) => option.value === value);
    setSelectedOption(selected);
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="flex flex-col">
          <DialogHeader>
            <DialogTitle>{t("forwardDocument")}</DialogTitle>
            <DialogDescription>
              {t("forwardDocumentConfirmation")}
            </DialogDescription>
          </DialogHeader>

          <div className="grid py-2">
            <Combobox
              className="w-full"
              label="Seat"
              placeholder="Select seat"
              onValueChange={handleValueChange}
              options={options}
              isMulti={false}
              modalPopover={true}
              showAvatar
            />

            {isReasonRequired && (
              <Textarea
                id="reason"
                label="Add Reason"
                infoText="Please provide a reason for forwarding the document."
                placeholder="Type your reason here"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[100px]"
              />
            )}
          </div>

          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button variant="neutral">{t("cancel")}</Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                variant="primary"
                onClick={() => {}}
                disabled={isForwardDisabled}
              >
                {t("forward")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ForwardDocumentDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ForwardDocumentDialog;
