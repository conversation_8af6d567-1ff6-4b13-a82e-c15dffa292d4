import { cn } from "@/utils";
import PropTypes from "prop-types";

const AccordionTitle = ({
  accordionOrderNo,
  accordionTitle,
  isColored = true,
}) => {
  return (
    <div className={"flex gap-2 items-center"}>
      <div
        className={cn(
          "rounded-full w-5 h-5 bg-grey-200 text-primary flex items-center justify-center text-sm leading-4",
          { "number-container": isColored },
        )}
      >
        {accordionOrderNo}
      </div>
      <p className={cn({ "text-primary": !isColored })}>{accordionTitle}</p>
    </div>
  );
};
AccordionTitle.propTypes = {
  accordionOrderNo: PropTypes.number,
  accordionTitle: PropTypes.string,
  isColored: PropTypes.bool,
};
export { AccordionTitle };
