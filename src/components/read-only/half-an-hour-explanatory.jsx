import { AccordionTitle } from "@/components/accordion-title";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { HalfAnHourRichTextPage } from "@/pages/half-an-hour-discussion/components/explanatory-note/half-an-hour-rich-text";

const HalfAnHourExplanatoryReadOnly = ({ accordionOrderNo, data }) => {
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Notice Content"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <HalfAnHourRichTextPage explanatoryDetails={data} />
        </div>
      </ExpandableContent>
    </>
  );
};

HalfAnHourExplanatoryReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
};

export { HalfAnHourExplanatoryReadOnly };
