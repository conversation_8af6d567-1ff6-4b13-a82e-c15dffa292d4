import { AccordionTitle } from "@/components/accordion-title";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import ContentData from "../ui/content-data";
import CompareIcon from "@/icons/compare-icon";
import { useState } from "react";
import CheckDuplicateModal from "../pop-up/check-duplicate";
import { useLanguage } from "@/hooks";

const NoriceDetailsReadOnly = ({ accordionOrderNo, data }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState("");
  const { t } = useLanguage();

  let htmlContent = "";

  try {
    const json = data?.description ?? null;
    htmlContent = json ?? "";
  } catch (error) {
    console.error("Invalid JSON in data.description:", error);
  }
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Notice Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            <div className="flex items-end w-full gap-1">
              {data?.noticeHeading && (
                <ContentData
                  className="w-full flex-1"
                  title={t("noticeHeading")}
                  text={data?.noticeHeading}
                />
              )}
              <div
                className="cursor-pointer flex items-end justify-center"
                onClick={() => {
                  if (data?.noticeHeading) {
                    setModalContent(data.noticeHeading);
                    setIsModalOpen(true);
                  }
                }}
              >
                <CompareIcon />
              </div>
            </div>

            <CheckDuplicateModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              content={modalContent}
              variant="HEADING"
            />

            {htmlContent && (
              <div className="border border-gray-300 rounded-lg">
                <div className="w-full h-full leading-8 px-8 py-12">
                  <div
                    id="pdf-content"
                    dangerouslySetInnerHTML={{ __html: htmlContent }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        {Array.isArray(data?.clauses) && data.clauses.length > 0 && (
          <div className="w-full flex flex-col gap-4 p-6">
            <h4 className="text-black font-bold text-sm">{t("clause")}</h4>
            {data.clauses.map((clause, index) => (
              <ContentData
                key={clause.id || index}
                className="w-full"
                title={`Clause ${index + 1}`}
                text={clause.content || "---"}
              />
            ))}
          </div>
        )}
      </ExpandableContent>
    </>
  );
};

NoriceDetailsReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
};

export { NoriceDetailsReadOnly };
