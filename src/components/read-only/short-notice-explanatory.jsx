import { AccordionTitle } from "@/components/accordion-title";
import { RichTextPage } from "@/pages/short-notice/components/explanatory-note/rich-text-page";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";

const ShortNoticeExplanatoryReadOnly = ({ accordionOrderNo, data }) => {
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Notice Content"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <RichTextPage explanatoryDetails={data} />
        </div>
      </ExpandableContent>
    </>
  );
};

ShortNoticeExplanatoryReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
};

export { ShortNoticeExplanatoryReadOnly };
