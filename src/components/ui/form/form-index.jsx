import { cn } from "@/utils";
import { Label } from "@kla-v2/ui-components";
import { Slot } from "@radix-ui/react-slot";
import PropTypes from "prop-types";
import { createContext, forwardRef, useContext, useId } from "react";
import { Controller, FormProvider, useFormContext } from "react-hook-form";

const Form = FormProvider;

const FormFieldContext = createContext({});

const FormField = (props) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};
FormField.propTypes = {
  name: PropTypes.string,
};

const useFormField = () => {
  const fieldContext = useContext(FormFieldContext);
  const itemContext = useContext(FormItemContext);

  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error("useFormField should be used within <FormField>");
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

const FormItemContext = createContext({});

const FormItem = forwardRef(({ ...props }, ref) => {
  const id = useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} {...props} />
    </FormItemContext.Provider>
  );
});
FormItem.displayName = "FormItem";

const FormControl = forwardRef(({ ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-invalid={!!error}
      error={error?.message}
      {...props}
    />
  );
});
FormControl.displayName = "FormControl";

const FormLabel = forwardRef(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Label
      ref={ref}
      className={cn(error && "text-destructive", className)}
      htmlFor={formItemId}
      {...props}
    />
  );
});
FormLabel.displayName = "FormLabel";
FormLabel.propTypes = {
  className: PropTypes.string, // Add PropTypes validation
};

export { Form, FormControl, FormField, FormItem, FormLabel };
