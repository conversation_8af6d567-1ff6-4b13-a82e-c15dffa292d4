# Master Data Management Service (MDMS) - API Implementation Specification

## Overview

This document specifies the exact API endpoints that need to be implemented or enhanced in the **core-mdm-service** to replace frontend mocks and improve existing functionality.

**Service Location**: `/Users/<USER>/Developer/kla/core/core-mdm-service`
**Base Package**: `com.niyamasabha.core.mdm`
**Current Port**: 8080 (based on existing configuration)

## Implementation Priority

### Critical Priority (Week 1)
1. Active Assembly API
2. User Profile API  
3. Assembly Sessions API

### Medium Priority (Week 2-3)
4. Enhanced Assembly Management
5. Enhanced Designation Management
6. Calendar of Sittings

### Low Priority (Week 3+)
7. Private Member APIs
8. Places List API

---

## Critical Priority APIs

### 1. Active Assembly API

**Current Mock**: `GET /api/active-assembly`
**Current Response**:
```json
{
  "assembly": "15",
  "session": "1"
}
```

**Used By**: 
- Throughout application for current assembly/session context
- `src/services/master-data-management/active-assembly.js`
- Multiple pages for form defaults

**NEW Implementation**:

**Controller**: `ActiveAssemblyController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/active-assembly")
@RequiredArgsConstructor
public class ActiveAssemblyController {
    
    private final ActiveAssemblyService activeAssemblyService;
    
    @GetMapping
    public ResponseEntity<ActiveAssemblyResponse> getActiveAssembly() {
        return ResponseEntity.ok(activeAssemblyService.getActiveAssembly());
    }
}
```

**Contract**: `ActiveAssemblyResponse.java` (NEW FILE)
```java
public class ActiveAssemblyResponse {
    private Integer assembly;
    private Integer session;
    private String assemblyName;
    private String assemblyNameInLocal;
    private String sessionName;
    private String sessionNameInLocal;
    private LocalDate assemblyStartDate;
    private LocalDate sessionStartDate;
    private LocalDate sessionEndDate;
    private Boolean isActive;
}
```

**Service Method**:
```java
public ActiveAssemblyResponse getActiveAssembly() {
    Assembly activeAssembly = assemblyRepository.findByStatus(AssemblyStatus.ACTIVE)
        .orElseThrow(() -> new LookupNotFound("No active assembly found"));
    
    Session activeSession = sessionRepository.findByAssemblyAndStatus(
        activeAssembly, SessionStatus.ACTIVE)
        .orElseThrow(() -> new LookupNotFound("No active session found"));
    
    return ActiveAssemblyResponse.builder()
        .assembly(activeAssembly.getNumber())
        .session(activeSession.getNumber())
        .assemblyName(activeAssembly.getName())
        .assemblyNameInLocal(activeAssembly.getNameInLocal())
        .sessionName(activeSession.getName())
        .sessionNameInLocal(activeSession.getNameInLocal())
        .assemblyStartDate(activeAssembly.getStartDate())
        .sessionStartDate(activeSession.getStartDate())
        .sessionEndDate(activeSession.getEndDate())
        .isActive(true)
        .build();
}
```

**Database Changes Required**:
```sql
-- Add nameInLocal to assemblies table
ALTER TABLE assemblies ADD COLUMN name_in_local VARCHAR(255);

-- Create sessions table if not exists
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assembly_id UUID NOT NULL REFERENCES assemblies(id),
    number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_in_local VARCHAR(255),
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'INACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 2. User Profile API

**Current Mock**: `GET /api/user/current-profile`
**Current Response**:
```json
{
  "content": [
    {
      "userid": "550e8400-e29b-41d4-a716-************",
      "username": "pinarayi",
      "email": "<EMAIL>",
      "memberDisplayName": "Pinarayi Vijayan",
      "memberDisplayNameInLocal": "പിണറായി വിജയൻ",
      "roles": "MINISTER",
      "profileURL": "https://randomuser.me/api/portraits/men/75.jpg",
      "ministerDesignation": "Chief Minister of Kerala",
      "ministerDesignationInLocal": "കേരള മുഖ്യമന്ത്രി",
      "ministerId": 123,
      "memberId": 1234,
      "portfolioId": 456,
      "portfolio": "Home Affairs, Vigilance",
      "portfolioInLocal": "ആഭ്യന്തരം, വിജിലൻസ്",
      "constituencyId": 500,
      "constituencyName": "Kozhikode North",
      "constituencyNameInLocal": "കോഴിക്കോട് നോർത്ത്",
      "politicalPartyId": 55,
      "politicalPartyName": "United Democratic Front",
      "politicalPartyNameInLocal": "യുണൈറ്റഡ് ഡെമോക്രാറ്റിക് ഫ്രണ്ട്",
      "isActive": true
    }
  ]
}
```

**Used By**:
- Authentication and authorization throughout app
- User context and role-based access control
- Profile display components

**NEW Implementation**:

**Controller**: `UserProfileController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserProfileController {
    
    private final UserProfileService userProfileService;
    
    @GetMapping("/current-profile")
    public ResponseEntity<UserProfileResponse> getCurrentUserProfile(
            Authentication authentication) {
        String username = authentication.getName();
        return ResponseEntity.ok(userProfileService.getCurrentUserProfile(username));
    }
}
```

**Contract**: `UserProfileResponse.java` (NEW FILE)
```java
public class UserProfileResponse {
    private String userid;
    private String username;
    private String email;
    private String memberDisplayName;
    private String memberDisplayNameInLocal;
    private String displayName;
    private String displayNameInLocal;
    private String roles;
    private String profileURL;
    private String userType;
    
    // Minister-specific fields
    private String ministerDesignation;
    private String ministerDesignationInLocal;
    private Long ministerId;
    private Long portfolioId;
    private String portfolio;
    private String portfolioInLocal;
    
    // Member-specific fields
    private Long memberId;
    private Long constituencyId;
    private String constituencyName;
    private String constituencyNameInLocal;
    private String constituencyNumber;
    private Long politicalPartyId;
    private String politicalPartyName;
    private String politicalPartyNameInLocal;
    
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
}
```

---

### 3. Assembly Sessions API

**Current Mock**: `GET /api/get/assembly/{klaId}/sessions`
**Current Response**:
```json
[
  {
    "id": 1,
    "name": "Session 1A"
  },
  {
    "id": 2,
    "name": "Session 1B"
  }
]
```

**Used By**:
- Assembly/session dropdowns in forms
- Session selection components

**NEW Implementation**:

**Add to existing AssemblyController**:
```java
@GetMapping("/{assemblyNumber}/sessions")
public ResponseEntity<List<SessionResponse>> getSessionsByAssembly(
        @PathVariable Integer assemblyNumber,
        @RequestParam(required = false) String status) {
    return ResponseEntity.ok(assemblyService.getSessionsByAssembly(assemblyNumber, status));
}
```

**Contract**: `SessionResponse.java` (NEW FILE)
```java
public class SessionResponse {
    private UUID id;
    private Integer number;
    private String name;
    private String nameInLocal;
    private LocalDate startDate;
    private LocalDate endDate;
    private String status;
    private Integer assemblyNumber;
}
```

---

## Medium Priority APIs

### 4. Enhanced Assembly Management

**Current Implementation**: `GET /assemblies` - Basic list without pagination
**Enhancement Needed**: Add pagination, search, and nameInLocal support

**Update AssemblyController**:
```java
@GetMapping
public ResponseEntity<PageResponse<AssemblyResponse>> getAssemblies(
        @RequestParam(required = false) AssemblyStatus status,
        @RequestParam(required = false) String search,
        @RequestParam(defaultValue = "0") Integer page,
        @RequestParam(defaultValue = "10") Integer size,
        @RequestParam(defaultValue = "number") String sortBy,
        @RequestParam(defaultValue = "desc") String sortDirection) {
    
    Pageable pageable = PageRequest.of(page, size, 
        Sort.Direction.fromString(sortDirection), sortBy);
    
    return ResponseEntity.ok(assemblyService.getAssemblies(
        status, search, pageable));
}
```

**Enhanced AssemblyResponse**:
```java
public class AssemblyResponse {
    private UUID id;
    private Integer number;
    private String name;
    private String nameInLocal;  // NEW FIELD
    private LocalDate startDate;
    private LocalDate endDate;
    private AssemblyStatus status;
    private Integer totalSessions;
    private Integer currentSession;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
}
```

**NEW Endpoint**: Assemblies with Sessions
```java
@GetMapping("/assemblies-sessions")
public ResponseEntity<AssembliesSessionsResponse> getAssembliesWithSessions(
        @RequestParam(defaultValue = "5") Integer limit,
        @RequestParam(defaultValue = "0") Integer offset,
        @RequestParam(defaultValue = "false") Boolean includeActiveOnly) {
    return ResponseEntity.ok(assemblyService.getAssembliesWithSessions(
        limit, offset, includeActiveOnly));
}
```

**Contract**: `AssembliesSessionsResponse.java` (NEW FILE)
```java
public class AssembliesSessionsResponse {
    private List<AssemblyWithSessionsResponse> assemblies;
    private UUID activeAssemblyId;
    private UUID activeSessionId;
    private PaginationInfo pagination;
}

public class AssemblyWithSessionsResponse {
    private UUID id;
    private Integer number;
    private String name;
    private String nameInLocal;
    private LocalDate startDate;
    private LocalDate endDate;
    private String status;
    private Integer totalSessions;
    private List<SessionResponse> sessions;
}
```

---

### 5. Enhanced Designation Management

**Current Implementation**: `GET /designations` - Basic list without pagination
**Enhancement Needed**: Add pagination, search, filtering

**Update DesignationController**:
```java
@GetMapping
public ResponseEntity<PageResponse<DesignationResponse>> getAllDesignations(
        @RequestParam(required = false) String search,
        @RequestParam(required = false) Boolean ministerial,
        @RequestParam(required = false) Boolean active,
        @RequestParam(defaultValue = "0") Integer page,
        @RequestParam(defaultValue = "20") Integer size,
        @RequestParam(defaultValue = "priority") String sortBy,
        @RequestParam(defaultValue = "asc") String sortDirection) {
    
    Pageable pageable = PageRequest.of(page, size,
        Sort.Direction.fromString(sortDirection), sortBy);
    
    return ResponseEntity.ok(designationService.getAllDesignations(
        search, ministerial, active, pageable));
}

@GetMapping("/ministerial")
public ResponseEntity<List<DesignationResponse>> getMinisterialDesignations() {
    return ResponseEntity.ok(designationService.getMinisterialDesignations());
}
```

**Enhanced DesignationResponse**:
```java
public class DesignationResponse {
    private UUID id;
    private String name;
    private String nameInLocal;  // NEW FIELD
    private Boolean isMinisterial;
    private Integer priority;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
}
```

---

### 6. Calendar of Sittings API

**Current Mock**: `GET /api/calendar-of-sittings`
**Current Response**:
```json
{
  "data": [
    {
      "id": 1,
      "date": "2024-10-01T00:00:00.000Z",
      "entryType": "government",
      "entries": [
        {
          "description": "Annual private business review",
          "descriptionInLocal": "വാർഷിക സ്വകാര്യ ബിസിനസ് അവലോകനം"
        }
      ]
    }
  ]
}
```

**NEW Implementation**:

**Controller**: `CalendarOfSittingsController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/calendar-of-sittings")
@RequiredArgsConstructor
public class CalendarOfSittingsController {
    
    private final CalendarOfSittingsService calendarService;
    
    @GetMapping
    public ResponseEntity<CalendarOfSittingsResponse> getCalendarOfSittings(
            @RequestParam(required = false) Integer assemblyNumber,
            @RequestParam(required = false) Integer sessionNumber,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String businessType) {
        
        return ResponseEntity.ok(calendarService.getCalendarOfSittings(
            assemblyNumber, sessionNumber, startDate, endDate, businessType));
    }
}
```

**Contracts**:
```java
public class CalendarOfSittingsResponse {
    private List<CalendarEntryResponse> data;
}

public class CalendarEntryResponse {
    private UUID id;
    private LocalDate date;
    private String entryType;
    private List<BusinessEntryResponse> entries;
    private Integer sittingNumber;
    private String startTime;
    private String endTime;
    private Boolean isHoliday;
    private Integer assemblyNumber;
    private Integer sessionNumber;
}

public class BusinessEntryResponse {
    private String description;
    private String descriptionInLocal;
    private String businessType;
}
```

---

## Low Priority APIs

### 7. Private Member APIs

**Current Mocks**: 
- `GET /api/private-member-resolutions`
- `GET /api/private-member-bills`

**NEW Implementation**:

**Controller**: `PrivateMemberController.java` (NEW FILE)
```java
@RestController
@RequestMapping("/private-member")
@RequiredArgsConstructor
public class PrivateMemberController {
    
    private final PrivateMemberService privateMemberService;
    
    @GetMapping("/resolutions")
    public ResponseEntity<PrivateMemberListResponse> getResolutions() {
        return ResponseEntity.ok(privateMemberService.getResolutions());
    }
    
    @GetMapping("/bills")
    public ResponseEntity<PrivateMemberListResponse> getBills() {
        return ResponseEntity.ok(privateMemberService.getBills());
    }
    
    @GetMapping("/members")
    public ResponseEntity<List<MemberResponse>> getPrivateMembers() {
        return ResponseEntity.ok(privateMemberService.getPrivateMembers());
    }
}
```

### 8. Places List API

**Current Mock**: `GET /api/places-list`
**Current Response**:
```json
{
  "value": "Trivandrum",
  "label": "Trivandrum"
}
```

**NEW Implementation**:
```java
@GetMapping("/places-list")
public ResponseEntity<List<PlaceResponse>> getPlacesList() {
    return ResponseEntity.ok(placeService.getAllPlaces());
}
```

**Contract**:
```java
public class PlaceResponse {
    private String value;
    private String label;
    private String labelInLocal;
}
```

---

## Database Schema Updates Required

### 1. Add nameInLocal columns
```sql
-- Assemblies
ALTER TABLE assemblies ADD COLUMN name_in_local VARCHAR(255);

-- Designations  
ALTER TABLE designations ADD COLUMN name_in_local VARCHAR(255);

-- Portfolio Subjects
ALTER TABLE portfolio_subjects ADD COLUMN name_in_local VARCHAR(255);

-- Political Parties
ALTER TABLE political_parties ADD COLUMN name_in_local VARCHAR(255);

-- Constituencies
ALTER TABLE constituencies ADD COLUMN name_in_local VARCHAR(255);
```

### 2. Create Sessions table
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assembly_id UUID NOT NULL REFERENCES assemblies(id),
    number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_in_local VARCHAR(255),
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'INACTIVE',
    session_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(assembly_id, number)
);
```

### 3. Create Calendar of Sittings tables
```sql
CREATE TABLE calendar_of_sittings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assembly_id UUID NOT NULL REFERENCES assemblies(id),
    session_id UUID REFERENCES sessions(id),
    sitting_date DATE NOT NULL,
    sitting_number INTEGER,
    start_time TIME,
    end_time TIME,
    is_holiday BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sitting_business_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    calendar_sitting_id UUID NOT NULL REFERENCES calendar_of_sittings(id),
    entry_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    description_in_local TEXT,
    business_type VARCHAR(100),
    order_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## Standard Response Wrappers

### Pagination Response
```java
public class PageResponse<T> {
    private List<T> content;
    private Integer totalElements;
    private Integer totalPages;
    private Integer pageSize;
    private Integer pageNumber;
    private Boolean first;
    private Boolean last;
    private Boolean hasNext;
    private Boolean hasPrevious;
    private Boolean empty;
}
```

### Simple List Response
```java
public class ListResponse<T> {
    private List<T> data;
    private Integer totalCount;
}
```

---

## Implementation Notes

1. **Follow existing patterns** from current MDM service controllers
2. **Use existing exception handling** (`GlobalExceptionHandler`, `LookupNotFound`)
3. **Maintain cache clearing endpoints** for each new controller
4. **Add proper validation** using Spring Validation annotations
5. **Use existing audit fields** pattern from `BaseEntity`
6. **Follow naming conventions** from existing codebase
7. **Add proper unit tests** following existing test patterns

---

## Testing Strategy

1. **Controller Tests**: Test HTTP endpoints with MockMvc
2. **Service Tests**: Test business logic with mocked repositories  
3. **Integration Tests**: Test with real database using @SpringBootTest
4. **API Tests**: Update Bruno collection in `mdm-api-tests/`

---

## Migration Strategy

1. **Phase 1**: Implement critical APIs (active-assembly, user-profile, sessions)
2. **Phase 2**: Enhance existing APIs (assemblies, designations)
3. **Phase 3**: Implement remaining APIs (calendar, private-member, places)
4. **Phase 4**: Frontend mock removal and testing

Each phase should be fully tested before moving to the next phase.