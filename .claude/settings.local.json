{"permissions": {"allow": ["Bash(rg:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"navigate\\([''\"\"]/\" --type js -A 1 -B 1)", "Bash(grep:*)", "Bash(pnpm run dev:local:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"from [''\"\"]@/utils\" src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"import.*,\\s*}\\s*from\" src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"formatDateString\" src/utils/)", "Bash(pnpm run lint:*)", "Bash(pnpm test:*)", "Bash(npx vitest run:*)", "Bash(pnpm run format:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"useContextualNavigation\" src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"navigate\\]\" src/ --type js)", "Bash(node:*)", "Bash(rm:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg *)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n 'navigate\\(`' src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n 'useNavigate' src/pages/notice-for-question/my-notice.jsx)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n 'DOCUMENT_ROUTES' src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n 'ROUTE_REPLACEMENTS' src/)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n 'question-edit-list' src/app/router.jsx)", "Bash(pnpm run:*)"], "deny": []}}