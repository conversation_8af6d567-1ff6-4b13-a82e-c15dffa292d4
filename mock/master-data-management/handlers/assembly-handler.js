import { http, HttpResponse } from "msw";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

// Mock for session and assembly data (adjust as needed)
export const assemblyList = Array.from({ length: 15 }, (_, i) => ({
  id: 15 - i,
  title: `${15 - i}`,
}));

export const sessionList = Array.from({ length: 15 }, (_, i) => ({
  id: 15 - i,
  title: `${15 - i}`,
}));

const assemblyListhandler = [
  // Handler for fetching all assemblies
  http.get(`${VITE_MDM_SERVICE_BASE_URL}assembly`, () => {
    return HttpResponse.json(assemblyList, { status: 200 });
  }),

  // Handler for fetching all sessions
  http.get(`${VITE_MDM_SERVICE_BASE_URL}session`, () => {
    return HttpResponse.json(sessionList, { status: 200 });
  }),
];

export { assemblyListhandler };
