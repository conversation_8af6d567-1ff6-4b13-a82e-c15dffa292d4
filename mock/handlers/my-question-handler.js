import { mockQuestions } from "../db/my-questions-db-model";
import { http, HttpResponse } from "msw";
const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;
export const myQuestionsHandlers = [
  // List API
  http.get(BASE_URL + "api/documents-questions/", ({ request }) => {
    const url = new URL(request.url);
    const limit = url.searchParams.get("limit");
    const data = limit ? mockQuestions.slice(0, Number(limit)) : mockQuestions;
    return new HttpResponse(JSON.stringify(data), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }),

  // Detail API
  http.get(BASE_URL + "api/documents-questions/:id", ({ params }) => {
    const { id } = params;
    const question = mockQuestions.find((q) => q.id === id);
    if (!question) {
      return new HttpResponse(JSON.stringify({ error: "Not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }
    return new HttpResponse(JSON.stringify(question), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }),
];
