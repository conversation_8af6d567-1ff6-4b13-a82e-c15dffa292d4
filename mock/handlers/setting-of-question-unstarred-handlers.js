import { http, HttpResponse } from "msw";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const settingOfQuestionsUnstarredActionData = [
  {
    content: [
      {
        id: "ab6a20ab-368d-47ee-a3cd-3d1849cf6348",
        questionDate: "29/09/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Suresh Menon",
            displayNameInLocale: "സുരേഷ് മേനോൻ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "<PERSON><PERSON> Thomas",
            displayNameInLocale: "ആയിഷ തോമസ്",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "COMPLETED",
      },
      {
        id: "9e0587aa-713e-4416-9086-8857b1245d37",
        questionDate: "21/05/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "<PERSON><PERSON> Menon",
            displayNameInLocale: "സുരേഷ് മേനോൻ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "<PERSON><PERSON><PERSON>",
            displayNameInLocale: "രാജീവ് കുമാർ",
            role: "Q&A Assistant 3",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "4d2f9019-9a8c-4b20-a8ae-345e8b26a521",
        questionDate: "21/12/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Ayesha Thomas",
            displayNameInLocale: "ആയിഷ തോമസ്",
            role: "Q&A Assistant 2",
          },
          {
            displayName: "Vijayakrishnan Nair",
            displayNameInLocale: "വിജയകൃഷ്ണൻ നായർ",
            role: "Q&A Assistant 3",
          },
        ],
        commentsPending: "Yes",
        status: "COMPLETED",
      },
      {
        id: "7f8d208f-0c92-4552-9012-6e87411b1c3c",
        questionDate: "27/06/2021",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Suresh Menon",
            displayNameInLocale: "സുരേഷ് മേനോൻ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Priya Nair",
            displayNameInLocale: "പ്രിയ നായർ",
            role: "Q&A Assistant 3",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "3a4b5c6d-7e8f-9g0h-1i2j-3k4l5m6n7o8p",
        questionDate: "15/03/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Rahul Sharma",
            displayNameInLocale: "രാഹുൽ ശർമ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Meera Patel",
            displayNameInLocale: "മീര പട്ടേൽ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "COMPLETED",
      },
      {
        id: "9q8w7e6r5t4y3u2i1o0p",
        questionDate: "08/11/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Ananya Singh",
            displayNameInLocale: "അനന്യ സിംഗ്",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Arjun Menon",
            displayNameInLocale: "അർജുൻ മേനോൻ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "1z2x3c4v5b6n7m8l9k0j",
        questionDate: "30/01/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Neha Gupta",
            displayNameInLocale: "നേഹ ഗുപ്ത",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Vikram Joshi",
            displayNameInLocale: "വിക്രം ജോഷി",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "0p9o8i7u6y5t4r3e2w1q",
        questionDate: "14/07/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Priyanka Reddy",
            displayNameInLocale: "പ്രിയങ്ക റെഡ്ഡി",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Amit Khanna",
            displayNameInLocale: "അമിത് ഖന്ന",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "COMPLETED",
      },
      {
        id: "2w3e4r5t6y7u8i9o0p",
        questionDate: "05/04/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Deepak Verma",
            displayNameInLocale: "ദീപക് വർമ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Shweta Iyer",
            displayNameInLocale: "ശ്വേത ഐയ്യർ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "1a2s3d4f5g6h7j8k9l0",
        questionDate: "19/08/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Rohan Mathew",
            displayNameInLocale: "റോഹൻ മാത്യു",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Anjali Nair",
            displayNameInLocale: "അഞ്ജലി നായർ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "5k6l7m8n9b0v1c2x3z4",
        questionDate: "22/02/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Sanjay Pillai",
            displayNameInLocale: "സഞ്ജയ് പിള്ള",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Divya Menon",
            displayNameInLocale: "ദിവ്യ മേനോൻ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "COMPLETED",
      },
      {
        id: "9m8n7b6v5c4x3z2a1s",
        questionDate: "11/10/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Karthik Nambiar",
            displayNameInLocale: "കാർത്തിക് നമ്പ്യാർ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Swetha Pillai",
            displayNameInLocale: "ശ്വേത പിള്ള",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "4d5f6g7h8j9k0l1q2w",
        questionDate: "03/05/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Vishnu Prasad",
            displayNameInLocale: "വിഷ്ണു പ്രസാദ്",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Anita Das",
            displayNameInLocale: "അനിത ദാസ്",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "3e4r5t6y7u8i9o0p1a",
        questionDate: "25/12/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Manoj Kumar",
            displayNameInLocale: "മനോജ് കുമാർ",
            role: "Q&A Assistant 1",
          },
          {
            displayName: "Sheela Thomas",
            displayNameInLocale: "ശീല തോമസ്",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "COMPLETED",
      },
      {
        id: "2s3d4f5g6h7j8k9l0q",
        questionDate: "17/09/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Gopalakrishnan Iyer",
            displayNameInLocale: "ഗോപാലകൃഷ്ണൻ ഐയ്യർ",
            role: "Q&A Assistant 1",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "6f7g8h9i0j1k2l3m4n5",
        questionDate: "10/01/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Lakshmi Nair",
            displayNameInLocale: "ലക്ഷ്മി നായർ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "7o8p9q0r1s2t3u4v5w6",
        questionDate: "05/07/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Arun Kumar",
            displayNameInLocale: "അരുൺ കുമാർ",
            role: "Q&A Assistant 1",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "8x9y0z1a2b3c4d5e6f7",
        questionDate: "18/03/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Divya Pillai",
            displayNameInLocale: "ദിവ്യ പിള്ള",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "9g0h1i2j3k4l5m6n7o8",
        questionDate: "22/11/2023",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Rajesh Nair",
            displayNameInLocale: "രാജേഷ് നായർ",
            role: "Q&A Assistant 1",
          },
        ],
        commentsPending: "No",
        status: "ACTION_TO_BE_TAKEN",
      },
      {
        id: "0p1q2r3s4t5u6v7w8x9",
        questionDate: "14/02/2024",
        category: "Unstarred",
        commentsSubmittedBy: [
          {
            displayName: "Sunita Menon",
            displayNameInLocale: "സുനിത മേനോൻ",
            role: "Q&A Assistant 2",
          },
        ],
        commentsPending: "Yes",
        status: "COMPLETED",
      },
    ],
  },
];

const settingOfQuestionsUnstarredHandlers = [
  http.get(BASE_URL + "api/setting-of-questions-unstarred", ({ request }) => {
    const url = new URL(request.url);

    // Pagination parameters
    let page = parseInt(url.searchParams.get("page") || "1", 10);
    if (page === 0) page = 1;

    let pageSize = parseInt(url.searchParams.get("size") || "10", 10);
    if (![10, 50, 100].includes(pageSize)) {
      pageSize = 10;
    }

    // Filter parameters
    const searchTerm = url.searchParams.get("search") || "";
    const category = url.searchParams.get("category") || "";
    const commentsPending = url.searchParams.get("commentsPending") || "";
    const status = url.searchParams.get("status") || "";

    // Date range filtering
    const questionDateStartDate =
      url.searchParams.get("questionDateStartDate") || "";
    const questionDateEndDate =
      url.searchParams.get("questionDateEndDate") || "";

    let allDocuments = settingOfQuestionsUnstarredActionData[0]?.content || [];

    // Apply filters
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      allDocuments = allDocuments.filter(
        (doc) =>
          doc.questionDate?.toLowerCase().includes(searchLower) ||
          doc.category?.toLowerCase().includes(searchLower) ||
          doc.commentsSubmittedBy?.some(
            (person) =>
              person.displayName.toLowerCase().includes(searchLower) ||
              person.displayNameInLocale.toLowerCase().includes(searchLower) ||
              person.role.toLowerCase().includes(searchLower)
          ) ||
          doc.status?.toLowerCase().includes(searchLower)
      );
    }

    if (category) {
      const categories = category.split(",");
      allDocuments = allDocuments.filter((doc) =>
        categories.includes(doc.category)
      );
    }

    if (commentsPending) {
      allDocuments = allDocuments.filter(
        (doc) => doc.commentsPending === commentsPending
      );
    }

    if (status) {
      const statuses = status.split(",");
      allDocuments = allDocuments.filter((doc) =>
        statuses.includes(doc.status)
      );
    }

    const isDateInRange = (dateStr, startDate, endDate) => {
      if (!dateStr) return false;
      if (!startDate && !endDate) return true;

      const parts = dateStr.split("/");
      if (parts.length !== 3) return false;

      const month = parseInt(parts[0]) - 1;
      const day = parseInt(parts[1]);
      let year = parseInt(parts[2]);

      if (year < 100) {
        year += year < 50 ? 2000 : 1900;
      }

      const date = new Date(year, month, day);

      let isAfterStart = true;
      let isBeforeEnd = true;

      if (startDate) {
        const [startYear, startMonth, startDay] = startDate
          .split("-")
          .map(Number);
        const start = new Date(startYear, startMonth - 1, startDay);
        isAfterStart = date >= start;
      }

      if (endDate) {
        const [endYear, endMonth, endDay] = endDate.split("-").map(Number);
        const end = new Date(endYear, endMonth - 1, endDay);
        isBeforeEnd = date <= end;
      }

      return isAfterStart && isBeforeEnd;
    };

    if (questionDateStartDate || questionDateEndDate) {
      allDocuments = allDocuments.filter((doc) =>
        isDateInRange(
          doc.questionDate,
          questionDateStartDate,
          questionDateEndDate
        )
      );
    }

    // Pagination logic
    const totalElements = allDocuments.length;
    const totalPages = Math.ceil(totalElements / pageSize);
    const startIndex = (page - 1) * pageSize;
    const paginatedDocuments = allDocuments.slice(
      startIndex,
      startIndex + pageSize
    );

    return HttpResponse.json({
      content: paginatedDocuments,
      page: page,
      size: pageSize,
      totalElements: totalElements,
      totalPages: totalPages,
      first: page === 1,
      last: page >= totalPages,
    });
  }),
];

export { settingOfQuestionsUnstarredHandlers };
