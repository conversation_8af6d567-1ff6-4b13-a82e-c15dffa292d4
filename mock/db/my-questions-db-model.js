export const mockQuestions = [
  {
    id: "10001",
    assembly: "15",
    session: "12",
    questionNumber: "Q123",
    heading: "Notice 20 December 2024",
    designation: "Chief Minister",
    portfolio: "Lorem Ipsum",
    date: "2024-12-20",
    status: "APPROVED",
    clubbed: true,
    priority: "P1",
    category: "Starred",
    starred: true,
    clauses: [
      {
        id: "6e2c3793-96d5-4d03-bc80-b031ee5d2d26",
        content: "അറിയിപ്പ് സമയത്തിൽ സമർപ്പിച്ചു.",
        order: 1,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "മന്ത്രിയുടെ വിശദീകരണം ഉൾപ്പെടുന്നു.",
        order: 2,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "അസംബ്ലിയിലെ недവശ്യചർച്ചയുമായി ബന്ധപ്പെട്ടിരിക്കുന്നു.",
        order: 3,
      },
    ],
    primaryMember: {
      memberId: 1,
      memberDisplayName: "Shri A B",
      memberDisplayNameInLocal: "ശ്രീ എ ബി",
      constituencyId: 101,
      constituencyName: "Trivandrum",
      constituencyNumber: "120",
      politicalPartyId: 10,
      constituencyNameInLocal: "തിരുവനന്തപുരം",
      politicalPartyName: "Democratic Party",
      politicalPartyNameInLocal: "ജനാധിപത്യ പാർട്ടി",
    },
    secondaryMembers: [
      {
        memberId: 2,
        memberDisplayName: "Shri B A",
        memberDisplayNameInLocal: "ശ്രീ എ ബി",
        constituencyId: 101,
        constituencyName: "Trivandrum",
        constituencyNumber: "120",
        politicalPartyId: 10,
        constituencyNameInLocal: "തിരുവനന്തപുരം",
        politicalPartyName: "Democratic Party",
        politicalPartyNameInLocal: "ജനാധിപത്യ പാർട്ടി",
      },
    ],
  },
  {
    id: "10002",
    assembly: "15",
    session: "14",
    questionNumber: "Q123",
    heading: "Notice 15 November 2024",
    designation: "Minister of Health",
    portfolio: "Health",
    date: "2024-11-15",
    status: "APPROVED",
    priority: "P1",
    clubbed: false,
    category: "Unstarred",
    starred: false,
    clauses: [
      {
        id: "6e2c3793-96d5-4d03-bc80-b031ee5d2d26",
        content:
          "ആശുപത്രിയുടെ അടിസ്ഥാന സൗകര്യങ്ങൾ സംബന്ധിച്ച റിപ്പോർട്ട് ആവശ്യപ്പെട്ടു.",
        order: 1,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "ജില്ലാതല വിശകലനത്തിൽ ശ്രദ്ധ കേന്ദ്രീകരിക്കുന്നു.",
        order: 2,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "മുൻ സെഷനിലെ ഫോളോ അപ്പ് ഉൾപ്പെടുന്നു.",
        order: 3,
      },
    ],
    primaryMember: {
      memberId: 3,
      memberDisplayName: "Smt C D",
      memberDisplayNameInLocal: "ശ്രീമതി സി ഡി",
      constituencyId: 102,
      constituencyName: "Kollam",
      constituencyNumber: "110",
      politicalPartyId: 20,
      constituencyNameInLocal: "കൊല്ലം",
      politicalPartyName: "People's Front",
      politicalPartyNameInLocal: "ജനതാ മുന്നണി",
    },
    secondaryMembers: [
      {
        memberId: 4,
        memberDisplayName: "Smt C D",
        memberDisplayNameInLocal: "ശ്രീമതി സി ഡി",
        constituencyId: 102,
        constituencyName: "Kollam",
        constituencyNumber: "110",
        politicalPartyId: 20,
        constituencyNameInLocal: "കൊല്ലം",
        politicalPartyName: "People's Front",
        politicalPartyNameInLocal: "ജനതാ മുന്നണി",
      },
    ],
  },
  {
    id: "10003",
    heading: "Bridge Construction in XYZ Area",
    designation: "Shri X Y MLA",
    portfolio: "Public Works",
    date: "2024-10-10",
    status: "APPROVED",
    session: "13",
    assembly: "15",
    noticeNo: "12345",
    type: "Question",
    priority: "P1",
    members: ["Shri X Y", "Smt Z"],
    starred: true,
    category: "Starred",
    clauses: [
      {
        id: "6e2c3793-96d5-4d03-bc80-b031ee5d2d26",
        content: "നിർമാണം തുടങ്ങുന്ന തീയതി ആവശ്യപ്പെട്ടിരിക്കുന്നു.",
        order: 1,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "ബജറ്റ് അനുവദനത്തിൽ സംശയം ഉന്നയിച്ചു.",
        order: 2,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "പ്രാദേശിക ഗതാഗതത്തിൽ പ്രതിഫലം ഉണ്ടോ എന്നത് പരിശോധിക്കുക.",
        order: 3,
      },
    ],
    primaryMember: {
      memberId: 5,
      memberDisplayName: "Shri X Y",
      memberDisplayNameInLocal: "ശ്രീ എക്സ് വൈ",
      constituencyId: 103,
      constituencyName: "Ernakulam",
      constituencyNumber: "130",
      politicalPartyId: 30,
      constituencyNameInLocal: "എറണാകുളം",
      politicalPartyName: "Secular Congress",
      politicalPartyNameInLocal: "സെക്യുലർ കോൺഗ്രസ്",
    },
    secondaryMembers: [
      {
        memberId: 6,
        memberDisplayName: "Shri X Y",
        memberDisplayNameInLocal: "ശ്രീ എക്സ് വൈ",
        constituencyId: 103,
        constituencyName: "Ernakulam",
        constituencyNumber: "130",
        politicalPartyId: 30,
        constituencyNameInLocal: "എറണാകുളം",
        politicalPartyName: "Secular Congress",
        politicalPartyNameInLocal: "സെക്യുലർ കോൺഗ്രസ്",
      },
    ],
  },
  {
    id: "10004",
    session: "13",
    assembly: "15",
    heading: "Water Scarcity in District A",
    designation: "Minister of Water",
    portfolio: "Water Resources",
    date: "2024-09-05",
    status: "APPROVED",
    clubbed: true,
    priority: "P1",
    category: "Starred",
    starred: true,
    clauses: [
      {
        id: "6e2c3793-96d5-4d03-bc80-b031ee5d2d26",
        content: "നിലവിലെ ഭൂഗർഭജല നിരപ്പ് പരിശോധിക്കുക.",
        order: 1,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "മഴവെള്ള ശേഖരണം എത്രമാത്രം പ്രാവർത്തികമാക്കി.",
        order: 2,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "ചൂടുകാലത്തിനുള്ള മുൻകരുതലുകൾ എന്തൊക്കെയാണ്.",
        order: 3,
      },
    ],
    primaryMember: {
      memberId: 7,
      memberDisplayName: "Shri M N",
      memberDisplayNameInLocal: "ശ്രീ എം എൻ",
      constituencyId: 104,
      constituencyName: "Palakkad",
      constituencyNumber: "140",
      politicalPartyId: 40,
      constituencyNameInLocal: "പാലക്കാട്",
      politicalPartyName: "Green Alliance",
      politicalPartyNameInLocal: "ഹരിത ഐക്യം",
    },
    secondaryMembers: [
      {
        memberId: 8,
        memberDisplayName: "Shri M N",
        memberDisplayNameInLocal: "ശ്രീ എം എൻ",
        constituencyId: 104,
        constituencyName: "Palakkad",
        constituencyNumber: "140",
        politicalPartyId: 40,
        constituencyNameInLocal: "പാലക്കാട്",
        politicalPartyName: "Green Alliance",
        politicalPartyNameInLocal: "ഹരിത ഐക്യം",
      },
    ],
  },
  {
    id: "10005",
    session: "13",
    assembly: "15",
    heading: "Road Development Scheme",
    designation: "Minister of Roads",
    portfolio: "Transport",
    date: "2024-08-25",
    status: "APPROVED",
    clubbed: false,
    priority: "P1",
    category: "Unstarred",
    starred: false,
    clauses: [
      {
        id: "6e2c3793-96d5-4d03-bc80-b031ee5d2d26",
        content: "ഗ്രാമ റോഡ് ബന്ധം സംബന്ധിച്ച വിവരങ്ങൾ ആവശ്യപ്പെട്ടു.",
        order: 1,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "ബജറ്റ് പദ്ധതി നൽകുന്നതിൽ വൈകിയിട്ടുണ്ട്.",
        order: 2,
      },
      {
        id: "e5e77247-207e-496d-8b27-e1255bcf2c25",
        content: "പാത വികസനത്തിന്റെ സമഗ്ര അവലോകനം ഉൾപ്പെടുത്തി.",
        order: 3,
      },
    ],
    primaryMember: {
      memberId: 9,
      memberDisplayName: "Smt P Q",
      memberDisplayNameInLocal: "ശ്രീമതി പി ക്യു",
      constituencyId: 105,
      constituencyName: "Alappuzha",
      constituencyNumber: "150",
      politicalPartyId: 50,
      constituencyNameInLocal: "ആലപ്പുഴ",
      politicalPartyName: "Progressive Party",
      politicalPartyNameInLocal: "പ്രോഗ്രസീവ് പാർട്ടി",
    },
    secondaryMembers: [
      {
        memberId: 10,
        memberDisplayName: "Smt P Q",
        memberDisplayNameInLocal: "ശ്രീമതി പി ക്യു",
        constituencyId: 105,
        constituencyName: "Alappuzha",
        constituencyNumber: "150",
        politicalPartyId: 50,
        constituencyNameInLocal: "ആലപ്പുഴ",
        politicalPartyName: "Progressive Party",
        politicalPartyNameInLocal: "പ്രോഗ്രസീവ് പാർട്ടി",
      },
    ],
  },
];
